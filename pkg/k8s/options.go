package k8s

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// NamespacesListOptions 命名空间列表选项
func NamespacesListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// PodsListOptions Pod列表选项
func PodsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// DeploymentsListOptions Deployment列表选项
func DeploymentsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// ServicesListOptions Service列表选项
func ServicesListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// ConfigMapsListOptions ConfigMap列表选项
func ConfigMapsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// SecretsListOptions Secret列表选项
func SecretsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// PersistentVolumesListOptions PersistentVolume列表选项
func PersistentVolumesListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// PersistentVolumeClaimsListOptions PersistentVolumeClaim列表选项
func PersistentVolumeClaimsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// StorageClassesListOptions StorageClass列表选项
func StorageClassesListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// JobsListOptions Job列表选项
func JobsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// CronJobsListOptions CronJob列表选项
func CronJobsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// IngressesListOptions Ingress列表选项
func IngressesListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// StatefulSetsListOptions StatefulSet列表选项
func StatefulSetsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// DaemonSetsListOptions DaemonSet列表选项
func DaemonSetsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// ReplicaSetsListOptions ReplicaSet列表选项
func ReplicaSetsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// EventsListOptions Event列表选项
func EventsListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// NodeListOptions Node列表选项
func NodeListOptions() metav1.ListOptions {
	return metav1.ListOptions{}
}

// DefaultDeleteOptions 默认删除选项
func DefaultDeleteOptions() metav1.DeleteOptions {
	return metav1.DeleteOptions{}
}

// GracePeriodDeleteOptions 优雅终止删除选项
func GracePeriodDeleteOptions(seconds int64) metav1.DeleteOptions {
	return metav1.DeleteOptions{
		GracePeriodSeconds: &seconds,
	}
}

// ForceDeleteOptions 强制删除选项
func ForceDeleteOptions() metav1.DeleteOptions {
	gracePeriod := int64(0)
	// Force字段在新版本的k8s.io/apimachinery中已不存在
	return metav1.DeleteOptions{
		GracePeriodSeconds: &gracePeriod,
	}
}
