package k8s

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// K8sClient Kubernetes客户端封装
type K8sClient interface {
	// GetClient 获取Kubernetes客户端
	GetClient() kubernetes.Interface
	// GetConfig 获取Kubernetes配置
	GetConfig() *rest.Config
	// Close 关闭客户端
	Close() error
}

// 管理多集群客户端
type ClusterManager struct {
	clients map[string]*clusterClient
	mu      sync.RWMutex
}

// 单集群客户端实现
type clusterClient struct {
	client   kubernetes.Interface
	config   *rest.Config
	name     string
	kubeConf string
}

// NewClusterManager 创建集群管理器
func NewClusterManager() *ClusterManager {
	return &ClusterManager{
		clients: make(map[string]*clusterClient),
	}
}

// AddCluster 添加集群
func (m *ClusterManager) AddCluster(name, kubeConfigPath string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; exists {
		return fmt.Errorf("集群 %s 已存在", name)
	}

	client, err := newClusterClient(name, kubeConfigPath)
	if err != nil {
		return fmt.Errorf("创建集群客户端失败: %w", err)
	}

	m.clients[name] = client
	return nil
}

// GetCluster 获取集群客户端
func (m *ClusterManager) GetCluster(name string) (K8sClient, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, ok := m.clients[name]
	if !ok {
		return nil, fmt.Errorf("集群 %s 不存在", name)
	}

	return client, nil
}

// newClusterClientFromConfig 从配置字符串创建集群客户端
func newClusterClientFromConfig(name, kubeconfigData string) (*clusterClient, error) {
	// 解析kubeconfig
	config, err := clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfigData))
	if err != nil {
		return nil, fmt.Errorf("解析kubeconfig失败: %w", err)
	}

	// 创建客户端
	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建kubernetes客户端失败: %w", err)
	}

	return &clusterClient{
		client:   client,
		config:   config,
		name:     name,
		kubeConf: kubeconfigData,
	}, nil
}

// RemoveCluster 移除集群
func (m *ClusterManager) RemoveCluster(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	client, ok := m.clients[name]
	if !ok {
		return fmt.Errorf("集群 %s 不存在", name)
	}

	if err := client.Close(); err != nil {
		return err
	}

	delete(m.clients, name)
	return nil
}

// ListClusters 列出所有集群
func (m *ClusterManager) ListClusters() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	clusters := make([]string, 0, len(m.clients))
	for name := range m.clients {
		clusters = append(clusters, name)
	}

	return clusters
}

// AddClusterFromConfig 从配置字符串添加集群
func (m *ClusterManager) AddClusterFromConfig(name, kubeconfigData string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; exists {
		return fmt.Errorf("集群 %s 已存在", name)
	}

	client, err := newClusterClientFromConfig(name, kubeconfigData)
	if err != nil {
		return fmt.Errorf("创建集群客户端失败: %w", err)
	}

	m.clients[name] = client
	return nil
}

// NewClientFromConfig 从配置创建K8s客户端
func NewClientFromConfig(config *rest.Config) (K8sClient, error) {
	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, err
	}

	return &clusterClient{
		client: client,
		config: config,
		name:   "temp",
	}, nil
}

// Close 关闭所有集群连接
func (m *ClusterManager) Close() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	var errs []error
	for name, client := range m.clients {
		if err := client.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭集群 %s 连接失败: %w", name, err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("%v", errs)
	}

	return nil
}

// newClusterClient 创建集群客户端
func newClusterClient(name, kubeConfigPath string) (*clusterClient, error) {
	var config *rest.Config
	var err error

	// 如果kubeConfigPath为空，尝试使用集群内部配置
	if kubeConfigPath == "" {
		config, err = rest.InClusterConfig()
		if err != nil {
			// 如果集群内部配置获取失败，尝试使用默认kubeconfig路径
			home := homedir.HomeDir()
			if home != "" {
				defaultKubeConfig := filepath.Join(home, ".kube", "config")
				if _, err := os.Stat(defaultKubeConfig); err == nil {
					kubeConfigPath = defaultKubeConfig
				} else {
					return nil, fmt.Errorf("无法获取集群配置: %w", err)
				}
			} else {
				return nil, errors.New("无法获取集群配置")
			}
		}
	}

	// 如果kubeConfigPath不为空，使用文件配置
	if kubeConfigPath != "" {
		config, err = clientcmd.BuildConfigFromFlags("", kubeConfigPath)
		if err != nil {
			return nil, fmt.Errorf("加载kubeconfig失败: %w", err)
		}
	}

	if config == nil {
		return nil, errors.New("无法加载Kubernetes配置")
	}

	// 创建客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %w", err)
	}

	return &clusterClient{
		client:   clientset,
		config:   config,
		name:     name,
		kubeConf: kubeConfigPath,
	}, nil
}

// GetClient 获取Kubernetes客户端
func (c *clusterClient) GetClient() kubernetes.Interface {
	return c.client
}

// GetConfig 获取Kubernetes配置
func (c *clusterClient) GetConfig() *rest.Config {
	return c.config
}

// Close 关闭客户端
func (c *clusterClient) Close() error {
	return nil
}

// GetNamespaces 获取命名空间列表
func GetNamespaces(ctx context.Context, client kubernetes.Interface) ([]string, error) {
	namespaceList, err := client.CoreV1().Namespaces().List(ctx, NamespacesListOptions())
	if err != nil {
		return nil, fmt.Errorf("获取命名空间列表失败: %w", err)
	}

	namespaces := make([]string, len(namespaceList.Items))
	for i, ns := range namespaceList.Items {
		namespaces[i] = ns.Name
	}

	return namespaces, nil
}
