package k8s

import (
	"context"
	"fmt"

	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// RBACService K8s RBAC服务接口
type RBACService interface {
	// 创建ClusterRole
	CreateClusterRole(ctx context.Context, clusterName string, roleName string, rules []rbacv1.PolicyRule) error

	// 创建ClusterRoleBinding
	CreateClusterRoleBinding(ctx context.Context, clusterName string, bindingName string, roleName string, subjects []rbacv1.Subject) error

	// 创建用户的K8s权限
	CreateUserK8sPermission(ctx context.Context, clusterName string, username string, resources []string, verbs []string) error

	// 删除用户的K8s权限
	DeleteUserK8sPermission(ctx context.Context, clusterName string, username string) error
}

// rbacService K8s RBAC服务实现
type rbacService struct {
	clusterManager *ClusterManager
}

// NewRBACService 创建K8s RBAC服务
func NewRBACService(clusterManager *ClusterManager) RBACService {
	return &rbacService{
		clusterManager: clusterManager,
	}
}

// CreateClusterRole 创建ClusterRole
func (s *rbacService) CreateClusterRole(ctx context.Context, clusterName string, roleName string, rules []rbacv1.PolicyRule) error {
	// 获取集群客户端
	k8sClient, err := s.getK8sClient(clusterName)
	if err != nil {
		return err
	}

	// 创建ClusterRole
	role := &rbacv1.ClusterRole{
		ObjectMeta: metav1.ObjectMeta{
			Name: roleName,
			Labels: map[string]string{
				"app.kubernetes.io/managed-by": "kubeops",
			},
		},
		Rules: rules,
	}

	// 检查是否已存在
	existingRole, err := k8sClient.RbacV1().ClusterRoles().Get(ctx, roleName, metav1.GetOptions{})
	if err == nil && existingRole != nil {
		// 更新已存在的ClusterRole
		existingRole.Rules = rules
		_, err = k8sClient.RbacV1().ClusterRoles().Update(ctx, existingRole, metav1.UpdateOptions{})
		return err
	}

	// 创建新的ClusterRole
	_, err = k8sClient.RbacV1().ClusterRoles().Create(ctx, role, metav1.CreateOptions{})
	return err
}

// CreateClusterRoleBinding 创建ClusterRoleBinding
func (s *rbacService) CreateClusterRoleBinding(ctx context.Context, clusterName string, bindingName string, roleName string, subjects []rbacv1.Subject) error {
	// 获取集群客户端
	k8sClient, err := s.getK8sClient(clusterName)
	if err != nil {
		return err
	}

	// 创建ClusterRoleBinding
	binding := &rbacv1.ClusterRoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name: bindingName,
			Labels: map[string]string{
				"app.kubernetes.io/managed-by": "kubeops",
			},
		},
		RoleRef: rbacv1.RoleRef{
			APIGroup: "rbac.authorization.k8s.io",
			Kind:     "ClusterRole",
			Name:     roleName,
		},
		Subjects: subjects,
	}

	// 检查是否已存在
	existingBinding, err := k8sClient.RbacV1().ClusterRoleBindings().Get(ctx, bindingName, metav1.GetOptions{})
	if err == nil && existingBinding != nil {
		// 更新已存在的ClusterRoleBinding
		existingBinding.Subjects = subjects
		_, err = k8sClient.RbacV1().ClusterRoleBindings().Update(ctx, existingBinding, metav1.UpdateOptions{})
		return err
	}

	// 创建新的ClusterRoleBinding
	_, err = k8sClient.RbacV1().ClusterRoleBindings().Create(ctx, binding, metav1.CreateOptions{})
	return err
}

// CreateUserK8sPermission 创建用户的K8s权限
func (s *rbacService) CreateUserK8sPermission(ctx context.Context, clusterName string, username string, resources []string, verbs []string) error {
	// 生成角色名称和绑定名称
	roleName := fmt.Sprintf("kubeops-%s-role", username)
	bindingName := fmt.Sprintf("kubeops-%s-binding", username)

	// 创建规则
	rules := []rbacv1.PolicyRule{
		{
			APIGroups: []string{""},
			Resources: resources,
			Verbs:     verbs,
		},
	}

	// 创建ClusterRole
	if err := s.CreateClusterRole(ctx, clusterName, roleName, rules); err != nil {
		return fmt.Errorf("创建ClusterRole失败: %w", err)
	}

	// 创建Subject
	subjects := []rbacv1.Subject{
		{
			Kind:     "User",
			APIGroup: "rbac.authorization.k8s.io",
			Name:     username,
		},
	}

	// 创建ClusterRoleBinding
	if err := s.CreateClusterRoleBinding(ctx, clusterName, bindingName, roleName, subjects); err != nil {
		return fmt.Errorf("创建ClusterRoleBinding失败: %w", err)
	}

	return nil
}

// DeleteUserK8sPermission 删除用户的K8s权限
func (s *rbacService) DeleteUserK8sPermission(ctx context.Context, clusterName string, username string) error {
	// 获取集群客户端
	k8sClient, err := s.getK8sClient(clusterName)
	if err != nil {
		return err
	}

	// 生成角色名称和绑定名称
	roleName := fmt.Sprintf("kubeops-%s-role", username)
	bindingName := fmt.Sprintf("kubeops-%s-binding", username)

	// 删除ClusterRoleBinding
	if err := k8sClient.RbacV1().ClusterRoleBindings().Delete(ctx, bindingName, metav1.DeleteOptions{}); err != nil {
		// 忽略NotFound错误
		// 继续尝试删除ClusterRole
	}

	// 删除ClusterRole
	if err := k8sClient.RbacV1().ClusterRoles().Delete(ctx, roleName, metav1.DeleteOptions{}); err != nil {
		// 忽略NotFound错误
	}

	return nil
}

// getK8sClient 获取K8s客户端
func (s *rbacService) getK8sClient(clusterName string) (kubernetes.Interface, error) {
	// 获取集群客户端
	client, err := s.clusterManager.GetCluster(clusterName)
	if err != nil {
		return nil, fmt.Errorf("获取集群客户端失败: %w", err)
	}

	return client.GetClient(), nil
}
