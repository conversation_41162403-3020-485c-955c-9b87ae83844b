package lark

import larkpkg "github.com/larksuite/oapi-sdk-go/v3"

// NewAuthService 创建飞书认证服务
func NewAuthService(config *FeishuConfig) (AuthService, error) {
	return &authServiceStub{
		config: config,
	}, nil
}

// NewFeishuService 创建飞书服务
func NewFeishuService(config *FeishuConfig) FeishuService {
	client := larkpkg.NewClient(config.AppID, config.AppSecret)
	return &feishuService{
		config: config,
		client: client,
	}
}

// GetTenantAccessToken 获取租户访问令牌
func (s *feishuService) GetTenantAccessToken() (*FeishuTenantToken, error) {
	// 直接使用配置中的应用ID和密钥
	// 这里简化实现，实际应该调用飞书API获取令牌
	return &FeishuTenantToken{
		AccessToken: "mock_tenant_token",
		ExpiresIn:   7200,
	}, nil
}

// UpdateConfig 更新飞书配置
func (s *feishuService) UpdateConfig(config *FeishuConfig) {
	s.config = config
	s.client = larkpkg.NewClient(config.AppID, config.AppSecret)
}

// GetAuthURL 获取飞书授权URL
func (s *feishuService) GetAuthURL(state string) string {
	if s.config == nil || s.config.AppID == "" {
		return ""
	}
	// 构建飞书授权URL
	return "https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=" +
		s.config.AppID + "&redirect_uri=http://localhost:8080/api/v1/auth/feishu/callback&state=" + state
}

// GetAccessToken 通过授权码获取访问令牌
func (s *feishuService) GetAccessToken(code string) (*FeishuTokenResponse, error) {
	// 简化实现，返回模拟数据
	return &FeishuTokenResponse{
		AccessToken:           "mock_access_token",
		ExpiresIn:             7200,
		RefreshToken:          "mock_refresh_token",
		RefreshTokenExpiresIn: 86400,
		TokenType:             "Bearer",
	}, nil
}

// GetUserInfo 获取用户信息
func (s *feishuService) GetUserInfo(accessToken string) (*FeishuUserInfo, error) {
	// 简化实现，返回模拟数据
	return &FeishuUserInfo{
		Sub:        "mock_sub",
		Name:       "Mock User",
		Picture:    "https://example.com/avatar.png",
		OpenID:     "mock_open_id",
		UnionID:    "mock_union_id",
		Email:      "<EMAIL>",
		EnName:     "Mock",
		Mobile:     "13800138000",
		TenantKey:  "mock_tenant",
		EmployeeID: "emp123",
	}, nil
}

// GetUserDepartments 获取用户部门信息
func (s *feishuService) GetUserDepartments(accessToken string, userID string) ([]FeishuDepartment, error) {
	// 简化实现，返回模拟数据
	return []FeishuDepartment{
		{
			DepartmentID:   "dept_1",
			DepartmentName: "研发部",
			ParentID:       "0",
			LeaderUserID:   "user_1",
			Status:         1,
		},
		{
			DepartmentID:   "dept_2",
			DepartmentName: "产品部",
			ParentID:       "0",
			LeaderUserID:   "user_2",
			Status:         1,
		},
	}, nil
}

// GetUserRoles 获取用户角色信息
func (s *feishuService) GetUserRoles(accessToken string, userID string) ([]FeishuRole, error) {
	// 简化实现，返回模拟数据
	return []FeishuRole{
		{
			RoleID:   "role_1",
			RoleName: "管理员",
			Level:    1,
		},
		{
			RoleID:   "role_2",
			RoleName: "普通用户",
			Level:    2,
		},
	}, nil
}

// AuthService 飞书认证服务接口
type AuthService interface {
	// GetAuthURL 获取飞书授权URL
	GetAuthURL(state string) string

	// GetUserInfo 获取用户信息
	GetUserInfo(accessToken string) (*UserInfo, error)

	// UpdateConfig 更新配置
	UpdateConfig(config *FeishuConfig)
}

// authServiceStub 飞书认证服务的简化实现
type authServiceStub struct {
	config *FeishuConfig
}

// GetAuthURL 获取飞书授权URL
func (s *authServiceStub) GetAuthURL(state string) string {
	if s.config == nil || s.config.AppID == "" {
		return ""
	}

	// 构建飞书授权URL，使用硬编码的回调URL
	return "https://open.feishu.cn/open-apis/authen/v1/authorize?app_id=" +
		s.config.AppID + "&redirect_uri=http://localhost:8080/api/v1/auth/feishu/callback&state=" + state
}

// GetUserInfo 获取用户信息
func (s *authServiceStub) GetUserInfo(accessToken string) (*UserInfo, error) {
	// 简化实现，返回模拟数据
	return &UserInfo{
		OpenID:     "mock_open_id",
		UnionID:    "mock_union_id",
		Name:       "Mock User",
		Email:      "<EMAIL>",
		Mobile:     "13800138000",
		Avatar:     "https://example.com/avatar.png",
		EmployeeID: "emp123",
	}, nil
}

// UpdateConfig 更新配置
func (s *authServiceStub) UpdateConfig(config *FeishuConfig) {
	s.config = config
}
