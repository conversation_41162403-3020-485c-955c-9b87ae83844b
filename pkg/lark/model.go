package lark

import (
	"encoding/json"

	lark "github.com/larksuite/oapi-sdk-go/v3"
)

// FeishuConfig 飞书认证配置
type FeishuConfig struct {
	AppID     string
	AppSecret string
}

// FeishuService 飞书认证服务
type FeishuService interface {
	// GetAuthURL 获取飞书授权URL
	GetAuthURL(state string) string

	// GetAccessToken 通过授权码获取访问令牌
	GetAccessToken(code string) (*FeishuTokenResponse, error)

	// GetUserInfo 获取用户信息
	GetUserInfo(accessToken string) (*FeishuUserInfo, error)

	// GetUserDepartments 获取用户部门信息
	GetUserDepartments(accessToken string, userID string) ([]FeishuDepartment, error)

	// GetUserRoles 获取用户角色信息
	GetUserRoles(accessToken string, userID string) ([]FeishuRole, error)

	// GetTenantAccessToken 获取租户访问令牌
	GetTenantAccessToken() (*FeishuTenantToken, error)

	// UpdateConfig 更新飞书配置
	UpdateConfig(config *FeishuConfig)
}

// feishuService 飞书认证服务实现
type feishuService struct {
	config *FeishuConfig
	client *lark.Client
}

// FeishuTokenResponse 飞书访问令牌响应
type FeishuTokenResponse struct {
	Code                  int    `json:"code"`
	Error                 string `json:"error"`             // 错误时才有
	ErrorDescription      string `json:"error_description"` // 错误时才有
	AccessToken           string `json:"access_token"`      // 成功时才有
	ExpiresIn             int    `json:"expires_in"`        // 成功时才有
	RefreshToken          string `json:"refresh_token"`     // 成功时才有
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	Scope                 string `json:"scope"`
	TokenType             string `json:"token_type"`
}

// FeishuTenantToken 飞书租户令牌
type FeishuTenantToken struct {
	AccessToken string `json:"tenant_access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

// FeishuUserInfo 飞书用户信息
type FeishuUserInfo struct {
	Sub        string `json:"sub"`
	Name       string `json:"name"`
	Picture    string `json:"picture"`
	OpenID     string `json:"open_id"`
	UnionID    string `json:"union_id"`
	Email      string `json:"email"`
	EnName     string `json:"en_name"`
	Mobile     string `json:"mobile"`
	TenantKey  string `json:"tenant_key"`
	EmployeeID string `json:"employee_id"`
}

// UserInfo 飞书用户信息
type UserInfo struct {
	OpenID     string `json:"open_id"`
	UnionID    string `json:"union_id"`
	Name       string `json:"name"`
	Email      string `json:"email"`
	Mobile     string `json:"mobile"`
	Avatar     string `json:"avatar"`
	EmployeeID string `json:"employee_id"`
}

// FeishuDepartment 飞书部门信息
type FeishuDepartment struct {
	DepartmentID   string `json:"department_id"`
	DepartmentName string `json:"name"`
	ParentID       string `json:"parent_department_id"`
	LeaderUserID   string `json:"leader_user_id"`
	Status         int    `json:"status"`
}

// FeishuRole 飞书角色信息
type FeishuRole struct {
	RoleID   string `json:"role_id"`
	RoleName string `json:"name"`
	Level    int    `json:"level"`
}

// FeishuTokenRequest 飞书令牌请求
type FeishuTokenRequest struct {
	Client_id     string `json:"client_id" :"client___id"`
	Client_secret string `json:"client_secret" :"client___secret"`
	Redirect_uri  string `json:"redirect_uri"`
	GrantType     string `json:"grant_type" :"grant_type"`
	Code          string `json:"code" :"code"`
}

// FeishuTenantTokenRequest 飞书租户令牌请求
type FeishuTenantTokenRequest struct {
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
}

// FeishuResponse 飞书API响应
type FeishuResponse struct {
	Code int             `json:"code"`
	Msg  string          `json:"msg"`
	Data json.RawMessage `json:"data"`
}
