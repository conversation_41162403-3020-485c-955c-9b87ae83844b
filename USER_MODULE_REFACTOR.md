# 用户模块重构总结

本文档总结了根据 `docs/technical-design.md` 中的JWT认证架构设计对用户模块进行的重构工作。

## 重构概述

### 主要目标
1. 集成新的JWT认证架构（Access Token + Refresh Token双Token机制）
2. 添加设备信息收集和会话管理功能
3. 实现JWT黑名单和Token撤销机制
4. 集成Redis缓存进行用户活跃状态跟踪
5. 添加登录尝试限制和账户锁定功能
6. 保持向后兼容性

## 重构内容详细说明

### 1. 新增数据结构 (`internal/service/auth_types.go`)

#### 核心数据结构：
- **TokenPairResponse**: 新的Token对响应结构，包含Access Token和Refresh Token
- **UserInfo**: 增强的用户信息结构，包含角色、组、权限信息
- **DeviceInfo**: 设备信息结构，支持设备类型、IP地址、User-Agent等
- **UserSession**: 用户会话信息，支持多设备会话管理
- **UserClaims**: 从JWT Claims转换的用户声明信息
- **LoginAttempt**: 登录尝试记录，用于安全控制
- **AccountLockInfo**: 账户锁定信息

#### 转换函数：
- `ConvertJWTClaimsToUserClaims()`: JWT Claims到用户Claims的转换
- `ConvertToAuthUserInfo()`: 服务层用户信息到认证层的转换
- `ConvertTokenPairToResponse()`: Token对到响应格式的转换

### 2. 认证服务重构 (`internal/service/auth_service.go`)

#### 新增接口方法：
```go
// 双Token登录机制
Login(ctx context.Context, username, password string, deviceInfo *DeviceInfo) (*TokenPairResponse, error)
LoginWithRememberMe(ctx context.Context, username, password string, deviceInfo *DeviceInfo, rememberMe bool) (*TokenPairResponse, error)

// Token管理
RefreshToken(ctx context.Context, refreshToken string) (*TokenPairResponse, error)
RevokeToken(ctx context.Context, token string, reason string) error
RevokeUserTokens(ctx context.Context, userID uint, reason string) error

// 会话管理
GetUserSessions(ctx context.Context, userID uint) ([]*UserSession, error)
RevokeSession(ctx context.Context, userID uint, sessionID string) error
GetActiveSessionCount(ctx context.Context, userID uint) (int, error)

// 登录安全
CheckLoginAttempts(ctx context.Context, username, ipAddress string) (*AccountLockInfo, error)
RecordLoginAttempt(ctx context.Context, username, ipAddress, userAgent string, success bool, reason string) error
UnlockAccount(ctx context.Context, username string) error
```

#### 核心功能实现：

**1. 增强的登录流程：**
- 登录尝试限制检查
- 设备信息收集
- 双Token生成（Access Token + Refresh Token）
- 会话信息记录到Redis
- 登录历史记录

**2. Token刷新机制：**
- 刷新窗口期验证
- 频率限制检查
- 会话状态验证
- 新Token对生成

**3. 安全登出：**
- 单设备登出（撤销当前Token）
- 全设备登出（撤销用户所有Token）
- JWT黑名单管理

**4. 会话管理：**
- 多设备会话跟踪
- 会话列表查询
- 指定会话撤销
- 活跃会话统计

### 3. API接口重构 (`internal/api/v1/handler/auth.go`)

#### 更新的API端点：

**登录接口 (`POST /api/v1/auth/login`)：**
```json
{
  "username": "user",
  "password": "password",
  "remember_me": true,
  "device_id": "device-123",
  "device_type": "web",
  "platform": "Windows",
  "browser": "Chrome"
}
```

**响应格式：**
```json
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "user": {
    "user_id": 1,
    "username": "user",
    "email": "<EMAIL>",
    "roles": ["user"],
    "permissions": ["read", "write"]
  },
  "session_id": "session-123",
  "login_time": 1640995200
}
```

**新增API端点：**
- `POST /api/v1/auth/refresh` - Token刷新
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/me` - 获取当前用户信息
- `GET /api/v1/auth/sessions` - 获取用户会话列表
- `DELETE /api/v1/auth/sessions/:sessionId` - 撤销指定会话

#### 设备信息收集：
- 自动提取客户端IP地址（支持代理）
- 解析User-Agent获取浏览器和操作系统信息
- 设备类型推断（web/mobile/tablet）
- 设备ID生成和管理

### 4. 响应结构优化 (`internal/response/auth_response.go`)

#### 新增响应结构：
- **LoginResponse**: 登录响应，包含完整的Token信息
- **RefreshTokenResponse**: Token刷新响应
- **UserInfoResponse**: 用户信息响应，包含会话信息
- **SessionListResponse**: 会话列表响应
- **AuthErrorResponse**: 统一的认证错误响应

#### 错误代码标准化：
```go
const (
    ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"
    ErrCodeAccountLocked      = "ACCOUNT_LOCKED"
    ErrCodeTokenExpired       = "TOKEN_EXPIRED"
    ErrCodeTokenRevoked       = "TOKEN_REVOKED"
    // ...
)
```

### 5. 服务层集成 (`internal/service/service.go`)

#### 更新服务构造函数：
```go
func NewServiceWithRBAC(
    repo repository.Repository,
    logger *zap.Logger,
    tracer trace.Tracer,
    cfg *config.Config,
    redisManager redis.RedisManager,
    newJWTService auth.JWTService,
    jwtConfig *JWTConfig,
    // ...
) Service
```

#### 服务依赖注入：
- 新的JWT服务集成
- Redis管理器集成
- 配置管理器集成
- 保持旧版JWT服务兼容性

### 6. 路由配置更新 (`internal/api/v1/router.go`)

#### 新增路由：
```go
// 当前用户信息
authorized.GET("/auth/me", authHandler.GetCurrentUser)

// 会话管理
sessions := authorized.Group("/auth/sessions")
sessions.GET("", authHandler.GetUserSessions)
sessions.DELETE("/:sessionId", authHandler.RevokeSession)
```

## 安全增强功能

### 1. 登录尝试限制
- 基于用户名+IP地址的尝试计数
- 可配置的最大尝试次数和锁定时间
- 自动解锁机制
- 登录历史记录

### 2. 会话安全
- 多设备会话跟踪
- 会话过期管理
- 异常会话检测
- 强制登出功能

### 3. Token安全
- JWT黑名单机制
- Token撤销功能
- 刷新频率限制
- 设备绑定验证

### 4. 设备管理
- 设备信息收集
- 设备类型识别
- 可疑设备检测
- 设备会话管理

## 向后兼容性

### 保留的旧接口：
- 旧版JWT服务接口保留
- 原有Cookie名称兼容
- 原有响应格式支持
- 渐进式迁移策略

### 迁移路径：
1. 新旧JWT服务并存
2. 逐步迁移API端点
3. 客户端适配新格式
4. 最终移除旧版本

## 配置要求

### Redis配置：
```yaml
redis_modules:
  auth_security:
    ttl:
      jwt_blacklist: 86400s
      login_attempts: 900s
      user_activity: 7200s
    security:
      max_login_attempts: 5
      lockout_duration: 900s
```

### JWT配置：
```yaml
auth:
  tokens:
    access_token:
      expires_in: "1h"
    refresh_token:
      expires_in: "168h"
  refresh:
    refresh_window: "6h"
    rate_limit:
      enabled: true
      max_requests: 5
      window_size: "1h"
```

## 测试覆盖

### 单元测试：
- 认证服务核心逻辑测试
- 数据转换函数测试
- 设备信息处理测试
- 登录限制逻辑测试

### 集成测试：
- 完整登录流程测试
- Token刷新流程测试
- 会话管理测试
- 安全功能测试

### 性能测试：
- 登录性能基准测试
- 并发登录测试
- Redis缓存性能测试

## 部署注意事项

1. **Redis依赖**: 确保Redis服务可用且配置正确
2. **配置迁移**: 更新配置文件以支持新的JWT设置
3. **数据库兼容**: 确保用户表结构兼容
4. **监控设置**: 配置新的认证相关监控指标
5. **日志配置**: 更新日志配置以记录新的安全事件

## 后续优化建议

1. **性能优化**: Redis连接池优化，缓存策略调整
2. **安全增强**: 添加更多安全检测规则
3. **监控完善**: 添加更详细的认证监控指标
4. **用户体验**: 优化前端交互，支持无感刷新
5. **扩展功能**: 支持SSO、多因子认证等高级功能
