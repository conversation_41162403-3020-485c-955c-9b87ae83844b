# KubeOps 生产环境配置文件

server:
  mode: release
  port: 8080

database:
  type: mysql
  host: "${DB_HOST}"
  port: 3306
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  dbname: "${DB_NAME}"

redis:
  global:
    mode: "${REDIS_MODE:cluster}"
    nodes: 
      - "${REDIS_NODE1}"
      - "${REDIS_NODE2}"
      - "${REDIS_NODE3}"
    password: "${REDIS_PASSWORD}"
  
  pool:
    max_active: 200
    max_idle: 50

log:
  level: info
  format: json
  output: stdout

auth:
  jwt_expire: "24h"

telemetry:
  enabled: true
  endpoint: "${JAEGER_ENDPOINT}"
