# KubeOps 配置文件 - 向后兼容版本
# 
# 此配置文件展示了如何使用简单的Redis配置格式
# 系统会自动将这些配置映射到新的Redis基础设施架构

server:
  port: 8080
  mode: debug
  serve_static: true
  static_dir: "./web/dist"
  enable_swagger: true

database:
  type: sqlite
  host: localhost
  port: 3306
  username: root
  password: password
  dbname: data/kubeops.db
  sslmode: disable

# 简单的Redis配置（向后兼容）
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

log:
  level: info
  format: json
  output: stdout

auth:
  jwt_secret: "your-secret-key"
  jwt_expire: 24h

telemetry:
  enabled: false
  endpoint: "http://localhost:4317"
  service_name: "kubeops"
  service_version: "1.0.0"
