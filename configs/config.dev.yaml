# KubeOps 开发环境配置文件

server:
  mode: debug
  port: 8080

database:
  type: sqlite
  dbname: "data/kubeops-dev.db"

redis:
  global:
    mode: standalone
    nodes: ["localhost:6379"]
    password: ""

log:
  level: debug
  format: text
  output: stdout

auth:
  jwt_expire: "1h"  # 开发环境短过期时间
  tokens:
    access_token:
      expires_in: "30m"  # 开发环境更短的过期时间
    refresh_token:
      expires_in: "24h"  # 开发环境更短的刷新时间

telemetry:
  enabled: false
