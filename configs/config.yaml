# KubeOps 配置文件
#
# 配置文件结构说明：
# - server: HTTP服务器配置
# - database: 数据库配置（MySQL/SQLite）
# - redis: Redis基础设施配置（支持单机/集群/哨兵模式）
# - redis_modules: 各模块的Redis使用配置
# - log: 日志配置
# - auth: 认证配置
# - telemetry: 遥测配置
#
# Redis配置说明：
# 1. 向后兼容：保留简单的host/port配置方式
# 2. 企业级功能：支持集群、哨兵、连接池、监控等
# 3. 模块化配置：各模块可独立配置Redis使用策略
# 4. 环境变量：敏感信息可通过环境变量注入

server:
  port: 8080                    # 服务端口
  mode: debug                   # 运行模式: debug, release, test
  serve_static: true            # 是否提供静态文件服务
  static_dir: "./web/dist"      # 静态文件目录
  enable_swagger: true          # 是否启用Swagger文档
  read_timeout: 30s             # 读取超时
  write_timeout: 30s            # 写入超时
  idle_timeout: 60s             # 空闲超时

database:
  type: sqlite                  # 数据库类型: mysql, sqlite
  host: "${DB_HOST:localhost}"  # 数据库主机
  port: 3306                    # 数据库端口
  username: "${DB_USERNAME:root}" # 数据库用户名
  password: "${DB_PASSWORD:password}" # 数据库密码
  dbname: "${DB_NAME:data/kubeops.db}" # 数据库名称
  sslmode: disable              # SSL模式
  charset: utf8mb4              # 字符集
  max_idle_conns: 10            # 最大空闲连接数
  max_open_conns: 100           # 最大打开连接数
  conn_max_lifetime: 3600s      # 连接最大生命周期

redis:
  # 全局配置
  global:
    # 部署模式: standalone, cluster, sentinel
    mode: "${REDIS_MODE:standalone}"

    # Redis节点配置（根据模式不同，配置方式不同）
    nodes:
      - "${REDIS_NODE1:localhost:6379}"

    # 密码认证
    password: "${REDIS_PASSWORD}"

    # 数据库选择（仅standalone模式）
    database: 0

    # 键前缀
    key_prefix: "kubeops:v1:"

    # 序列化方式: json, msgpack, protobuf
    serialization: "json"

    # 压缩配置
    compression:
      enabled: false
      algorithm: "gzip"  # gzip, lz4, snappy
      threshold: 1024    # 超过1KB启用压缩

  # 集群配置（当mode为cluster时使用）
  cluster:
    # 集群节点列表
    nodes:
      - "redis-node1:6379"
      - "redis-node2:6379"
      - "redis-node3:6379"

    # 集群配置参数
    max_redirects: 3
    read_only: false
    route_by_latency: false
    route_randomly: false

  # 哨兵配置（当mode为sentinel时使用）
  sentinel:
    master_name: "kubeops-redis"
    nodes:
      - "sentinel1:26379"
      - "sentinel2:26379"
      - "sentinel3:26379"

    # 哨兵密码
    sentinel_password: ""

  # 连接池配置
  pool:
    # 最大空闲连接数
    max_idle: 10

    # 最大活跃连接数
    max_active: 50

    # 空闲连接超时时间
    idle_timeout: 300s

    # 连接最大生命周期
    max_conn_lifetime: 3600s

    # 连接等待超时
    wait_timeout: 5s

    # 最小空闲连接数
    min_idle: 2

  # 超时配置
  timeout:
    # 连接超时
    dial: 5s

    # 读取超时
    read: 3s

    # 写入超时
    write: 3s

    # 空闲连接检查间隔
    idle_check_frequency: 60s

  # 重试配置
  retry:
    # 最大重试次数
    max_retries: 3

    # 最小重试间隔
    min_retry_backoff: 8ms

    # 最大重试间隔
    max_retry_backoff: 512ms

  # 监控配置
  monitoring:
    # 启用监控
    enabled: true

    # 慢查询阈值
    slow_query_threshold: 100ms

    # 统计信息收集间隔
    stats_interval: 30s

    # 健康检查间隔
    health_check_interval: 10s

  # 安全配置
  security:
    # 启用TLS
    tls_enabled: false

    # TLS配置
    tls_config:
      cert_file: ""
      key_file: ""
      ca_file: ""
      insecure_skip_verify: false

# 模块特定的Redis配置
redis_modules:
  # 权限管理模块缓存配置
  permission_cache:
    # 缓存TTL配置
    ttl:
      permission_result: 300s    # 权限检查结果
      user_groups: 900s          # 用户组关系
      user_permissions: 600s     # 用户权限汇总
      policy_version: 3600s      # 策略版本

    # 缓存键配置
    keys:
      permission_prefix: "perm:"
      groups_prefix: "groups:"
      policy_prefix: "policy:"

    # 失效通知配置
    invalidation:
      channels:
        - "kubeops:cache:invalidation"
        - "kubeops:cache:policy_change"
        - "kubeops:cache:user_group_change"
      batch_size: 100
      retry_attempts: 3

  # 应用管理模块缓存配置
  application_cache:
    ttl:
      app_status: 180s           # 应用状态
      deployment_info: 300s      # 部署信息
      metrics: 60s               # 应用指标

    keys:
      app_prefix: "app:"
      deployment_prefix: "deploy:"
      metrics_prefix: "metrics:"

    # 事件流配置
    event_stream:
      max_length: 10000
      trim_strategy: "MAXLEN"

  # 用户认证和安全管理（JWT模式）
  auth_security:
    ttl:
      jwt_blacklist: 86400s      # JWT黑名单 (24小时)
      login_attempts: 900s       # 登录尝试限制 (15分钟)
      user_activity: 7200s       # 用户活跃状态 (2小时)
      password_reset: 3600s      # 密码重置令牌 (1小时)

    keys:
      blacklist_prefix: "auth:blacklist:"
      attempts_prefix: "auth:attempts:"
      activity_prefix: "auth:activity:"
      reset_prefix: "auth:reset:"

    # 安全策略配置
    security:
      max_login_attempts: 5      # 最大登录尝试次数
      lockout_duration: 900s     # 账户锁定时间 (15分钟)
      jwt_refresh_window: 3600s  # JWT刷新窗口期 (1小时)

  # WebShell模块配置
  webshell_management:
    ttl:
      shell_session: 3600s       # WebShell会话 (1小时)
      command_history: 86400s    # 命令历史 (24小时)
      session_recording: 604800s # 会话录制 (7天)

    keys:
      shell_prefix: "shell:"
      history_prefix: "history:"
      recording_prefix: "recording:"

  # 审计模块配置
  audit_management:
    ttl:
      audit_buffer: 300s         # 审计缓冲 (5分钟)
      audit_metrics: 3600s       # 审计指标 (1小时)
      alert_cache: 1800s         # 告警缓存 (30分钟)

    keys:
      buffer_prefix: "audit:buffer:"
      metrics_prefix: "audit:metrics:"
      alert_prefix: "audit:alert:"

  # 系统级功能配置
  system_level:
    # 分布式锁配置
    distributed_locks:
      default_ttl: 30s
      key_prefix: "lock:"

    # 限流配置
    rate_limiting:
      window_size: 60s
      key_prefix: "rate:"

    # 健康检查缓存
    health_check:
      ttl: 30s
      key_prefix: "health:"

log:
  level: debug                                # 日志级别: debug, info, warn, error
  format: json                                # 日志格式: json, text
  output: stdout                              # 输出目标: stdout, stderr, file
  file_path: "logs/kubeops.log"              # 日志文件路径（当output为file时）
  max_size: 100                               # 日志文件最大大小（MB）
  max_backups: 10                             # 保留的日志文件数量
  max_age: 30                                 # 日志文件保留天数
  compress: true                              # 是否压缩旧日志文件

# JWT认证配置 - 按照technical-design.md设计
auth:
  # JWT基础配置
  jwt_secret: "${JWT_SECRET:your-jwt-secret-key-at-least-32-characters}" # JWT签名密钥（至少32字符）
  jwt_expire: "24h"                          # JWT过期时间
  jwt_algorithm: "HS256"                     # 签名算法: HS256, RS256等

  # Token配置
  tokens:
    access_token:
      expires_in: "1h"                        # 访问令牌过期时间
      issuer: "kubeops"                       # 签发者
      audience: ["kubeops-api", "kubeops-web"] # 受众

    refresh_token:
      expires_in: "168h"                      # 刷新令牌过期时间（7天）
      issuer: "kubeops"                       # 签发者
      audience: ["kubeops-auth"]              # 受众

    remember_me_token:
      expires_in: "720h"                      # 记住我令牌过期时间（30天）
      issuer: "kubeops"                       # 签发者
      audience: ["kubeops-web"]               # 受众

  # 刷新策略
  refresh:
    refresh_window: "6h"                      # 刷新窗口期
    revoke_old_token_on_refresh: false       # 刷新时是否撤销旧token
    max_refresh_count: 10                     # 最大刷新次数

    # 频率限制
    rate_limit:
      enabled: true                           # 是否启用频率限制
      max_requests: 5                         # 每个时间窗口最大刷新次数
      window_size: "1h"                      # 时间窗口大小

    # 并发控制
    concurrency_control:
      enabled: true                           # 是否启用并发控制
      lock_timeout: "30s"                     # 锁超时时间

telemetry:
  enabled: false                              # 是否启用遥测
  endpoint: "${JAEGER_ENDPOINT:http://localhost:14268/api/traces}" # Jaeger端点
  service_name: "kubeops"                     # 服务名称
  service_version: "1.0.0"                   # 服务版本
  sample_rate: 0.1                            # 采样率

  # 指标配置
  metrics:
    enabled: true                             # 是否启用指标收集
    endpoint: "/metrics"                      # 指标端点
    namespace: "kubeops"                      # 指标命名空间