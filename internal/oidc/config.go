package oidc

// OIDCConfig 表示OIDC认证配置
type OIDCConfig struct {
	Enabled      bool   // 是否启用OIDC认证
	IssuerURL    string // 发行者URL
	ClientID     string // 客户端ID
	ClientSecret string // 客户端密钥
	RedirectURI  string // 重定向URI
	Scopes       string // OAuth2作用域列表，逗号分隔
	GroupsClaim  string // 组声明字段
}

// DefaultScopes 默认的OIDC作用域
const DefaultScopes = "openid,profile,email"

// DefaultGroupsClaim 默认的组声明字段
const DefaultGroupsClaim = "groups"
