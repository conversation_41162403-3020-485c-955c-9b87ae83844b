package config

import (
	"errors"
	"fmt"
	"strings"
)

// ConfigValidator 配置验证器接口
type ConfigValidator interface {
	Validate(config *Config) error
}

// DatabaseConfigValidator 数据库配置验证器
type DatabaseConfigValidator struct{}

func (v *DatabaseConfigValidator) Validate(config *Config) error {
	if config.Database.Type == "" {
		return errors.New("database type is required")
	}

	validTypes := []string{"mysql", "sqlite"}
	if !contains(validTypes, config.Database.Type) {
		return fmt.Errorf("invalid database type: %s, must be one of: %s", 
			config.Database.Type, strings.Join(validTypes, ", "))
	}

	if config.Database.Type == "mysql" {
		if config.Database.Host == "" {
			return errors.New("database host is required for mysql")
		}
		if config.Database.Username == "" {
			return errors.New("database username is required for mysql")
		}
		if config.Database.Password == "" {
			return errors.New("database password is required for mysql")
		}
		if config.Database.Port <= 0 || config.Database.Port > 65535 {
			return errors.New("database port must be between 1 and 65535")
		}
	}

	if config.Database.DBName == "" {
		return errors.New("database name is required")
	}

	return nil
}

// RedisConfigValidator Redis配置验证器
type RedisConfigValidator struct{}

func (v *RedisConfigValidator) Validate(config *Config) error {
	redis := config.Redis

	// 验证部署模式
	validModes := []string{"standalone", "cluster", "sentinel"}
	if !contains(validModes, redis.Global.Mode) {
		return fmt.Errorf("invalid redis mode: %s, must be one of: %s", 
			redis.Global.Mode, strings.Join(validModes, ", "))
	}

	// 验证节点配置
	if len(redis.Global.Nodes) == 0 {
		return errors.New("redis nodes cannot be empty")
	}

	if redis.Global.Mode == "cluster" && len(redis.Cluster.Nodes) < 3 {
		return errors.New("cluster mode requires at least 3 nodes")
	}

	if redis.Global.Mode == "sentinel" {
		if len(redis.Sentinel.Nodes) < 3 {
			return errors.New("sentinel mode requires at least 3 sentinel nodes")
		}
		if redis.Sentinel.MasterName == "" {
			return errors.New("sentinel master name is required")
		}
	}

	// 验证连接池配置
	if redis.Pool.MaxActive <= 0 {
		return errors.New("redis max active connections must be greater than 0")
	}

	if redis.Pool.MaxIdle < 0 {
		return errors.New("redis max idle connections cannot be negative")
	}

	if redis.Pool.MaxIdle > redis.Pool.MaxActive {
		return errors.New("redis max idle connections cannot exceed max active connections")
	}

	return nil
}

// JWTConfigValidator JWT配置验证器
type JWTConfigValidator struct{}

func (v *JWTConfigValidator) Validate(config *Config) error {
	auth := config.Auth

	if auth.JWTSecret == "" {
		return errors.New("jwt secret is required")
	}

	if len(auth.JWTSecret) < 32 {
		return errors.New("jwt secret must be at least 32 characters")
	}

	// 验证签名算法
	validAlgorithms := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "ES256", "ES384", "ES512"}
	if auth.JWTAlgorithm != "" && !contains(validAlgorithms, auth.JWTAlgorithm) {
		return fmt.Errorf("invalid jwt algorithm: %s, must be one of: %s", 
			auth.JWTAlgorithm, strings.Join(validAlgorithms, ", "))
	}

	// 验证Token过期时间
	if auth.JWTExpire <= 0 {
		return errors.New("jwt expire time must be greater than 0")
	}

	if auth.Tokens.AccessToken.ExpiresIn <= 0 {
		return errors.New("access token expire time must be greater than 0")
	}

	if auth.Tokens.RefreshToken.ExpiresIn <= 0 {
		return errors.New("refresh token expire time must be greater than 0")
	}

	// 验证刷新策略
	if auth.Refresh.RefreshWindow <= 0 {
		return errors.New("refresh window must be greater than 0")
	}

	if auth.Refresh.MaxRefreshCount <= 0 {
		return errors.New("max refresh count must be greater than 0")
	}

	return nil
}

// ServerConfigValidator 服务器配置验证器
type ServerConfigValidator struct{}

func (v *ServerConfigValidator) Validate(config *Config) error {
	server := config.Server

	if server.Port <= 0 || server.Port > 65535 {
		return errors.New("server port must be between 1 and 65535")
	}

	validModes := []string{"debug", "release", "test"}
	if server.Mode != "" && !contains(validModes, server.Mode) {
		return fmt.Errorf("invalid server mode: %s, must be one of: %s", 
			server.Mode, strings.Join(validModes, ", "))
	}

	return nil
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
