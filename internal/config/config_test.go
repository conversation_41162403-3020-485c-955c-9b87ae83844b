package config

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfigLoader(t *testing.T) {
	// 创建临时配置文件
	configContent := `
server:
  port: 8080
  mode: test

database:
  type: sqlite
  dbname: ":memory:"

redis:
  global:
    mode: standalone
    nodes: ["localhost:6379"]
    key_prefix: "test:"

auth:
  jwt_secret: "test-secret-key-at-least-32-characters"
  jwt_expire: "1h"
  tokens:
    access_token:
      expires_in: "30m"
      issuer: "test"
      audience: ["test-api"]

log:
  level: debug
  format: json
`

	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "config-test-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	_, err = tmpFile.WriteString(configContent)
	require.NoError(t, err)
	tmpFile.Close()

	// 测试配置加载
	cfg, err := LoadConfig(tmpFile.Name())
	require.NoError(t, err)
	assert.NotNil(t, cfg)

	// 验证服务器配置
	assert.Equal(t, 8080, cfg.Server.Port)
	assert.Equal(t, "test", cfg.Server.Mode)

	// 验证数据库配置
	assert.Equal(t, "sqlite", cfg.Database.Type)
	assert.Equal(t, ":memory:", cfg.Database.DBName)

	// 验证Redis配置
	assert.Equal(t, "standalone", cfg.Redis.Global.Mode)
	assert.Equal(t, []string{"localhost:6379"}, cfg.Redis.Global.Nodes)
	assert.Equal(t, "test:", cfg.Redis.Global.KeyPrefix)

	// 验证JWT配置
	assert.Equal(t, "test-secret-key-at-least-32-characters", cfg.Auth.JWTSecret)
	assert.Equal(t, time.Hour, cfg.Auth.JWTExpire)
	assert.Equal(t, 30*time.Minute, cfg.Auth.Tokens.AccessToken.ExpiresIn)
}

func TestEnvironmentVariableReplacement(t *testing.T) {
	// 设置环境变量
	os.Setenv("TEST_DB_HOST", "test-host")
	os.Setenv("TEST_JWT_SECRET", "test-jwt-secret-from-env")
	defer func() {
		os.Unsetenv("TEST_DB_HOST")
		os.Unsetenv("TEST_JWT_SECRET")
	}()

	configContent := `
database:
  host: "${TEST_DB_HOST:localhost}"
  port: 3306

auth:
  jwt_secret: "${TEST_JWT_SECRET}"
`

	tmpFile, err := os.CreateTemp("", "config-env-test-*.yaml")
	require.NoError(t, err)
	defer os.Remove(tmpFile.Name())

	_, err = tmpFile.WriteString(configContent)
	require.NoError(t, err)
	tmpFile.Close()

	cfg, err := LoadConfig(tmpFile.Name())
	require.NoError(t, err)

	// 验证环境变量替换
	assert.Equal(t, "test-host", cfg.Database.Host)
	assert.Equal(t, "test-jwt-secret-from-env", cfg.Auth.JWTSecret)
}

func TestConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      string
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid config",
			config: `
server:
  port: 8080
database:
  type: sqlite
  dbname: "test.db"
redis:
  global:
    mode: standalone
    nodes: ["localhost:6379"]
auth:
  jwt_secret: "valid-secret-key-at-least-32-characters"
`,
			expectError: false,
		},
		{
			name: "invalid database type",
			config: `
server:
  port: 8080
database:
  type: invalid
  dbname: "test.db"
redis:
  global:
    mode: standalone
    nodes: ["localhost:6379"]
auth:
  jwt_secret: "valid-secret-key-at-least-32-characters"
`,
			expectError: true,
			errorMsg:    "invalid database type",
		},
		{
			name: "short jwt secret",
			config: `
server:
  port: 8080
database:
  type: sqlite
  dbname: "test.db"
redis:
  global:
    mode: standalone
    nodes: ["localhost:6379"]
auth:
  jwt_secret: "short"
`,
			expectError: true,
			errorMsg:    "jwt secret must be at least 32 characters",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpFile, err := os.CreateTemp("", "config-validation-test-*.yaml")
			require.NoError(t, err)
			defer os.Remove(tmpFile.Name())

			_, err = tmpFile.WriteString(tt.config)
			require.NoError(t, err)
			tmpFile.Close()

			_, err = LoadConfig(tmpFile.Name())
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestGetDatabaseDSN(t *testing.T) {
	tests := []struct {
		name     string
		config   Config
		expected string
	}{
		{
			name: "mysql dsn",
			config: Config{
				Database: struct {
					Type            string        `yaml:"type"`
					Host            string        `yaml:"host"`
					Port            int           `yaml:"port"`
					Username        string        `yaml:"username"`
					Password        string        `yaml:"password"`
					DBName          string        `yaml:"dbname"`
					SSLMode         string        `yaml:"sslmode"`
					Charset         string        `yaml:"charset"`
					MaxIdleConns    int           `yaml:"max_idle_conns"`
					MaxOpenConns    int           `yaml:"max_open_conns"`
					ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
				}{
					Type:     "mysql",
					Host:     "localhost",
					Port:     3306,
					Username: "root",
					Password: "password",
					DBName:   "kubeops",
					Charset:  "utf8mb4",
				},
			},
			expected: "root:password@tcp(localhost:3306)/kubeops?charset=utf8mb4&parseTime=True&loc=Local",
		},
		{
			name: "sqlite dsn",
			config: Config{
				Database: struct {
					Type            string        `yaml:"type"`
					Host            string        `yaml:"host"`
					Port            int           `yaml:"port"`
					Username        string        `yaml:"username"`
					Password        string        `yaml:"password"`
					DBName          string        `yaml:"dbname"`
					SSLMode         string        `yaml:"sslmode"`
					Charset         string        `yaml:"charset"`
					MaxIdleConns    int           `yaml:"max_idle_conns"`
					MaxOpenConns    int           `yaml:"max_open_conns"`
					ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
				}{
					Type:   "sqlite",
					DBName: "test.db",
				},
			},
			expected: "test.db",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dsn := tt.config.GetDatabaseDSN()
			assert.Equal(t, tt.expected, dsn)
		})
	}
}
