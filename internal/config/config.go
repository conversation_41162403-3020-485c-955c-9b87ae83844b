package config

import (
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// Config 应用配置结构 - 按照technical-design.md设计
type Config struct {
	Server struct {
		Port         int           `yaml:"port"`
		Mode         string        `yaml:"mode"`
		ServeStatic  bool          `yaml:"serve_static"`
		StaticDir    string        `yaml:"static_dir"`
		EnableSwagger bool         `yaml:"enable_swagger"`
		ReadTimeout  time.Duration `yaml:"read_timeout"`
		WriteTimeout time.Duration `yaml:"write_timeout"`
		IdleTimeout  time.Duration `yaml:"idle_timeout"`
	} `yaml:"server"`

	Database struct {
		Type            string        `yaml:"type"`
		Host            string        `yaml:"host"`
		Port            int           `yaml:"port"`
		Username        string        `yaml:"username"`
		Password        string        `yaml:"password"`
		DBName          string        `yaml:"dbname"`
		SSLMode         string        `yaml:"sslmode"`
		Charset         string        `yaml:"charset"`
		MaxIdleConns    int           `yaml:"max_idle_conns"`
		MaxOpenConns    int           `yaml:"max_open_conns"`
		ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	} `yaml:"database"`

	// Redis基础设施配置
	Redis struct {
		Global struct {
			Mode          string            `yaml:"mode"`
			Nodes         []string          `yaml:"nodes"`
			Password      string            `yaml:"password"`
			Database      int               `yaml:"database"`
			KeyPrefix     string            `yaml:"key_prefix"`
			Serialization string            `yaml:"serialization"`
			Compression   CompressionConfig `yaml:"compression"`
		} `yaml:"global"`

		Cluster struct {
			Nodes            []string `yaml:"nodes"`
			MaxRedirects     int      `yaml:"max_redirects"`
			ReadOnly         bool     `yaml:"read_only"`
			RouteByLatency   bool     `yaml:"route_by_latency"`
			RouteRandomly    bool     `yaml:"route_randomly"`
		} `yaml:"cluster"`

		Sentinel struct {
			MasterName       string   `yaml:"master_name"`
			Nodes            []string `yaml:"nodes"`
			SentinelPassword string   `yaml:"sentinel_password"`
		} `yaml:"sentinel"`

		Pool struct {
			MaxIdle         int           `yaml:"max_idle"`
			MaxActive       int           `yaml:"max_active"`
			IdleTimeout     time.Duration `yaml:"idle_timeout"`
			MaxConnLifetime time.Duration `yaml:"max_conn_lifetime"`
			WaitTimeout     time.Duration `yaml:"wait_timeout"`
			MinIdle         int           `yaml:"min_idle"`
		} `yaml:"pool"`

		Timeout struct {
			Dial               time.Duration `yaml:"dial"`
			Read               time.Duration `yaml:"read"`
			Write              time.Duration `yaml:"write"`
			IdleCheckFrequency time.Duration `yaml:"idle_check_frequency"`
		} `yaml:"timeout"`

		Retry struct {
			MaxRetries      int           `yaml:"max_retries"`
			MinRetryBackoff time.Duration `yaml:"min_retry_backoff"`
			MaxRetryBackoff time.Duration `yaml:"max_retry_backoff"`
		} `yaml:"retry"`

		Monitoring struct {
			Enabled             bool          `yaml:"enabled"`
			SlowQueryThreshold  time.Duration `yaml:"slow_query_threshold"`
			StatsInterval       time.Duration `yaml:"stats_interval"`
			HealthCheckInterval time.Duration `yaml:"health_check_interval"`
		} `yaml:"monitoring"`

		Security struct {
			TLSEnabled bool      `yaml:"tls_enabled"`
			TLSConfig  TLSConfig `yaml:"tls_config"`
		} `yaml:"security"`
	} `yaml:"redis"`

	// JWT认证配置
	Auth struct {
		JWTSecret    string        `yaml:"jwt_secret"`
		JWTExpire    time.Duration `yaml:"jwt_expire"`
		JWTAlgorithm string        `yaml:"jwt_algorithm"`

		Tokens struct {
			AccessToken struct {
				ExpiresIn time.Duration `yaml:"expires_in"`
				Issuer    string        `yaml:"issuer"`
				Audience  []string      `yaml:"audience"`
			} `yaml:"access_token"`

			RefreshToken struct {
				ExpiresIn time.Duration `yaml:"expires_in"`
				Issuer    string        `yaml:"issuer"`
				Audience  []string      `yaml:"audience"`
			} `yaml:"refresh_token"`

			RememberMeToken struct {
				ExpiresIn time.Duration `yaml:"expires_in"`
				Issuer    string        `yaml:"issuer"`
				Audience  []string      `yaml:"audience"`
			} `yaml:"remember_me_token"`
		} `yaml:"tokens"`

		Refresh struct {
			RefreshWindow           time.Duration `yaml:"refresh_window"`
			RevokeOldTokenOnRefresh bool          `yaml:"revoke_old_token_on_refresh"`
			MaxRefreshCount         int           `yaml:"max_refresh_count"`

			RateLimit struct {
				Enabled     bool          `yaml:"enabled"`
				MaxRequests int           `yaml:"max_requests"`
				WindowSize  time.Duration `yaml:"window_size"`
			} `yaml:"rate_limit"`

			ConcurrencyControl struct {
				Enabled     bool          `yaml:"enabled"`
				LockTimeout time.Duration `yaml:"lock_timeout"`
			} `yaml:"concurrency_control"`
		} `yaml:"refresh"`
	} `yaml:"auth"`

	Log struct {
		Level       string `yaml:"level"`
		Format      string `yaml:"format"`
		Output      string `yaml:"output"`
		FilePath    string `yaml:"file_path"`
		MaxSize     int    `yaml:"max_size"`
		MaxBackups  int    `yaml:"max_backups"`
		MaxAge      int    `yaml:"max_age"`
		Compress    bool   `yaml:"compress"`
	} `yaml:"log"`

	Telemetry struct {
		Enabled        bool    `yaml:"enabled"`
		Endpoint       string  `yaml:"endpoint"`
		ServiceName    string  `yaml:"service_name"`
		ServiceVersion string  `yaml:"service_version"`
		SampleRate     float64 `yaml:"sample_rate"`

		Metrics struct {
			Enabled   bool   `yaml:"enabled"`
			Endpoint  string `yaml:"endpoint"`
			Namespace string `yaml:"namespace"`
		} `yaml:"metrics"`
	} `yaml:"telemetry"`

	// 模块特定的Redis配置
	RedisModules RedisModulesConfig `yaml:"redis_modules"`
}

// CompressionConfig 压缩配置
type CompressionConfig struct {
	Enabled   bool   `yaml:"enabled"`
	Algorithm string `yaml:"algorithm"`
	Threshold int    `yaml:"threshold"`
}

// TLSConfig TLS配置
type TLSConfig struct {
	CertFile           string `yaml:"cert_file"`
	KeyFile            string `yaml:"key_file"`
	CAFile             string `yaml:"ca_file"`
	InsecureSkipVerify bool   `yaml:"insecure_skip_verify"`
}

// RedisModulesConfig 模块特定的Redis配置
type RedisModulesConfig struct {
	PermissionCache struct {
		TTL struct {
			PermissionResult time.Duration `yaml:"permission_result"`
			UserGroups       time.Duration `yaml:"user_groups"`
			UserPermissions  time.Duration `yaml:"user_permissions"`
			PolicyVersion    time.Duration `yaml:"policy_version"`
		} `yaml:"ttl"`

		Keys struct {
			PermissionPrefix string `yaml:"permission_prefix"`
			GroupsPrefix     string `yaml:"groups_prefix"`
			PolicyPrefix     string `yaml:"policy_prefix"`
		} `yaml:"keys"`

		Invalidation struct {
			Channels       []string `yaml:"channels"`
			BatchSize      int      `yaml:"batch_size"`
			RetryAttempts  int      `yaml:"retry_attempts"`
		} `yaml:"invalidation"`
	} `yaml:"permission_cache"`

	ApplicationCache struct {
		TTL struct {
			AppStatus      time.Duration `yaml:"app_status"`
			DeploymentInfo time.Duration `yaml:"deployment_info"`
			Metrics        time.Duration `yaml:"metrics"`
		} `yaml:"ttl"`

		Keys struct {
			AppPrefix        string `yaml:"app_prefix"`
			DeploymentPrefix string `yaml:"deployment_prefix"`
			MetricsPrefix    string `yaml:"metrics_prefix"`
		} `yaml:"keys"`

		EventStream struct {
			MaxLength     int    `yaml:"max_length"`
			TrimStrategy  string `yaml:"trim_strategy"`
		} `yaml:"event_stream"`
	} `yaml:"application_cache"`

	AuthSecurity struct {
		TTL struct {
			JWTBlacklist   time.Duration `yaml:"jwt_blacklist"`
			LoginAttempts  time.Duration `yaml:"login_attempts"`
			UserActivity   time.Duration `yaml:"user_activity"`
			PasswordReset  time.Duration `yaml:"password_reset"`
		} `yaml:"ttl"`

		Keys struct {
			BlacklistPrefix string `yaml:"blacklist_prefix"`
			AttemptsPrefix  string `yaml:"attempts_prefix"`
			ActivityPrefix  string `yaml:"activity_prefix"`
			ResetPrefix     string `yaml:"reset_prefix"`
		} `yaml:"keys"`

		Security struct {
			MaxLoginAttempts   int           `yaml:"max_login_attempts"`
			LockoutDuration    time.Duration `yaml:"lockout_duration"`
			JWTRefreshWindow   time.Duration `yaml:"jwt_refresh_window"`
		} `yaml:"security"`
	} `yaml:"auth_security"`

	WebshellManagement struct {
		TTL struct {
			ShellSession     time.Duration `yaml:"shell_session"`
			CommandHistory   time.Duration `yaml:"command_history"`
			SessionRecording time.Duration `yaml:"session_recording"`
		} `yaml:"ttl"`

		Keys struct {
			ShellPrefix     string `yaml:"shell_prefix"`
			HistoryPrefix   string `yaml:"history_prefix"`
			RecordingPrefix string `yaml:"recording_prefix"`
		} `yaml:"keys"`
	} `yaml:"webshell_management"`

	AuditManagement struct {
		TTL struct {
			AuditBuffer  time.Duration `yaml:"audit_buffer"`
			AuditMetrics time.Duration `yaml:"audit_metrics"`
			AlertCache   time.Duration `yaml:"alert_cache"`
		} `yaml:"ttl"`

		Keys struct {
			BufferPrefix  string `yaml:"buffer_prefix"`
			MetricsPrefix string `yaml:"metrics_prefix"`
			AlertPrefix   string `yaml:"alert_prefix"`
		} `yaml:"keys"`
	} `yaml:"audit_management"`

	SystemLevel struct {
		DistributedLocks struct {
			DefaultTTL time.Duration `yaml:"default_ttl"`
			KeyPrefix  string        `yaml:"key_prefix"`
		} `yaml:"distributed_locks"`

		RateLimiting struct {
			WindowSize time.Duration `yaml:"window_size"`
			KeyPrefix  string        `yaml:"key_prefix"`
		} `yaml:"rate_limiting"`

		HealthCheck struct {
			TTL       time.Duration `yaml:"ttl"`
			KeyPrefix string        `yaml:"key_prefix"`
		} `yaml:"health_check"`
	} `yaml:"system_level"`
}

var (
	globalConfig *Config
	once         sync.Once
)

// ConfigLoader 配置加载器
type ConfigLoader struct {
	configPath   string
	environment  string
	validators   []ConfigValidator
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(configPath string) *ConfigLoader {
	return &ConfigLoader{
		configPath:  configPath,
		environment: getEnvironment(),
		validators: []ConfigValidator{
			&DatabaseConfigValidator{},
			&RedisConfigValidator{},
			&JWTConfigValidator{},
			&ServerConfigValidator{},
		},
	}
}

// LoadConfig 加载配置文件
func LoadConfig(filePath string) (*Config, error) {
	loader := NewConfigLoader(filePath)
	return loader.Load()
}

// Load 加载配置
func (loader *ConfigLoader) Load() (*Config, error) {
	var err error
	once.Do(func() {
		// 1. 读取配置文件
		data, readErr := os.ReadFile(loader.configPath)
		if readErr != nil {
			err = fmt.Errorf("failed to read config file: %w", readErr)
			return
		}

		// 2. 解析YAML到map
		var rawConfig map[string]interface{}
		if unmarshalErr := yaml.Unmarshal(data, &rawConfig); unmarshalErr != nil {
			err = fmt.Errorf("failed to parse yaml: %w", unmarshalErr)
			return
		}

		// 3. 环境变量替换
		if envErr := loader.replaceEnvVars(rawConfig); envErr != nil {
			err = fmt.Errorf("failed to replace env vars: %w", envErr)
			return
		}

		// 4. 加载环境特定配置
		if envConfigErr := loader.loadEnvironmentConfig(rawConfig); envConfigErr != nil {
			err = fmt.Errorf("failed to load environment config: %w", envConfigErr)
			return
		}

		// 5. 反序列化到结构体
		configBytes, marshalErr := yaml.Marshal(rawConfig)
		if marshalErr != nil {
			err = fmt.Errorf("failed to marshal config: %w", marshalErr)
			return
		}

		globalConfig = &Config{}
		if unmarshalErr := yaml.Unmarshal(configBytes, globalConfig); unmarshalErr != nil {
			err = fmt.Errorf("failed to unmarshal config: %w", unmarshalErr)
			return
		}

		// 6. 配置验证
		for _, validator := range loader.validators {
			if validateErr := validator.Validate(globalConfig); validateErr != nil {
				err = fmt.Errorf("config validation failed: %w", validateErr)
				return
			}
		}

		// 7. 设置默认值
		loader.setDefaults(globalConfig)
	})

	if err != nil {
		return nil, err
	}

	return globalConfig, nil
}

// replaceEnvVars 替换环境变量
func (loader *ConfigLoader) replaceEnvVars(config map[string]interface{}) error {
	return loader.walkConfig(config, func(value interface{}) (interface{}, error) {
		if str, ok := value.(string); ok {
			return loader.expandEnvVar(str), nil
		}
		return value, nil
	})
}

// expandEnvVar 展开环境变量
func (loader *ConfigLoader) expandEnvVar(value string) string {
	// 支持 ${VAR} 和 ${VAR:default} 格式
	re := regexp.MustCompile(`\$\{([^}:]+)(?::([^}]*))?\}`)

	return re.ReplaceAllStringFunc(value, func(match string) string {
		parts := re.FindStringSubmatch(match)
		envVar := parts[1]
		defaultValue := ""
		if len(parts) > 2 {
			defaultValue = parts[2]
		}

		if envValue := os.Getenv(envVar); envValue != "" {
			return envValue
		}
		return defaultValue
	})
}

// walkConfig 递归遍历配置
func (loader *ConfigLoader) walkConfig(config map[string]interface{}, fn func(interface{}) (interface{}, error)) error {
	for key, value := range config {
		switch v := value.(type) {
		case map[string]interface{}:
			if err := loader.walkConfig(v, fn); err != nil {
				return err
			}
		case []interface{}:
			for i, item := range v {
				newItem, err := fn(item)
				if err != nil {
					return err
				}
				v[i] = newItem
			}
		default:
			newValue, err := fn(value)
			if err != nil {
				return err
			}
			config[key] = newValue
		}
	}
	return nil
}

// loadEnvironmentConfig 加载环境特定配置
func (loader *ConfigLoader) loadEnvironmentConfig(baseConfig map[string]interface{}) error {
	if loader.environment == "" {
		return nil
	}

	envConfigPath := strings.Replace(loader.configPath, ".yaml", "."+loader.environment+".yaml", 1)

	// 检查环境配置文件是否存在
	if _, err := os.Stat(envConfigPath); os.IsNotExist(err) {
		return nil // 环境配置文件不存在时不报错
	}

	// 读取环境配置文件
	envData, err := os.ReadFile(envConfigPath)
	if err != nil {
		return fmt.Errorf("failed to read environment config file: %w", err)
	}

	var envConfig map[string]interface{}
	if err := yaml.Unmarshal(envData, &envConfig); err != nil {
		return fmt.Errorf("failed to parse environment config: %w", err)
	}

	// 合并配置
	loader.mergeConfig(baseConfig, envConfig)
	return nil
}

// mergeConfig 深度合并配置
func (loader *ConfigLoader) mergeConfig(base, override map[string]interface{}) {
	for key, value := range override {
		if baseValue, exists := base[key]; exists {
			if baseMap, ok := baseValue.(map[string]interface{}); ok {
				if overrideMap, ok := value.(map[string]interface{}); ok {
					loader.mergeConfig(baseMap, overrideMap)
					continue
				}
			}
		}
		base[key] = value
	}
}

// getEnvironment 获取当前环境
func getEnvironment() string {
	env := os.Getenv("ENVIRONMENT")
	if env == "" {
		env = os.Getenv("ENV")
	}
	if env == "" {
		return "dev" // 默认开发环境
	}
	return env
}

// setDefaults 设置默认值
func (loader *ConfigLoader) setDefaults(config *Config) {
	// 服务器默认值
	if config.Server.Port == 0 {
		config.Server.Port = 8080
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "release"
	}
	if config.Server.ReadTimeout == 0 {
		config.Server.ReadTimeout = 30 * time.Second
	}
	if config.Server.WriteTimeout == 0 {
		config.Server.WriteTimeout = 30 * time.Second
	}
	if config.Server.IdleTimeout == 0 {
		config.Server.IdleTimeout = 60 * time.Second
	}

	// 数据库默认值
	if config.Database.Port == 0 && config.Database.Type == "mysql" {
		config.Database.Port = 3306
	}
	if config.Database.Charset == "" {
		config.Database.Charset = "utf8mb4"
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = 10
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = 100
	}
	if config.Database.ConnMaxLifetime == 0 {
		config.Database.ConnMaxLifetime = 3600 * time.Second
	}

	// Redis默认值
	if config.Redis.Global.Mode == "" {
		config.Redis.Global.Mode = "standalone"
	}
	if config.Redis.Global.KeyPrefix == "" {
		config.Redis.Global.KeyPrefix = "kubeops:v1:"
	}
	if config.Redis.Global.Serialization == "" {
		config.Redis.Global.Serialization = "json"
	}

	// Redis连接池默认值
	if config.Redis.Pool.MaxActive == 0 {
		config.Redis.Pool.MaxActive = 100
	}
	if config.Redis.Pool.MaxIdle == 0 {
		config.Redis.Pool.MaxIdle = 20
	}
	if config.Redis.Pool.IdleTimeout == 0 {
		config.Redis.Pool.IdleTimeout = 300 * time.Second
	}
	if config.Redis.Pool.MaxConnLifetime == 0 {
		config.Redis.Pool.MaxConnLifetime = 3600 * time.Second
	}
	if config.Redis.Pool.WaitTimeout == 0 {
		config.Redis.Pool.WaitTimeout = 5 * time.Second
	}
	if config.Redis.Pool.MinIdle == 0 {
		config.Redis.Pool.MinIdle = 5
	}

	// Redis超时默认值
	if config.Redis.Timeout.Dial == 0 {
		config.Redis.Timeout.Dial = 5 * time.Second
	}
	if config.Redis.Timeout.Read == 0 {
		config.Redis.Timeout.Read = 3 * time.Second
	}
	if config.Redis.Timeout.Write == 0 {
		config.Redis.Timeout.Write = 3 * time.Second
	}
	if config.Redis.Timeout.IdleCheckFrequency == 0 {
		config.Redis.Timeout.IdleCheckFrequency = 60 * time.Second
	}

	// JWT默认值
	if config.Auth.JWTAlgorithm == "" {
		config.Auth.JWTAlgorithm = "HS256"
	}
	if config.Auth.JWTExpire == 0 {
		config.Auth.JWTExpire = 24 * time.Hour
	}
	if config.Auth.Tokens.AccessToken.ExpiresIn == 0 {
		config.Auth.Tokens.AccessToken.ExpiresIn = 1 * time.Hour
	}
	if config.Auth.Tokens.RefreshToken.ExpiresIn == 0 {
		config.Auth.Tokens.RefreshToken.ExpiresIn = 168 * time.Hour // 7天
	}
	if config.Auth.Refresh.RefreshWindow == 0 {
		config.Auth.Refresh.RefreshWindow = 6 * time.Hour
	}
	if config.Auth.Refresh.MaxRefreshCount == 0 {
		config.Auth.Refresh.MaxRefreshCount = 10
	}

	// 日志默认值
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
	if config.Log.Format == "" {
		config.Log.Format = "json"
	}
	if config.Log.Output == "" {
		config.Log.Output = "stdout"
	}
}

// GetConfig 获取全局配置
func GetConfig() *Config {
	if globalConfig == nil {
		zap.L().Fatal("配置未加载，请先调用 LoadConfig")
	}
	return globalConfig
}

// GetDatabaseDSN 获取数据库连接字符串
func (c *Config) GetDatabaseDSN() string {
	switch c.Database.Type {
	case "mysql":
		charset := c.Database.Charset
		if charset == "" {
			charset = "utf8mb4"
		}
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
			c.Database.Username, c.Database.Password, c.Database.Host, c.Database.Port, c.Database.DBName, charset)
	case "sqlite":
		return c.Database.DBName
	default:
		return ""
	}
}

// GetRedisNodes 获取Redis节点列表
func (c *Config) GetRedisNodes() []string {
	return c.Redis.Global.Nodes
}
