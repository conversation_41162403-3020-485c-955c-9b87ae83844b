package model

import (
	"time"

	"gorm.io/gorm"
)

// ClusterStatus 集群状态枚举
type ClusterStatus int8

const (
	ClusterStatusOffline     ClusterStatus = 0 // 离线/异常
	ClusterStatusOnline      ClusterStatus = 1 // 在线/正常
	ClusterStatusActive      ClusterStatus = 1 // 活跃状态（别名）
	ClusterStatusMaintenance ClusterStatus = 2 // 维护中
)

// Cluster 集群配置模型
type Cluster struct {
	ID          uint                   `gorm:"primaryKey" json:"id"`
	Name        string                 `gorm:"size:50;uniqueIndex;not null" json:"name"`
	DisplayName string                 `gorm:"size:100" json:"display_name"`
	ApiServer   string                 `gorm:"size:200;not null" json:"api_server"`
	KubeConfig  string                 `gorm:"type:text" json:"-"`
	Status      ClusterStatus          `gorm:"default:1" json:"status"`
	Version     string                 `gorm:"size:20" json:"version"`
	Description string                 `gorm:"size:200" json:"description"`
	Config      map[string]interface{} `gorm:"type:json" json:"config"`
	CreatedBy   uint                   `json:"created_by"`

	// 集群信息
	NodeCount       int       `json:"node_count"`
	PodCount        int       `json:"pod_count"`
	NamespaceCount  int       `json:"namespace_count"`
	LastHealthCheck time.Time `json:"last_health_check"`

	// 时间戳
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	ProjectClusters []ProjectCluster `json:"project_clusters,omitempty"`
}

// ClusterCreateRequest 创建集群请求
type ClusterCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	KubeConfig  string `json:"kubeconfig" binding:"required"`
}

// ClusterUpdateRequest 更新集群请求
type ClusterUpdateRequest struct {
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	KubeConfig  string `json:"kubeconfig"`
}

// ClusterInfo 集群基本信息
type ClusterInfo struct {
	Version        string `json:"version"`
	NodeCount      int    `json:"node_count"`
	PodCount       int    `json:"pod_count"`
	NamespaceCount int    `json:"namespace_count"`
}

// ClusterResponse 集群响应
type ClusterResponse struct {
	ID              uint          `json:"id"`
	Name            string        `json:"name"`
	DisplayName     string        `json:"display_name"`
	ApiServer       string        `json:"api_server"`
	Status          ClusterStatus `json:"status"`
	Version         string        `json:"version"`
	Description     string        `json:"description"`
	NodeCount       int           `json:"node_count"`
	PodCount        int           `json:"pod_count"`
	NamespaceCount  int           `json:"namespace_count"`
	LastHealthCheck time.Time     `json:"last_health_check"`
	CreatedAt       time.Time     `json:"created_at"`
	UpdatedAt       time.Time     `json:"updated_at"`
	ProjectCount    int           `json:"project_count,omitempty"`
}

// ClusterHealthStatus 集群健康状态
type ClusterHealthStatus struct {
	ClusterID    uint      `json:"cluster_id"`
	Status       string    `json:"status"` // healthy, unhealthy, unknown
	Message      string    `json:"message"`
	CheckedAt    time.Time `json:"checked_at"`
	ResponseTime int64     `json:"response_time"` // 毫秒
}

// InformerStatus Informer状态
type InformerStatus struct {
	ClusterID     uint                   `json:"cluster_id"`
	Status        string                 `json:"status"` // running, stopped, error
	StartedAt     *time.Time             `json:"started_at"`
	LastSyncAt    *time.Time             `json:"last_sync_at"`
	SyncDuration  int64                  `json:"sync_duration"` // 毫秒
	ResourceStats map[string]interface{} `json:"resource_stats"`
}

// ClusterMetrics 集群指标
type ClusterMetrics struct {
	ClusterID           uint      `json:"cluster_id"`
	CPUUsagePercent     float64   `json:"cpu_usage_percent"`
	MemoryUsagePercent  float64   `json:"memory_usage_percent"`
	StorageUsagePercent float64   `json:"storage_usage_percent"`
	PodCount            int       `json:"pod_count"`
	NodeCount           int       `json:"node_count"`
	NamespaceCount      int       `json:"namespace_count"`
	CollectedAt         time.Time `json:"collected_at"`
}

// TableName 指定表名
func (Cluster) TableName() string {
	return "clusters"
}
