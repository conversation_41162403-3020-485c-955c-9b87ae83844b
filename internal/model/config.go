package model

import (
	"time"

	"gorm.io/gorm"
)

// Config 通用配置模型

type Config struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	Type      string         `gorm:"size:50;index;not null" json:"type"`       // 配置类型，如keycloak_group_mapping
	Key       string         `gorm:"size:255;uniqueIndex;not null" json:"key"` // 配置键
	Value     string         `gorm:"type:text;not null" json:"value"`          // 配置值
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
