package model

import (
	"time"

	"gorm.io/gorm"
)

// AlertLevel 告警级别枚举
type AlertLevel string

const (
	AlertLevelCritical AlertLevel = "critical" // 严重
	AlertLevelWarning  AlertLevel = "warning"  // 警告
	AlertLevelInfo     AlertLevel = "info"     // 信息
)

// AlertStatus 告警状态枚举
type AlertStatus int8

const (
	AlertStatusPending  AlertStatus = 0 // 未处理
	AlertStatusHandling AlertStatus = 1 // 处理中
	AlertStatusResolved AlertStatus = 2 // 已解决
	AlertStatusIgnored  AlertStatus = 3 // 已忽略
)

// Alert 告警模型
type Alert struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	Title        string         `gorm:"size:200;not null" json:"title"`
	ClusterName  string         `gorm:"size:50;index" json:"cluster_name"`
	Namespace    string         `gorm:"size:50;index" json:"namespace"`
	Level        AlertLevel     `gorm:"size:20;not null" json:"level"`
	Resource     string         `gorm:"size:100" json:"resource"`      // 资源名称
	ResourceKind string         `gorm:"size:50" json:"resource_kind"`  // 资源类型
	Message      string         `gorm:"type:text;not null" json:"message"`
	Source       string         `gorm:"size:50;not null" json:"source"` // 告警来源
	Status       AlertStatus    `gorm:"default:0" json:"status"`
	Handler      *uint          `json:"handler"`     // 处理人ID
	HandleTime   *time.Time     `json:"handle_time"` // 处理时间
	Comment      string         `json:"comment"`     // 处理备注
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	HandlerUser *User `gorm:"foreignKey:Handler" json:"handler_user,omitempty"`
}

// AlertCreateRequest 创建告警请求
type AlertCreateRequest struct {
	Title        string     `json:"title" binding:"required"`
	ClusterName  string     `json:"cluster_name" binding:"required"`
	Namespace    string     `json:"namespace"`
	Level        AlertLevel `json:"level" binding:"required"`
	Resource     string     `json:"resource"`
	ResourceKind string     `json:"resource_kind"`
	Message      string     `json:"message" binding:"required"`
	Source       string     `json:"source" binding:"required"`
}

// AlertUpdateRequest 更新告警请求
type AlertUpdateRequest struct {
	Status  AlertStatus `json:"status"`
	Handler *uint       `json:"handler"`
	Comment string      `json:"comment"`
}

// ValidAlertLevels 有效的告警级别列表
var ValidAlertLevels = []AlertLevel{
	AlertLevelCritical,
	AlertLevelWarning,
	AlertLevelInfo,
}

// ValidAlertStatuses 有效的告警状态列表
var ValidAlertStatuses = []AlertStatus{
	AlertStatusPending,
	AlertStatusHandling,
	AlertStatusResolved,
	AlertStatusIgnored,
}

// TableName 指定表名
func (Alert) TableName() string {
	return "alerts"
}
