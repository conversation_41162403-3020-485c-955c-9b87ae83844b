package model

import (
	"time"

	"gorm.io/gorm"
)

// AuditLog 审计日志模型 - 优化版，支持"5W1H"审计要素
type AuditLog struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	UserID        uint      `gorm:"index" json:"user_id"`                  // 操作用户ID
	Username      string    `gorm:"size:50" json:"username"`               // 用户名
	UserType      string    `gorm:"size:20" json:"user_type"`              // 用户类型: local, feishu
	Department    string    `gorm:"size:100" json:"department"`            // 部门/组织
	Role          string    `gorm:"size:50" json:"role"`                   // 角色
	Action        string    `gorm:"size:50;not null" json:"action"`        // 操作类型: create, update, delete, read, approve等
	ResourceType  string    `gorm:"size:50;not null" json:"resource_type"` // 资源类型: user, role, cluster, pod等
	ResourceID    string    `gorm:"size:100" json:"resource_id"`           // 资源ID或名称
	RequestPath   string    `gorm:"size:200" json:"request_path"`          // API路径
	RequestParams string    `gorm:"type:text" json:"request_params"`       // 请求参数，JSON格式
	Result        string    `gorm:"size:20" json:"result"`                 // 操作结果: success, failed
	ErrorMessage  string    `gorm:"type:text" json:"error_message"`        // 失败原因
	IP            string    `gorm:"size:50" json:"ip"`                     // 来源IP
	Client        string    `gorm:"size:255" json:"client"`                // 客户端信息
	TraceID       string    `gorm:"size:100" json:"trace_id"`              // 链路追踪ID
	CreatedAt     time.Time `gorm:"index" json:"created_at"`               // 操作时间
}

// AuditArchive 审计归档记录模型
type AuditArchive struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	ArchiveName  string         `gorm:"size:200;not null" json:"archive_name"` // 归档文件名
	Quarter      string         `gorm:"size:10;not null" json:"quarter"`       // 归档季度，格式：2024-Q1
	StartTime    time.Time      `gorm:"not null" json:"start_time"`            // 归档数据开始时间
	EndTime      time.Time      `gorm:"not null" json:"end_time"`              // 归档数据结束时间
	RecordCount  int64          `gorm:"not null" json:"record_count"`          // 归档记录数
	FileSize     int64          `gorm:"not null" json:"file_size"`             // 文件大小（字节）
	OBSKey       string         `gorm:"size:500;not null" json:"obs_key"`      // OBS对象键
	Status       string         `gorm:"size:20;not null" json:"status"`        // 状态: pending, processing, completed, failed
	ErrorMessage string         `gorm:"type:text" json:"error_message"`        // 错误信息
	CreatedBy    uint           `gorm:"not null" json:"created_by"`            // 创建人ID
	CreatedAt    time.Time      `json:"created_at"`                            // 创建时间
	UpdatedAt    time.Time      `json:"updated_at"`                            // 更新时间
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// AuditQueryFilter 审计日志查询过滤器
type AuditQueryFilter struct {
	UserID       *uint      `json:"user_id,omitempty"`
	Username     string     `json:"username,omitempty"`
	UserType     string     `json:"user_type,omitempty"`
	Department   string     `json:"department,omitempty"`
	Role         string     `json:"role,omitempty"`
	Action       string     `json:"action,omitempty"`
	ResourceType string     `json:"resource_type,omitempty"`
	ResourceID   string     `json:"resource_id,omitempty"`
	Result       string     `json:"result,omitempty"`
	IP           string     `json:"ip,omitempty"`
	TraceID      string     `json:"trace_id,omitempty"`
	StartTime    *time.Time `json:"start_time,omitempty"`
	EndTime      *time.Time `json:"end_time,omitempty"`
	Page         int        `json:"page"`
	PageSize     int        `json:"page_size"`
}

// AuditExportRequest 审计日志导出请求
type AuditExportRequest struct {
	Filter         AuditQueryFilter `json:"filter"`
	Format         string           `json:"format"`          // csv, json
	IncludeArchive bool             `json:"include_archive"` // 是否包含归档数据
}

// AuditArchiveConfig 审计归档配置
type AuditArchiveConfig struct {
	ID                uint      `gorm:"primaryKey" json:"id"`
	ArchiveInterval   string    `gorm:"size:20;not null" json:"archive_interval"` // 归档间隔: quarterly
	RetentionDays     int       `gorm:"not null" json:"retention_days"`           // 数据库保留天数
	OBSBucket         string    `gorm:"size:100;not null" json:"obs_bucket"`      // OBS存储桶
	OBSRegion         string    `gorm:"size:50;not null" json:"obs_region"`       // OBS区域
	EncryptionEnabled bool      `gorm:"not null" json:"encryption_enabled"`       // 是否启用加密
	EncryptionKey     string    `gorm:"size:100" json:"encryption_key"`           // 加密密钥（加密存储）
	LastArchiveTime   time.Time `json:"last_archive_time"`                        // 上次归档时间
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}
