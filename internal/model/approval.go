package model

import (
	"time"

	"gorm.io/gorm"
)

// ApprovalFlow 审批流程模型
type ApprovalFlow struct {
	ID            uint           `gorm:"primaryKey" json:"id"`
	Name          string         `gorm:"size:100;not null" json:"name"`
	Description   string         `gorm:"size:255" json:"description"`
	Type          string         `gorm:"size:50;not null" json:"type"` // resource_create, resource_update, resource_delete
	ResourceType  string         `gorm:"size:50" json:"resource_type"` // pod, deployment, service等
	Namespace     string         `gorm:"size:100" json:"namespace"`    // 命名空间匹配，空表示所有
	ClusterID     uint           `gorm:"default:0" json:"cluster_id"`  // 0表示所有集群
	Status        int            `gorm:"default:1" json:"status"`      // 1启用，0禁用
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
	ApprovalSteps []ApprovalStep `gorm:"foreignKey:FlowID" json:"approval_steps"`
}

// ApprovalStep 审批步骤模型
type ApprovalStep struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	FlowID       uint           `gorm:"index" json:"flow_id"`
	Flow         *ApprovalFlow  `gorm:"foreignKey:FlowID" json:"-"`
	Name         string         `gorm:"size:100;not null" json:"name"`
	Order        int            `gorm:"default:0" json:"order"`                // 步骤顺序
	ApproverType string         `gorm:"size:50;not null" json:"approver_type"` // user, role, department
	ApproverIDs  string         `gorm:"size:500" json:"approver_ids"`          // 多个ID用逗号分隔
	ApproveType  string         `gorm:"size:50" json:"approve_type"`           // any(任一人), all(所有人)
	Timeout      int            `gorm:"default:0" json:"timeout"`              // 超时时间(小时)，0表示不限制
	Status       int            `gorm:"default:1" json:"status"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// ApprovalRequest 审批请求模型
type ApprovalRequest struct {
	ID            uint                  `gorm:"primaryKey" json:"id"`
	FlowID        uint                  `gorm:"index" json:"flow_id"`
	Flow          *ApprovalFlow         `gorm:"foreignKey:FlowID" json:"flow"`
	Title         string                `gorm:"size:200;not null" json:"title"`
	Content       string                `gorm:"type:text" json:"content"`
	ApplicantID   uint                  `gorm:"index" json:"applicant_id"` // 申请人ID
	ApplicantName string                `gorm:"size:50" json:"applicant_name"`
	ResourceType  string                `gorm:"size:50" json:"resource_type"`
	ResourceID    string                `gorm:"size:100" json:"resource_id"`
	RequestData   string                `gorm:"type:text" json:"request_data"`           // 请求数据，JSON格式
	Status        string                `gorm:"size:50;default:'pending'" json:"status"` // pending, approved, rejected, canceled
	Result        string                `gorm:"size:50" json:"result"`                   // 审批结果：同意/拒绝的理由
	CurrentStep   int                   `gorm:"default:0" json:"current_step"`           // 当前审批步骤
	CompletedAt   *time.Time            `json:"completed_at"`                            // 完成时间
	CreatedAt     time.Time             `json:"created_at"`
	UpdatedAt     time.Time             `json:"updated_at"`
	DeletedAt     gorm.DeletedAt        `gorm:"index" json:"-"`
	Steps         []ApprovalRequestStep `gorm:"foreignKey:RequestID" json:"steps"`
}

// ApprovalRequestStep 审批请求步骤模型
type ApprovalRequestStep struct {
	ID            uint             `gorm:"primaryKey" json:"id"`
	RequestID     uint             `gorm:"index" json:"request_id"`
	Request       *ApprovalRequest `gorm:"foreignKey:RequestID" json:"-"`
	StepID        uint             `json:"step_id"`
	Step          *ApprovalStep    `gorm:"foreignKey:StepID" json:"-"`
	StepName      string           `gorm:"size:100" json:"step_name"`
	StepOrder     int              `json:"step_order"`
	Status        string           `gorm:"size:50;default:'pending'" json:"status"` // pending, approved, rejected, timeout
	ApproverIDs   string           `gorm:"size:500" json:"approver_ids"`            // 实际审批人列表
	ApproverNames string           `gorm:"size:500" json:"approver_names"`
	Approver      uint             `json:"approver"` // 最终审批人ID
	ApproverName  string           `gorm:"size:50" json:"approver_name"`
	Comment       string           `gorm:"type:text" json:"comment"` // 审批意见
	ApprovedAt    *time.Time       `json:"approved_at"`              // 审批时间
	TimeoutAt     *time.Time       `json:"timeout_at"`               // 超时时间
	CreatedAt     time.Time        `json:"created_at"`
	UpdatedAt     time.Time        `json:"updated_at"`
	DeletedAt     gorm.DeletedAt   `gorm:"index" json:"-"`
}
