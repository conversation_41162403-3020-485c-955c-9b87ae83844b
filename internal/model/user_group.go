package model

import (
	"time"
)

// UserGroup 用户组模型 - 按照technical-design.md 4.3.2节设计
type UserGroup struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	Name        string    `gorm:"uniqueIndex:idx_group_name_domain;not null" json:"name"`           // 组名称
	Domain      string    `gorm:"uniqueIndex:idx_group_name_domain;default:''" json:"domain"`       // 域字段，支持多域管理
	Description string    `json:"description"`                                                       // 组描述
	Type        string    `gorm:"size:50;default:local" json:"type"`                                // 组类型：local, keycloak, feishu
	CreatedAt   time.Time `json:"created_at"`                                                        // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`                                                        // 更新时间

	// 关联关系
	Users               []User                   `gorm:"many2many:user_group_members;" json:"users,omitempty"`               // 组成员
	ResourcePermissions []ResourcePermission     `gorm:"many2many:group_resource_permissions;" json:"resource_permissions,omitempty"` // 组权限
	MappingRules        []KeycloakGroupMapping   `gorm:"foreignKey:UserGroupID" json:"mapping_rules,omitempty"`             // 映射规则
}

// UserGroupMember 用户组成员关系，记录用户是手动添加还是自动映射 - 按照technical-design.md 4.3.3节设计
type UserGroupMember struct {
	GroupID    uint      `gorm:"primaryKey;autoIncrement:false" json:"group_id"`  // 用户组ID
	UserID     uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`   // 用户ID
	IsAutoJoin bool      `gorm:"default:false" json:"is_auto_join"`               // 是否自动加入（通过映射）
	CreatedAt  time.Time `json:"created_at"`                                      // 创建时间

	// 关联关系
	User  *User      `gorm:"foreignKey:UserID" json:"user,omitempty"`   // 关联的用户
	Group *UserGroup `gorm:"foreignKey:GroupID" json:"group,omitempty"` // 关联的用户组
}

// UserGroupCreateRequest 创建用户组请求 - 按照technical-design.md简化设计
type UserGroupCreateRequest struct {
	Name        string `json:"name" binding:"required,max=50"`
	Domain      string `json:"domain" binding:"max=100"`       // 域字段
	Description string `json:"description" binding:"max=500"`
	Type        string `json:"type" binding:"max=50"`          // 组类型：local, keycloak, feishu
}

// UserGroupUpdateRequest 更新用户组请求 - 按照technical-design.md简化设计
type UserGroupUpdateRequest struct {
	Domain      string `json:"domain" binding:"max=100"`
	Description string `json:"description" binding:"max=500"`
	Type        string `json:"type" binding:"max=50"`
}

// UserGroupResponse 用户组响应 - 按照technical-design.md简化设计
type UserGroupResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Domain      string    `json:"domain"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	MemberCount int       `json:"member_count,omitempty"`
}

// GroupMemberRequest 用户组成员请求
type GroupMemberRequest struct {
	UserIDs []uint `json:"user_ids" binding:"required"`
}



// UserGroupMemberRequest 用户组成员操作请求
type UserGroupMemberRequest struct {
	UserIDs []uint `json:"user_ids" binding:"required"`
}

// UserGroupType 用户组类型常量
const (
	UserGroupTypeCustom   = "custom"   // 自定义组
	UserGroupTypeSystem   = "system"   // 系统组
	UserGroupTypeKeycloak = "keycloak" // Keycloak同步组
)

// ValidUserGroupTypes 有效的用户组类型列表
var ValidUserGroupTypes = []string{
	UserGroupTypeCustom,
	UserGroupTypeSystem,
	UserGroupTypeKeycloak,
}

// TableName 指定表名
func (UserGroup) TableName() string {
	return "user_groups"
}


