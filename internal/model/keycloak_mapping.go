package model

import "time"

// KeycloakGroupMapping Keycloak组织映射表 - 按照technical-design.md 4.3.3节设计（简化版）
type KeycloakGroupMapping struct {
	ID            uint      `gorm:"primaryKey" json:"id"`
	KeycloakGroup string    `gorm:"size:255;uniqueIndex;not null" json:"keycloak_group"` // Keycloak组完整路径，如：/上海金盈子/技术开发部/grafanaadmin
	UserGroupID   uint      `gorm:"not null;index" json:"user_group_id"`                 // 对应的本地用户组ID
	Description   string    `gorm:"size:255" json:"description"`                         // 映射描述
	CreatedAt     time.Time `json:"created_at"`                                          // 创建时间
	UpdatedAt     time.Time `json:"updated_at"`                                          // 更新时间

	// 关联关系
	UserGroup *UserGroup `gorm:"foreignKey:UserGroupID" json:"user_group,omitempty"` // 关联的本地用户组
}

// TableName 返回表名
func (KeycloakGroupMapping) TableName() string {
	return "keycloak_group_mappings"
}


