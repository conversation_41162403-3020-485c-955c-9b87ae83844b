package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/datatypes"
)

// ProjectStatus 项目状态枚举
type ProjectStatus int8

const (
	ProjectStatusInactive ProjectStatus = 0 // 未激活
	ProjectStatusActive   ProjectStatus = 1 // 激活
	ProjectStatusArchived ProjectStatus = 2 // 已归档
)

// Project 项目模型 - 可以部署到多个集群
type Project struct {
	ID          uint           `gorm:"primaryKey" json:"id"`
	Name        string         `gorm:"size:50;uniqueIndex;not null" json:"name"`        // 项目名称
	DisplayName string         `gorm:"size:100" json:"display_name"`                    // 显示名称
	Description string         `gorm:"size:500" json:"description"`                     // 项目描述
	Status      ProjectStatus  `gorm:"default:1" json:"status"`                         // 项目状态
	CreatedBy   uint           `json:"created_by"`                                      // 创建者ID
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Members         []User           `gorm:"many2many:project_members;" json:"members,omitempty"`
	ProjectClusters []ProjectCluster `json:"project_clusters,omitempty"` // 项目在各集群的部署信息
	Applications    []Application    `json:"applications,omitempty"`     // 项目下的应用
}

// ProjectCluster 项目集群关联表 - 项目在特定集群的部署配置
type ProjectCluster struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	ProjectID uint           `gorm:"not null;index" json:"project_id"`                // 项目ID
	ClusterID uint           `gorm:"not null;index" json:"cluster_id"`                // 集群ID
	Namespace string         `gorm:"size:50;not null" json:"namespace"`               // 在该集群中的命名空间名称
	Status    int8           `gorm:"default:1" json:"status"`                         // 状态：0=未激活，1=激活，2=已归档
	Config    datatypes.JSON `json:"config"`                                          // 集群特定配置
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Project *Project `gorm:"foreignKey:ProjectID" json:"project,omitempty"`
	Cluster *Cluster `gorm:"foreignKey:ClusterID" json:"cluster,omitempty"`
}

// ProjectMember 项目成员关联表（仅用于界面展示，不承载权限控制）
type ProjectMember struct {
	ProjectID uint      `gorm:"primaryKey;autoIncrement:false" json:"project_id"`
	UserID    uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
	Role      string    `gorm:"size:50;not null" json:"role"` // 项目角色：owner, admin, developer, viewer（仅用于界面展示）
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ProjectCreateRequest 创建项目请求
type ProjectCreateRequest struct {
	Name        string `json:"name" binding:"required"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	ClusterIDs  []uint `json:"cluster_ids" binding:"required"` // 要部署到的集群ID列表
}

// ProjectUpdateRequest 更新项目请求
type ProjectUpdateRequest struct {
	DisplayName string         `json:"display_name"`
	Description string         `json:"description"`
	Status      *ProjectStatus `json:"status"`
}

// ProjectMemberRequest 项目成员操作请求
type ProjectMemberRequest struct {
	UserID uint   `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required"`
}

// ProjectRole 项目角色常量
const (
	ProjectRoleOwner     = "owner"     // 项目所有者
	ProjectRoleAdmin     = "admin"     // 项目管理员
	ProjectRoleDeveloper = "developer" // 开发者
	ProjectRoleViewer    = "viewer"    // 查看者
)

// ValidProjectRoles 有效的项目角色列表
var ValidProjectRoles = []string{
	ProjectRoleOwner,
	ProjectRoleAdmin,
	ProjectRoleDeveloper,
	ProjectRoleViewer,
}

// TableName 指定表名
func (Project) TableName() string {
	return "projects"
}

// TableName 指定表名
func (ProjectCluster) TableName() string {
	return "project_clusters"
}

// TableName 指定表名
func (ProjectMember) TableName() string {
	return "project_members"
}



// ProjectResponse 项目响应
type ProjectResponse struct {
	ID          uint          `json:"id"`
	Name        string        `json:"name"`
	DisplayName string        `json:"display_name"`
	Description string        `json:"description"`
	Status      ProjectStatus `json:"status"`
	CreatedBy   uint          `json:"created_by"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
	ClusterCount int          `json:"cluster_count,omitempty"`
	AppCount     int          `json:"app_count,omitempty"`
	MemberCount  int          `json:"member_count,omitempty"`
}

// ProjectClusterRequest 项目集群请求
type ProjectClusterRequest struct {
	ClusterID uint   `json:"cluster_id" binding:"required"`
	Namespace string `json:"namespace" binding:"required,max=63"`
}


