package model

import (
	"time"

	"gorm.io/gorm"
)

// ==================== RBAC模型和常量定义 ====================

// CommonActions 常见操作常量
var CommonActions = []string{
	"create", // 创建
	"read",   // 读取
	"update", // 更新
	"delete", // 删除
	"list",   // 列表
	"exec",   // 执行
	"view",   // 查看(UI专用)
}

// K8sResources 预定义的Kubernetes资源
var K8sResources = []string{
	"pods",
	"deployments",
	"services",
	"ingresses",
	"configmaps",
	"secrets",
	"namespaces",
	"nodes",
	"persistentvolumes",
	"persistentvolumeclaims",
}

// K8sActions Kubernetes资源操作
var K8sActions = []string{
	"get",    // 获取单个资源
	"list",   // 列出资源
	"watch",  // 监控资源变化
	"create", // 创建资源
	"update", // 更新资源
	"patch",  // 部分更新资源
	"delete", // 删除资源
}

// UIActions UI资源操作
var UIActions = []string{
	"view", // 查看UI元素
}

// RBACModel 表示RBAC模型配置，存储在数据库中而不是静态文件
type RBACModel struct {
	ID          uint      `gorm:"primaryKey" json:"id"`     // 主键ID，数据库自增
	Name        string    `json:"name"`                     // 模型名称，用于标识不同的RBAC策略模型
	Description string    `json:"description"`              // 模型描述，详细解释模型的用途和特点
	Content     string    `gorm:"type:text" json:"content"` // 模型配置内容，使用Casbin语法定义的RBAC模型
	IsActive    bool      `json:"is_active"`                // 是否为当前激活的模型，true表示当前使用的模型
	CreatedAt   time.Time `json:"created_at"`               // 创建时间，记录模型创建的时间戳
	UpdatedAt   time.Time `json:"updated_at"`               // 更新时间，记录模型最后更新的时间戳
}

// ==================== 权限资源模型 ====================

// ResourcePermission 资源权限模型 - 层次化权限设计
type ResourcePermission struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	Name         string         `gorm:"size:100;not null" json:"name"`          // 权限名称
	ResourceType string         `gorm:"size:50;not null" json:"resource_type"`  // 资源类型: api, k8s, ui
	ResourcePath string         `gorm:"size:200;not null" json:"resource_path"` // 资源路径: system:api:users, cluster:*:project:*:k8s:pods
	Actions      string         `gorm:"size:200;not null" json:"actions"`       // 允许的操作: get,list,create,update,delete 或 *
	ScopeLevel   string         `gorm:"size:50;not null" json:"scope_level"`    // 权限级别: system, cluster, project, application
	Description  string         `gorm:"size:500" json:"description"`            // 描述
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

	// 关联关系
	Groups []UserGroup `gorm:"many2many:group_resource_permissions;" json:"groups,omitempty"`
	Users  []User      `gorm:"many2many:user_resource_permissions;" json:"users,omitempty"`
}

// GroupResourcePermission 用户组权限关联表
type GroupResourcePermission struct {
	GroupID              uint      `gorm:"primaryKey;autoIncrement:false" json:"group_id"`
	ResourcePermissionID uint      `gorm:"primaryKey;autoIncrement:false" json:"resource_permission_id"`
	CreatedAt            time.Time `json:"created_at"`

	// 关联关系
	Group              *UserGroup          `gorm:"foreignKey:GroupID" json:"group,omitempty"`
	ResourcePermission *ResourcePermission `gorm:"foreignKey:ResourcePermissionID" json:"resource_permission,omitempty"`
}

// UserResourcePermission 用户直接权限关联表（用于特殊授权场景）
type UserResourcePermission struct {
	UserID               uint       `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
	ResourcePermissionID uint       `gorm:"primaryKey;autoIncrement:false" json:"resource_permission_id"`
	GrantedBy            uint       `gorm:"not null" json:"granted_by"`   // 授权人ID
	GrantReason          string     `gorm:"size:500" json:"grant_reason"` // 授权原因
	ExpiresAt            *time.Time `json:"expires_at"`                   // 过期时间，NULL表示永不过期
	CreatedAt            time.Time  `json:"created_at"`

	// 关联关系
	User               *User               `gorm:"foreignKey:UserID" json:"user,omitempty"`
	ResourcePermission *ResourcePermission `gorm:"foreignKey:ResourcePermissionID" json:"resource_permission,omitempty"`
	GrantedByUser      *User               `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// PermissionCreateRequest 创建权限请求
type PermissionCreateRequest struct {
	Name         string `json:"name" binding:"required"`
	ResourceType string `json:"resource_type" binding:"required"`
	ResourcePath string `json:"resource_path" binding:"required"`
	Actions      string `json:"actions" binding:"required"`
	ScopeLevel   string `json:"scope_level" binding:"required"`
	Description  string `json:"description"`
}

// PermissionUpdateRequest 更新权限请求
type PermissionUpdateRequest struct {
	Name         string `json:"name"`
	ResourceType string `json:"resource_type"`
	ResourcePath string `json:"resource_path"`
	Actions      string `json:"actions"`
	ScopeLevel   string `json:"scope_level"`
	Description  string `json:"description"`
}

// UserPermissionRequest 用户权限请求
type UserPermissionRequest struct {
	PermissionID uint       `json:"permission_id" binding:"required"`
	Reason       string     `json:"reason"`
	ExpiresAt    *time.Time `json:"expires_at"`
}

// GroupPermissionRequest 用户组权限操作请求
type GroupPermissionRequest struct {
	PermissionIDs []uint `json:"permission_ids" binding:"required"`
}

// ResourceType 资源类型常量
const (
	ResourceTypeAPI = "api" // API权限
	ResourceTypeK8s = "k8s" // K8s资源权限
	ResourceTypeUI  = "ui"  // UI界面权限
)

// ScopeLevel 权限级别常量
const (
	ScopeLevelSystem      = "system"      // 系统级
	ScopeLevelCluster     = "cluster"     // 集群级
	ScopeLevelProject     = "project"     // 项目级
	ScopeLevelApplication = "application" // 应用级
)

// ValidResourceTypes 有效的资源类型列表
var ValidResourceTypes = []string{
	ResourceTypeAPI,
	ResourceTypeK8s,
	ResourceTypeUI,
}

// ValidScopeLevels 有效的权限级别列表
var ValidScopeLevels = []string{
	ScopeLevelSystem,
	ScopeLevelCluster,
	ScopeLevelProject,
	ScopeLevelApplication,
}

// TableName 指定表名
func (ResourcePermission) TableName() string {
	return "resource_permissions"
}

// TableName 指定表名
func (GroupResourcePermission) TableName() string {
	return "group_resource_permissions"
}

// TableName 指定表名
func (UserResourcePermission) TableName() string {
	return "user_resource_permissions"
}

// TableName 指定表名
func (RBACModel) TableName() string {
	return "rbac_models"
}

// ==================== RBAC模型方法 ====================

// DefaultRBACModel 返回默认的RBAC模型配置 - 按照technical-design.md规范
func DefaultRBACModel() *RBACModel {
	return &RBACModel{
		Name:        "KubeOps RBAC模型",
		Description: "支持用户直接权限和用户组权限的双策略模型",
		Content: `[request_definition]
r = sub, resource_type, resource_path, action

[policy_definition]
p = sub, resource_type, resource_path, action
p2 = group, resource_type, resource_path, action

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = (r.sub == p.sub && (p.resource_type == "*" || r.resource_type == p.resource_type) && resourcePathMatch(r.resource_path, p.resource_path) && (p.action == "*" || actionMatch(r.action, p.action))) || (g(r.sub, p2.group) && (p2.resource_type == "*" || r.resource_type == p2.resource_type) && resourcePathMatch(r.resource_path, p2.resource_path) && (p2.action == "*" || actionMatch(r.action, p2.action)))`,
		IsActive: true,
	}
}

// AddDomainAndPermissions 扩展现有的UserGroup模型，添加域和资源权限关联
func (ug *UserGroup) AddDomainAndPermissions() {
	// 添加域字段和资源权限关联
	// 此方法只是为了表明UserGroup模型需要扩展
}
