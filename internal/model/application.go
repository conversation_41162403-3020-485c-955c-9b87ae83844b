package model

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/datatypes"
)

// ApplicationStatus 应用状态枚举
type ApplicationStatus int8

const (
	ApplicationStatusInactive ApplicationStatus = 0 // 未激活
	ApplicationStatusActive   ApplicationStatus = 1 // 激活
	ApplicationStatusArchived ApplicationStatus = 2 // 已归档
)

// ApplicationSource 应用来源枚举
type ApplicationSource string

const (
	ApplicationSourceManual     ApplicationSource = "manual"     // 手动创建
	ApplicationSourceDiscovered ApplicationSource = "discovered" // 从K8s发现
)

// ApplicationSyncStatus 应用同步状态枚举
type ApplicationSyncStatus string

const (
	ApplicationSyncStatusSynced  ApplicationSyncStatus = "synced"  // 已同步
	ApplicationSyncStatusPending ApplicationSyncStatus = "pending" // 待同步
	ApplicationSyncStatusSyncing ApplicationSyncStatus = "syncing" // 同步中
	ApplicationSyncStatusFailed  ApplicationSyncStatus = "failed"  // 同步失败
)

// ApplicationClusterStatus 应用集群状态
type ApplicationClusterStatus string

const (
	ApplicationClusterStatusRunning   ApplicationClusterStatus = "running"   // 运行中
	ApplicationClusterStatusPending   ApplicationClusterStatus = "pending"   // 等待中
	ApplicationClusterStatusFailed    ApplicationClusterStatus = "failed"    // 失败
	ApplicationClusterStatusDeploying ApplicationClusterStatus = "deploying" // 部署中
	ApplicationClusterStatusStopped   ApplicationClusterStatus = "stopped"   // 已停止
)

// Application 应用模型 - 混合模式：支持手动创建和自动发现
type Application struct {
	ID           uint                  `gorm:"primaryKey" json:"id"`
	Name         string                `gorm:"size:100;not null" json:"name"`                              // 应用名称
	DisplayName  string                `gorm:"size:100" json:"display_name"`                               // 显示名称
	Description  string                `gorm:"size:500" json:"description"`                                // 描述
	ProjectID    uint                  `gorm:"not null;index" json:"project_id"`                           // 所属项目ID
	WorkloadType string                `gorm:"size:50;not null" json:"workload_type"`                      // 工作负载类型: deployment, statefulset, daemonset, job, cronjob
	WorkloadName string                `gorm:"size:100" json:"workload_name"`                              // 工作负载名称（K8s资源名称）
	Namespace    string                `gorm:"size:100" json:"namespace"`                                  // 命名空间
	
	// 业务信息（手动创建时填写，自动发现时为空）
	GitRepoURL   string                `gorm:"size:500" json:"git_repo_url"`                               // Git仓库地址
	GitBranch    string                `gorm:"size:100;default:main" json:"git_branch"`                    // 默认Git分支
	Language     string                `gorm:"size:50" json:"language"`                                    // 编程语言
	Framework    string                `gorm:"size:50" json:"framework"`                                   // 框架
	OwnerID      *uint                 `gorm:"index" json:"owner_id"`                                      // 负责人ID
	
	// 应用管理信息
	Source       ApplicationSource     `gorm:"size:20;default:manual" json:"source"`                       // 应用来源：manual/discovered
	SyncStatus   ApplicationSyncStatus `gorm:"size:20;default:synced" json:"sync_status"`                  // 同步状态
	IsComplete   bool                  `gorm:"default:false" json:"is_complete"`                           // 是否完善了业务信息
	Status       ApplicationStatus     `gorm:"default:1" json:"status"`                                    // 状态：0=未激活，1=激活，2=已归档
	Labels       map[string]string     `gorm:"type:json" json:"labels"`                                    // K8s标签
	Annotations  map[string]string     `gorm:"type:json" json:"annotations"`                              // K8s注解
	Config       map[string]interface{} `gorm:"type:json" json:"config"`                                   // 应用配置
	
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
	DeletedAt    gorm.DeletedAt        `gorm:"index" json:"-"`

	// 关联关系
	Project             *Project             `gorm:"foreignKey:ProjectID" json:"project,omitempty"`
	Owner               *User                `gorm:"foreignKey:OwnerID" json:"owner,omitempty"`
	Members             []User               `gorm:"many2many:application_members;" json:"members,omitempty"`
	ApplicationClusters []ApplicationCluster `json:"application_clusters,omitempty"` // 应用在各集群的部署信息
}

// ApplicationCluster 应用集群关联表 - 应用在特定集群的部署配置
type ApplicationCluster struct {
	ID                uint              `gorm:"primaryKey" json:"id"`
	ApplicationID     uint              `gorm:"not null;index" json:"application_id"`                   // 应用ID
	ProjectClusterID  uint              `gorm:"not null;index" json:"project_cluster_id"`               // 项目集群关联ID
	Replicas          int               `gorm:"default:1" json:"replicas"`                              // 副本数
	Image             string            `gorm:"size:500" json:"image"`                                  // 镜像地址
	Ports             datatypes.JSON    `json:"ports"`                                                  // 端口配置
	EnvVars           datatypes.JSON    `json:"env_vars"`                                               // 环境变量
	Resources         datatypes.JSON    `json:"resources"`                                              // 资源配置
	Labels            datatypes.JSON    `json:"labels"`                                                 // 标签
	Annotations       datatypes.JSON    `json:"annotations"`                                            // 注解
	Status            ApplicationClusterStatus `gorm:"default:running" json:"status"`                         // 状态：running/pending/failed等
	K8sStatus         string                   `gorm:"size:50" json:"k8s_status"`                             // K8s状态：Running/Pending/Failed等
	ResourceVersion   string                   `gorm:"size:100" json:"resource_version"`                      // K8s资源版本
	LastDeployedAt    *time.Time               `json:"last_deployed_at"`                                       // 最后部署时间
	CreatedAt         time.Time         `json:"created_at"`
	UpdatedAt         time.Time         `json:"updated_at"`
	DeletedAt         gorm.DeletedAt    `gorm:"index" json:"-"`

	// 关联关系
	Application    *Application    `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
	ProjectCluster *ProjectCluster `gorm:"foreignKey:ProjectClusterID" json:"project_cluster,omitempty"`
}

// ApplicationMember 应用成员关联表（仅用于界面展示，不承载权限控制）
type ApplicationMember struct {
	ApplicationID uint      `gorm:"primaryKey;autoIncrement:false" json:"application_id"`
	UserID        uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
	Role          string    `gorm:"size:50;not null" json:"role"` // 角色：owner, maintainer, developer, viewer（仅用于界面展示）
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}



// ApplicationRole 应用角色常量
const (
	ApplicationRoleOwner      = "owner"      // 应用负责人
	ApplicationRoleMaintainer = "maintainer" // 应用维护者
	ApplicationRoleDeveloper  = "developer"  // 开发者
	ApplicationRoleViewer     = "viewer"     // 查看者
)

// ValidApplicationRoles 有效的应用角色列表
var ValidApplicationRoles = []string{
	ApplicationRoleOwner,
	ApplicationRoleMaintainer,
	ApplicationRoleDeveloper,
	ApplicationRoleViewer,
}

// TableName 指定表名
func (Application) TableName() string {
	return "applications"
}

// TableName 指定表名
func (ApplicationCluster) TableName() string {
	return "application_clusters"
}

// TableName 指定表名
func (ApplicationMember) TableName() string {
	return "application_members"
}

// ApplicationCreateRequest 创建应用请求
type ApplicationCreateRequest struct {
	Name         string                 `json:"name" binding:"required,max=50"`
	DisplayName  string                 `json:"display_name" binding:"max=100"`
	Description  string                 `json:"description" binding:"max=500"`
	ProjectID    uint                   `json:"project_id" binding:"required"`
	WorkloadType string                 `json:"workload_type" binding:"required"`
	WorkloadName string                 `json:"workload_name" binding:"required,max=63"`
	Namespace    string                 `json:"namespace" binding:"required,max=63"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	Config       map[string]interface{} `json:"config"`
	OwnerID      uint                   `json:"owner_id"`
}

// ApplicationUpdateRequest 更新应用请求
type ApplicationUpdateRequest struct {
	DisplayName  string                 `json:"display_name" binding:"max=100"`
	Description  string                 `json:"description" binding:"max=500"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	Config       map[string]interface{} `json:"config"`
	Status       ApplicationStatus      `json:"status"`
	SyncStatus   ApplicationSyncStatus  `json:"sync_status"`
}

// ApplicationDiscoverRequest 应用发现请求
type ApplicationDiscoverRequest struct {
	ProjectID    uint     `json:"project_id" binding:"required"`
	ClusterID    uint     `json:"cluster_id" binding:"required"`
	Namespaces   []string `json:"namespaces"`
	WorkloadTypes []string `json:"workload_types"`
	LabelSelector string   `json:"label_selector"`
}

// ApplicationCompleteRequest 完善应用信息请求
type ApplicationCompleteRequest struct {
	DisplayName string                 `json:"display_name" binding:"max=100"`
	Description string                 `json:"description" binding:"max=500"`
	Labels      map[string]string      `json:"labels"`
	Annotations map[string]string      `json:"annotations"`
	Config      map[string]interface{} `json:"config"`
	OwnerID     uint                   `json:"owner_id"`
}

// ApplicationResponse 应用响应
type ApplicationResponse struct {
	ID           uint                  `json:"id"`
	Name         string                `json:"name"`
	DisplayName  string                `json:"display_name"`
	Description  string                `json:"description"`
	ProjectID    uint                  `json:"project_id"`
	WorkloadType string                `json:"workload_type"`
	WorkloadName string                `json:"workload_name"`
	Namespace    string                `json:"namespace"`
	Status       ApplicationStatus     `json:"status"`
	SyncStatus   ApplicationSyncStatus `json:"sync_status"`
	Labels       map[string]string     `json:"labels"`
	Annotations  map[string]string     `json:"annotations"`
	Config       map[string]interface{} `json:"config"`
	OwnerID      uint                  `json:"owner_id"`
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
	ClusterCount int                   `json:"cluster_count,omitempty"`
	MemberCount  int                   `json:"member_count,omitempty"`
}

// ApplicationClusterConfig 应用集群配置
type ApplicationClusterConfig struct {
	Replicas    int32                  `json:"replicas"`
	Resources   map[string]interface{} `json:"resources"`
	Environment map[string]string      `json:"environment"`
	Config      map[string]interface{} `json:"config"`
}

// ApplicationMemberRequest 应用成员请求
type ApplicationMemberRequest struct {
	UserID uint   `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required,max=50"`
}

// ApplicationStatsResponse 应用统计响应
type ApplicationStatsResponse struct {
	TotalCount    int64                    `json:"total_count"`
	StatusCounts  map[string]int64         `json:"status_counts"`
	ClusterCounts map[string]int64         `json:"cluster_counts"`
	TypeCounts    map[string]int64         `json:"type_counts"`
	HealthStatus  map[string]interface{}   `json:"health_status"`
}

// WorkloadEvent 工作负载事件
type WorkloadEvent struct {
	ClusterID    uint                   `json:"cluster_id"`
	Namespace    string                 `json:"namespace"`
	WorkloadType string                 `json:"workload_type"`
	WorkloadName string                 `json:"workload_name"`
	EventType    string                 `json:"event_type"` // ADDED, MODIFIED, DELETED
	ResourceVersion string              `json:"resource_version"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	Spec         map[string]interface{} `json:"spec"`
	Status       map[string]interface{} `json:"status"`
	Timestamp    time.Time              `json:"timestamp"`
}

// WorkloadInfo 工作负载信息
type WorkloadInfo struct {
	Namespace    string                 `json:"namespace"`
	WorkloadType string                 `json:"workload_type"`
	WorkloadName string                 `json:"workload_name"`
	Labels       map[string]string      `json:"labels"`
	Annotations  map[string]string      `json:"annotations"`
	Spec         map[string]interface{} `json:"spec"`
	Status       map[string]interface{} `json:"status"`
	CreatedAt    time.Time              `json:"created_at"`
}

// DiscoverSummary 发现摘要
type DiscoverSummary struct {
	TotalDiscovered int `json:"total_discovered"` // 发现的工作负载总数
	Created         int `json:"created"`          // 新创建的应用数
	Updated         int `json:"updated"`          // 更新的应用数
	Skipped         int `json:"skipped"`          // 跳过的应用数（已存在）
	Incomplete      int `json:"incomplete"`       // 待完善的应用数
}

// ConsistencyReport 一致性检查报告
type ConsistencyReport struct {
	ClusterID         uint                `json:"cluster_id"`
	TotalApplications int                 `json:"total_applications"`
	TotalWorkloads    int                 `json:"total_workloads"`
	OrphanApps        []*Application      `json:"orphan_apps"`        // 数据库中存在但K8s中不存在
	MissingApps       []*WorkloadInfo     `json:"missing_apps"`       // K8s中存在但数据库中缺失
	InconsistentApps  []*Application      `json:"inconsistent_apps"`  // 状态不一致的应用
	CheckedAt         time.Time           `json:"checked_at"`
}

// ApplicationSyncConfig 应用同步配置
type ApplicationSyncConfig struct {
	ClusterID         uint     `json:"cluster_id"`
	EnabledNamespaces []string `json:"enabled_namespaces"` // 启用同步的命名空间，空表示所有
	ExcludedNamespaces []string `json:"excluded_namespaces"` // 排除的命名空间
	WorkloadTypes     []string `json:"workload_types"`     // 同步的工作负载类型
	LabelSelector     string   `json:"label_selector"`     // 标签选择器
	SyncInterval      int      `json:"sync_interval"`      // 同步间隔（秒）
	AutoComplete      bool     `json:"auto_complete"`      // 是否自动完善应用信息
}
