package telemetry

import (
	"context"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
)

// MetricsProvider 提供应用程序指标收集功能
type MetricsProvider struct {
	meter      metric.Meter
	counters   map[string]metric.Int64Counter
	gauges     map[string]metric.Float64Gauge
	histograms map[string]metric.Float64Histogram
}

// NewMetricsProvider 创建一个新的指标提供程序
func NewMetricsProvider(meterProvider metric.MeterProvider, serviceName string) *MetricsProvider {
	if meterProvider == nil {
		meterProvider = otel.GetMeterProvider()
	}

	meter := meterProvider.Meter(serviceName)
	return &MetricsProvider{
		meter:      meter,
		counters:   make(map[string]metric.Int64Counter),
		gauges:     make(map[string]metric.Float64Gauge),
		histograms: make(map[string]metric.Float64Histogram),
	}
}

// RegisterCounter 注册一个计数器指标
func (p *MetricsProvider) RegisterCounter(name, description string) (metric.Int64Counter, error) {
	if p == nil || p.meter == nil {
		return nil, nil
	}

	if counter, exists := p.counters[name]; exists {
		return counter, nil
	}

	counter, err := p.meter.Int64Counter(
		name,
		metric.WithDescription(description),
	)
	if err != nil {
		return nil, err
	}

	p.counters[name] = counter
	return counter, nil
}

// RegisterGauge 注册一个仪表指标
func (p *MetricsProvider) RegisterGauge(name, description string) (metric.Float64Gauge, error) {
	if p == nil || p.meter == nil {
		return nil, nil
	}

	if gauge, exists := p.gauges[name]; exists {
		return gauge, nil
	}

	gauge, err := p.meter.Float64Gauge(
		name,
		metric.WithDescription(description),
	)
	if err != nil {
		return nil, err
	}

	p.gauges[name] = gauge
	return gauge, nil
}

// RegisterHistogram 注册一个直方图指标
func (p *MetricsProvider) RegisterHistogram(name, description string) (metric.Float64Histogram, error) {
	if p == nil || p.meter == nil {
		return nil, nil
	}

	if histogram, exists := p.histograms[name]; exists {
		return histogram, nil
	}

	histogram, err := p.meter.Float64Histogram(
		name,
		metric.WithDescription(description),
	)
	if err != nil {
		return nil, err
	}

	p.histograms[name] = histogram
	return histogram, nil
}

// IncCounter 增加计数器值
func (p *MetricsProvider) IncCounter(ctx context.Context, name string, value int64, attributes ...attribute.KeyValue) {
	if p == nil || p.counters == nil {
		return
	}

	if counter, exists := p.counters[name]; exists {
		counter.Add(ctx, value, metric.WithAttributes(attributes...))
	}
}

// SetGauge 设置仪表值
func (p *MetricsProvider) SetGauge(ctx context.Context, name string, value float64, attributes ...attribute.KeyValue) {
	if p == nil || p.gauges == nil {
		return
	}

	if gauge, exists := p.gauges[name]; exists {
		gauge.Record(ctx, value, metric.WithAttributes(attributes...))
	}
}

// RecordHistogram 记录直方图指标
func (p *MetricsProvider) RecordHistogram(ctx context.Context, name string, value float64, attributes ...attribute.KeyValue) {
	if p == nil || p.histograms == nil {
		return
	}

	if histogram, exists := p.histograms[name]; exists {
		histogram.Record(ctx, value, metric.WithAttributes(attributes...))
	}
}

// RecordRequestDuration 记录请求处理时间
func (p *MetricsProvider) RecordRequestDuration(ctx context.Context, method, path string, statusCode int, startTime time.Time) {
	if p == nil || p.histograms == nil {
		return
	}

	if histogram, exists := p.histograms["http.request.duration"]; exists {
		duration := float64(time.Since(startTime).Milliseconds())
		histogram.Record(ctx, duration,
			metric.WithAttributes(
				attribute.String("method", method),
				attribute.String("path", path),
				attribute.Int("status_code", statusCode),
			),
		)
	}
}
