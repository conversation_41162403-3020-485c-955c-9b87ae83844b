package telemetry

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Config 包含遥测配置选项
type Config struct {
	ServiceName    string
	ServiceVersion string
	Environment    string
	OTLPEndpoint   string
	Enabled        bool
	SamplingRate   float64
}

// Provider 表示遥测供应商
type Provider struct {
	tracer         trace.Tracer
	tracerProvider *sdktrace.TracerProvider
	meterProvider  metric.MeterProvider
	config         *Config
	shutdown       func(context.Context) error
}

// noopExporter 是一个不执行任何操作的导出器
type noopExporter struct{}

func (e *noopExporter) ExportSpans(ctx context.Context, spans []sdktrace.ReadOnlySpan) error {
	return nil
}

func (e *noopExporter) Shutdown(ctx context.Context) error {
	return nil
}

// NewProvider 创建并配置新的遥测提供者
func NewProvider(ctx context.Context, config *Config) (*Provider, error) {
	// 创建资源，无论是否启用遥测
	res, err := resource.New(ctx,
		resource.WithAttributes(
			semconv.ServiceName(config.ServiceName),
			semconv.ServiceVersion(config.ServiceVersion),
			attribute.String("environment", config.Environment),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("创建遥测资源失败: %w", err)
	}

	// 即使未启用远程遥测，也需要创建本地跟踪提供者以生成有效的trace ID和span ID
	if !config.Enabled {
		// 创建本地跟踪提供者，不导出到远程系统
		tracerProvider := sdktrace.NewTracerProvider(
			sdktrace.WithSampler(sdktrace.AlwaysSample()),
			sdktrace.WithResource(res),
		)

		// 设置全局跟踪提供者
		otel.SetTracerProvider(tracerProvider)

		// 设置全局传播器
		otel.SetTextMapPropagator(propagation.TraceContext{})

		tracer := tracerProvider.Tracer(config.ServiceName)

		return &Provider{
			tracer:         tracer,
			tracerProvider: tracerProvider,
			meterProvider:  otel.GetMeterProvider(),
			config:         config,
			shutdown: func(context.Context) error {
				return tracerProvider.Shutdown(ctx)
			},
		}, nil
	}

	// 配置采样率
	samplingRate := config.SamplingRate
	if samplingRate <= 0 || samplingRate > 1 {
		samplingRate = 1.0 // 默认采样所有请求
	}

	var tracerProvider *sdktrace.TracerProvider
	var exporter sdktrace.SpanExporter

	// 尝试创建OTLP导出器，但使用非阻塞连接
	if config.OTLPEndpoint != "" {
		// 设置短超时，防止长时间阻塞
		dialCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
		defer cancel()

		// 尝试非阻塞方式连接
		var dialErr error
		conn, dialErr := grpc.DialContext(dialCtx, config.OTLPEndpoint,
			grpc.WithTransportCredentials(insecure.NewCredentials()),
			grpc.WithBlock(),
		)

		if dialErr == nil {
			// 连接成功，尝试创建导出器
			expCtx, expCancel := context.WithTimeout(ctx, 2*time.Second)
			defer expCancel()

			var expErr error
			exporter, expErr = otlptrace.New(expCtx,
				otlptracegrpc.NewClient(
					otlptracegrpc.WithGRPCConn(conn),
				),
			)

			if expErr != nil {
				// 导出器创建失败，使用空导出器
				fmt.Printf("警告: 无法创建OTLP导出器: %v, 将使用空导出器\n", expErr)
				exporter = &noopExporter{}
			} else {
				fmt.Println("成功连接到OTLP Collector")
			}
		} else {
			// 连接失败，使用空导出器
			fmt.Printf("警告: 无法连接到OTLP Collector %s: %v, 将使用空导出器\n",
				config.OTLPEndpoint, dialErr)
			exporter = &noopExporter{}
		}
	} else {
		// 未配置OTLP端点，使用空导出器
		fmt.Println("未配置OTLP端点，将使用空导出器")
		exporter = &noopExporter{}
	}

	// 创建批处理Span处理器
	bsp := sdktrace.NewBatchSpanProcessor(exporter)

	// 创建跟踪提供者
	tracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sdktrace.TraceIDRatioBased(samplingRate)),
		sdktrace.WithResource(res),
		sdktrace.WithSpanProcessor(bsp),
	)

	// 设置全局传播器
	otel.SetTextMapPropagator(propagation.TraceContext{})

	// 设置全局跟踪提供者
	otel.SetTracerProvider(tracerProvider)

	tracer := tracerProvider.Tracer(config.ServiceName)

	// 返回带关闭功能的提供者
	return &Provider{
		tracer:         tracer,
		tracerProvider: tracerProvider,
		meterProvider:  otel.GetMeterProvider(),
		config:         config,
		shutdown: func(ctx context.Context) error {
			ctxShutdown, cancel := context.WithTimeout(ctx, 5*time.Second)
			defer cancel()
			if err := tracerProvider.Shutdown(ctxShutdown); err != nil {
				fmt.Printf("关闭跟踪提供者时出错: %v\n", err)
			}
			return nil
		},
	}, nil
}

// Tracer 返回配置的跟踪器
func (p *Provider) Tracer() trace.Tracer {
	if p == nil || p.tracer == nil {
		return otel.GetTracerProvider().Tracer("noop")
	}
	return p.tracer
}

// TracerProvider 返回跟踪提供者
func (p *Provider) TracerProvider() trace.TracerProvider {
	if p == nil || p.tracerProvider == nil {
		return otel.GetTracerProvider()
	}
	return p.tracerProvider
}

// MeterProvider 返回指标提供者
func (p *Provider) MeterProvider() metric.MeterProvider {
	if p == nil || p.meterProvider == nil {
		return otel.GetMeterProvider()
	}
	return p.meterProvider
}

// Shutdown 优雅地关闭遥测提供者
func (p *Provider) Shutdown(ctx context.Context) error {
	if p == nil || p.shutdown == nil {
		return nil
	}
	return p.shutdown(ctx)
}
