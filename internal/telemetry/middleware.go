package telemetry

import (
	"time"

	"kubeops/internal/logger"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

// Middleware OpenTelemetry中间件结构体
type Middleware struct {
	tracerProvider trace.TracerProvider
	serviceName    string
	logger         *zap.Logger
}

// NewMiddleware 创建新的OpenTelemetry中间件
func NewMiddleware(tracerProvider trace.TracerProvider, serviceName string, logger *zap.Logger) *Middleware {
	return &Middleware{
		tracerProvider: tracerProvider,
		serviceName:    serviceName,
		logger:         logger,
	}
}

// Handle 返回OpenTelemetry中间件处理函数
func (m *Middleware) Handle() gin.HandlerFunc {
	return TraceMiddleware(m.serviceName)
}

// MetricsMiddleware 指标中间件结构体
type MetricsMiddlewareImpl struct {
	metrics *MetricsProvider
}

// NewMetricsMiddleware 创建新的指标中间件
func NewMetricsMiddleware(metrics *MetricsProvider) *MetricsMiddlewareImpl {
	return &MetricsMiddlewareImpl{
		metrics: metrics,
	}
}

// Handle 返回指标中间件处理函数
func (m *MetricsMiddlewareImpl) Handle() gin.HandlerFunc {
	return MetricsMiddleware(m.metrics)
}

// TraceMiddleware 返回Gin中间件，为每个请求创建一个新的追踪Span
func TraceMiddleware(serviceName string) gin.HandlerFunc {
	tracer := otel.Tracer(serviceName)
	propagator := otel.GetTextMapPropagator()

	return func(c *gin.Context) {
		// 从传入请求中提取追踪上下文
		ctx := propagator.Extract(c.Request.Context(), propagation.HeaderCarrier(c.Request.Header))

		// 为此请求开始一个新Span
		spanName := c.Request.Method + " " + c.FullPath()
		if c.FullPath() == "" {
			spanName = c.Request.Method + " " + c.Request.URL.Path
		}

		// 添加采样配置，确保所有请求都被采样
		opts := []trace.SpanStartOption{
			trace.WithAttributes(
				attribute.String("http.method", c.Request.Method),
				attribute.String("http.url", c.Request.URL.String()),
				attribute.String("http.scheme", c.Request.URL.Scheme),
				attribute.String("http.host", c.Request.Host),
				attribute.String("http.path", c.Request.URL.Path),
				attribute.String("http.user_agent", c.Request.UserAgent()),
				attribute.String("http.client_ip", c.ClientIP()),
			),
			trace.WithSpanKind(trace.SpanKindServer),
		}

		ctx, span := tracer.Start(ctx, spanName, opts...)
		defer span.End()

		// 在响应头中添加 trace ID
		traceID := span.SpanContext().TraceID().String()
		spanID := span.SpanContext().SpanID().String()
		c.Writer.Header().Set("X-Trace-ID", traceID)
		c.Writer.Header().Set("X-Span-ID", spanID)

		// 将上下文传递给下一个处理程序
		c.Request = c.Request.WithContext(ctx)

		// 记录请求开始日志
		requestInfo := map[string]interface{}{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"client_ip":  c.ClientIP(),
			"user_agent": c.Request.UserAgent(),
			"referer":    c.Request.Referer(),
			"request_id": c.GetHeader("X-Request-ID"),
		}

		logger.InfoContext(ctx, "HTTP请求开始", zap.Any("request", requestInfo))

		// 记录请求开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 计算请求处理时间
		latency := time.Since(startTime)

		// 添加响应属性到Span
		span.SetAttributes(
			attribute.Int("http.status_code", c.Writer.Status()),
			attribute.Int("http.response_size", c.Writer.Size()),
		)

		// 记录请求结束日志
		responseInfo := map[string]interface{}{
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"status":     c.Writer.Status(),
			"size":       c.Writer.Size(),
			"latency_ms": latency.Milliseconds(),
			"latency":    latency.String(),
			"trace_id":   traceID,
			"span_id":    spanID,
		}

		// 记录错误（如果有）
		if len(c.Errors) > 0 {
			span.RecordError(c.Errors[0])
			responseInfo["error"] = c.Errors.String()
			logger.ErrorContext(ctx, "HTTP请求失败", zap.Any("response", responseInfo))
		} else if c.Writer.Status() >= 400 {
			span.SetAttributes(attribute.Bool("error", true))
			logger.WarnContext(ctx, "HTTP请求完成但状态码异常", zap.Any("response", responseInfo))
		} else {
			logger.InfoContext(ctx, "HTTP请求成功完成", zap.Any("response", responseInfo))
		}

		// 设置Span状态
		if c.Writer.Status() >= 400 {
			span.SetAttributes(attribute.Bool("error", true))
			if len(c.Errors) > 0 {
				span.SetAttributes(attribute.String("error.message", c.Errors.String()))
			}
		}
	}
}

// MetricsMiddleware 返回Gin中间件，收集请求指标
func MetricsMiddleware(metrics *MetricsProvider) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		startTime := time.Now()

		// 处理请求
		c.Next()

		// 记录请求处理时间
		metrics.RecordRequestDuration(
			c.Request.Context(),
			c.Request.Method,
			c.FullPath(),
			c.Writer.Status(),
			startTime,
		)

		// 增加请求计数
		metrics.IncCounter(
			c.Request.Context(),
			"http.requests.total",
			1,
			attribute.String("method", c.Request.Method),
			attribute.String("path", c.FullPath()),
			attribute.Int("status_code", c.Writer.Status()),
		)
	}
}
