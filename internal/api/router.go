package api

import (
	"context"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	swaggerfiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.uber.org/zap"

	v1 "kubeops/internal/api/v1"
	"kubeops/internal/auth"
	"kubeops/internal/config"
	"kubeops/internal/middleware"
	"kubeops/internal/router"
	"kubeops/internal/service"
	"kubeops/internal/telemetry"
)

// SetupRouter 设置路由器 - 重构版本
func SetupRouter(
	cfg *config.Config,
	svc service.Service,
	jwtService auth.JWTService,
	telemetryProvider *telemetry.Provider,
	metricsProvider *telemetry.MetricsProvider,
	logger *zap.Logger,
) *gin.Engine {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 创建Gin引擎
	engine := gin.New()

	// 使用恢复和日志中间件
	engine.Use(gin.Recovery())
	engine.Use(middleware.ErrorHandler())

	// 添加请求体捕获中间件 - 必须在审计日志中间件之前应用
	engine.Use(middleware.RequestBodyCapture())

	// 添加跨域中间件
	engine.Use(middleware.CORS())

	// 添加OpenTelemetry中间件
	if telemetryProvider != nil && telemetryProvider.TracerProvider() != nil {
		engine.Use(telemetry.NewMiddleware(telemetryProvider.TracerProvider(), "http", logger).Handle())
	}

	// 添加指标中间件
	if metricsProvider != nil {
		engine.Use(telemetry.NewMetricsMiddleware(metricsProvider).Handle())
	}

	// 添加审计日志中间件
	auditMiddleware := middleware.NewAuditMiddleware(svc.Audit(), logger)
	engine.Use(auditMiddleware.Handle())

	// JWT中间件在v1路由中创建

	// 创建路由注册器
	registrar := router.NewRouteRegistrar(engine, svc.RBAC(), logger, telemetryProvider.Tracer())

	// 健康检查路由 - 跳过权限注册
	rootGroup := registrar.Group("")
	rootGroup.WithPermission().GET("/ping",
		[]router.RouteOption{
			router.SkipPermissionRegistration(),
		},
		func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
			})
		})

	// API路由
	apiGroup := registrar.Group("/api")
	{
		// V1 API
		v1Group := apiGroup.Group("/v1")
		{
			// 注册V1 API路由
			v1.RegisterRoutes(v1Group, svc, telemetryProvider.Tracer(), logger, cfg)
		}
	}

	// Swagger API文档 - 跳过权限注册
	if cfg.Server.EnableSwagger {
		swaggerGroup := registrar.Group("")
		swaggerGroup.WithPermission().GET("/swagger/*any",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			ginSwagger.WrapHandler(swaggerfiles.Handler))
	}

	// 静态文件服务
	if cfg.Server.ServeStatic {
		staticDir := cfg.Server.StaticDir
		if staticDir == "" {
			staticDir = "./web/dist"
		}

		// 检查目录是否存在
		if _, err := os.Stat(staticDir); err == nil {
			logger.Info("提供静态文件服务", zap.String("目录", staticDir))
			engine.Static("/static", staticDir+"/static")
			engine.StaticFile("/", staticDir+"/index.html")
			engine.StaticFile("/favicon.ico", staticDir+"/favicon.ico")

			// 将所有未匹配的路由指向index.html（用于SPA前端路由）
			engine.NoRoute(func(c *gin.Context) {
				// 如果请求的是API路径，返回404
				if strings.HasPrefix(c.Request.URL.Path, "/api/") {
					logger.Warn("API路径不存在",
						zap.String("path", c.Request.URL.Path),
						zap.String("method", c.Request.Method),
						zap.String("client_ip", c.ClientIP()))
					c.JSON(http.StatusNotFound, gin.H{"error": "API路径不存在", "path": c.Request.URL.Path})
					return
				}
				// 否则返回index.html
				c.File(staticDir + "/index.html")
			})
		} else {
			logger.Warn("静态文件目录不存在，不提供静态文件服务", zap.String("目录", staticDir), zap.Error(err))
		}
	}

	// 注册API资源
	logger.Info("开始注册API资源")
	if err := registrar.RegisterResources(context.Background()); err != nil {
		logger.Error("注册API资源失败", zap.Error(err))
	}

	return engine
}
