package v1

import (
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/api/v1/handler"
	"kubeops/internal/config"
	"kubeops/internal/middleware"
	"kubeops/internal/router"
	"kubeops/internal/service"
)

// RegisterRoutes 使用自定义路由组注册API路由
func RegisterRoutes(r *router.RouteGroup, svc service.Service, tracer trace.Tracer, logger *zap.Logger, cfg *config.Config) {
	// 创建处理器
	authHandler := handler.NewAuthHandler(svc.Auth(), tracer)
	userHandler := handler.NewUserHandler(svc.User(), svc.<PERSON>(), svc.UserGroup(), tracer)
	rbacHandler := handler.NewRBACHandler(svc.RBAC(), logger, tracer)
	userGroupHandler := handler.NewUserGroupHandler(svc.UserGroup(), svc.<PERSON>(), logger, tracer)
	clusterHandler := handler.NewClusterHandler(svc.Cluster(), tracer)
	systemConfigHandler := handler.NewSystemConfigHandler(logger, svc.Config(), svc.UserGroup(), svc.SystemConfig())
	auditHandler := handler.NewAuditHandler(svc.Audit(), logger)

	oidcHandler := handler.NewOIDCHandler(svc.Auth(), tracer)

	// JWT中间件
	jwtMiddleware := middleware.NewJWTMiddleware(svc.JWT(), cfg)

	// 权限中间件
	permissionMiddleware := middleware.NewPermissionMiddleware(
		svc.RBAC(),
		logger,
		tracer,
	)

	// 公共路由，不需要认证
	public := r.Group("")
	{
		// 公共健康检查路由 - 跳过权限注册
		public.WithPermission().GET("/ping",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			func(c *gin.Context) {
				c.JSON(200, gin.H{"message": "pong"})
			})

		// 认证相关路由
		public.WithPermission().POST("/auth/login",
			[]router.RouteOption{
				router.WithResource("auth"),
				router.WithAction("login"),
				router.WithResourceName("认证"),
				router.WithActionName("登录"),
				router.WithDescription("用户登录"),
			},
			authHandler.Login)

		public.WithPermission().POST("/auth/refresh",
			[]router.RouteOption{
				router.WithResource("auth"),
				router.WithAction("refresh"),
				router.WithResourceName("认证"),
				router.WithActionName("刷新"),
				router.WithDescription("刷新访问令牌"),
			},
			authHandler.RefreshToken)

		public.WithPermission().POST("/auth/logout",
			[]router.RouteOption{
				router.WithResource("auth"),
				router.WithAction("logout"),
				router.WithResourceName("认证"),
				router.WithActionName("登出"),
				router.WithDescription("用户登出"),
			},
			authHandler.Logout)

		// OIDC相关路由
		public.WithPermission().GET("/auth/oidc/login",
			[]router.RouteOption{
				router.WithResource("oidc"),
				router.WithAction("login"),
				router.WithResourceName("OIDC认证"),
				router.WithActionName("登录"),
				router.WithDescription("获取OIDC认证URL"),
			},
			oidcHandler.GetAuthURL)

		public.WithPermission().GET("/auth/oidc/callback",
			[]router.RouteOption{
				router.WithResource("oidc"),
				router.WithAction("callback"),
				router.WithResourceName("OIDC认证"),
				router.WithActionName("回调"),
				router.WithDescription("处理OIDC认证回调"),
			},
			oidcHandler.HandleCallback)

		// 临时测试路由，跳过权限注册
		public.WithPermission().GET("/test/config/oidc",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			systemConfigHandler.GetKeycloakGroupMappings)
	}

	// 需要认证的路由
	authorized := r.Group("", jwtMiddleware.AuthRequired())
	{
		// 用户信息 - 跳过权限注册（所有已认证用户都可访问）
		authorized.WithPermission().GET("/user/info",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			userHandler.GetUserInfo)

		// 新增：当前用户信息（使用新的JWT架构）
		authorized.WithPermission().GET("/auth/me",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			authHandler.GetCurrentUser)

		// 新增：用户会话管理
		sessions := authorized.Group("/auth/sessions")
		{
			// 获取当前用户的会话列表
			sessions.WithPermission().GET("",
				[]router.RouteOption{
					router.SkipPermissionRegistration(),
				},
				authHandler.GetUserSessions)

			// 撤销指定会话
			sessions.WithPermission().DELETE("/:sessionId",
				[]router.RouteOption{
					router.SkipPermissionRegistration(),
				},
				authHandler.RevokeSession)
		}

		// 获取当前用户权限 - 跳过权限注册（所有已认证用户都可访问）
		authorized.WithPermission().GET("/user/permissions",
			[]router.RouteOption{
				router.SkipPermissionRegistration(),
			},
			rbacHandler.GetCurrentUserPermissions)

		// 用户管理 - 需要权限
		userManage := authorized.Group("/users")
		{
			// 列出用户 - 需要查看用户权限
			userManage.WithPermission().GET("",
				[]router.RouteOption{
					router.WithResource("user"),
					router.WithAction("list"),
					router.WithResourceName("用户"),
					router.WithActionName("列表"),
					router.WithDescription("查看用户列表"),
				},
				permissionMiddleware.RequirePermission("api", "system:users", "read"),
				userHandler.ListUsers)

			// 创建用户 - 需要创建用户权限
			userManage.WithPermission().POST("",
				[]router.RouteOption{
					router.WithResource("user"),
					router.WithAction("create"),
					router.WithResourceName("用户"),
					router.WithActionName("创建"),
					router.WithDescription("创建新用户"),
				},
				permissionMiddleware.RequirePermission("api", "system:users", "create"),
				userHandler.CreateUser)

			// 查看单个用户 - 需要查看用户权限
			userManage.WithPermission().GET("/:id",
				[]router.RouteOption{
					router.WithResource("user"),
					router.WithAction("read"),
					router.WithResourceName("用户"),
					router.WithActionName("详情"),
					router.WithDescription("查看用户详情"),
				},
				permissionMiddleware.RequirePermission("api", "system:users", "read"),
				userHandler.GetUser)

			// 更新用户 - 需要更新用户权限
			userManage.WithPermission().PUT("/:id",
				[]router.RouteOption{
					router.WithResource("user"),
					router.WithAction("update"),
					router.WithResourceName("用户"),
					router.WithActionName("更新"),
					router.WithDescription("更新用户信息"),
				},
				permissionMiddleware.RequirePermission("api", "system:users", "update"),
				userHandler.UpdateUser)

			// 删除用户 - 需要删除用户权限
			userManage.WithPermission().DELETE("/:id",
				[]router.RouteOption{
					router.WithResource("user"),
					router.WithAction("delete"),
					router.WithResourceName("用户"),
					router.WithActionName("删除"),
					router.WithDescription("删除用户"),
				},
				permissionMiddleware.RequirePermission("api", "system:users", "delete"),
				userHandler.DeleteUser)
		}

		// 用户组管理 - 独立模块
		userGroups := authorized.Group("/usergroups")
		{
			// 获取用户组列表
			userGroups.WithPermission().GET("",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("list"),
					router.WithResourceName("用户组"),
					router.WithActionName("列表"),
					router.WithDescription("查看用户组列表"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "read"),
				userGroupHandler.ListUserGroups)

			// 创建用户组
			userGroups.WithPermission().POST("",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("create"),
					router.WithResourceName("用户组"),
					router.WithActionName("创建"),
					router.WithDescription("创建新用户组"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "create"),
				userGroupHandler.CreateUserGroup)

			// 获取用户组详情
			userGroups.WithPermission().GET("/:id",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("read"),
					router.WithResourceName("用户组"),
					router.WithActionName("详情"),
					router.WithDescription("查看用户组详情"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "read"),
				userGroupHandler.GetUserGroup)

			// 更新用户组
			userGroups.WithPermission().PUT("/:id",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("更新"),
					router.WithDescription("更新用户组信息"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.UpdateUserGroup)

			// 删除用户组
			userGroups.WithPermission().DELETE("/:id",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("delete"),
					router.WithResourceName("用户组"),
					router.WithActionName("删除"),
					router.WithDescription("删除用户组"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "delete"),
				userGroupHandler.DeleteUserGroup)

			// 获取用户组成员
			userGroups.WithPermission().GET("/:id/members",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("read"),
					router.WithResourceName("用户组"),
					router.WithActionName("成员列表"),
					router.WithDescription("查看用户组成员"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "read"),
				userGroupHandler.GetGroupMembers)

			// 添加用户到用户组
			userGroups.WithPermission().POST("/:id/members",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("添加成员"),
					router.WithDescription("添加用户到用户组"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.AddUserToGroup)

			// 从用户组移除用户
			userGroups.WithPermission().DELETE("/:id/members/:userId",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("移除成员"),
					router.WithDescription("从用户组移除用户"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.RemoveUserFromGroup)

			// 批量更新用户组成员
			userGroups.WithPermission().PUT("/:id/members",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("批量更新成员"),
					router.WithDescription("批量更新用户组成员"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.BatchUpdateGroupMembers)

			// 获取用户组权限
			userGroups.WithPermission().GET("/:id/permissions",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("read"),
					router.WithResourceName("用户组"),
					router.WithActionName("权限列表"),
					router.WithDescription("查看用户组权限"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "read"),
				userGroupHandler.GetGroupPermissions)

			// 分配用户组权限
			userGroups.WithPermission().POST("/:id/permissions",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("分配权限"),
					router.WithDescription("分配用户组权限"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.AssignGroupPermissions)

			// 移除用户组权限
			userGroups.WithPermission().DELETE("/:id/permissions",
				[]router.RouteOption{
					router.WithResource("user-group"),
					router.WithAction("update"),
					router.WithResourceName("用户组"),
					router.WithActionName("移除权限"),
					router.WithDescription("移除用户组权限"),
				},
				permissionMiddleware.RequirePermission("api", "system:user-groups", "update"),
				userGroupHandler.RemoveGroupPermissions)
		}

		// RBAC相关
		rbac := authorized.Group("/rbac")
		{
			// 权限检查
			rbac.WithPermission().POST("/check-permission",
				[]router.RouteOption{
					router.WithResource("permission-check"),
					router.WithAction("check"),
					router.WithResourceName("权限检查"),
					router.WithActionName("检查"),
					router.WithDescription("检查用户权限"),
				},
				rbacHandler.CheckPermission)

			// 权限管理
			permissions := rbac.Group("/permissions")
			{
				// 获取权限列表
				permissions.WithPermission().GET("",
					[]router.RouteOption{
						router.WithResource("permission"),
						router.WithAction("list"),
						router.WithResourceName("权限"),
						router.WithActionName("列表"),
						router.WithDescription("查看权限列表"),
					},
					permissionMiddleware.RequirePermission("api", "system:permissions", "read"),
					rbacHandler.ListPermissions)

				// 创建权限
				permissions.WithPermission().POST("",
					[]router.RouteOption{
						router.WithResource("permission"),
						router.WithAction("create"),
						router.WithResourceName("权限"),
						router.WithActionName("创建"),
						router.WithDescription("创建新权限"),
					},
					permissionMiddleware.RequirePermission("api", "system:permissions", "create"),
					rbacHandler.CreatePermission)

				// 获取权限详情
				permissions.WithPermission().GET("/:id",
					[]router.RouteOption{
						router.WithResource("permission"),
						router.WithAction("read"),
						router.WithResourceName("权限"),
						router.WithActionName("详情"),
						router.WithDescription("查看权限详情"),
					},
					permissionMiddleware.RequirePermission("api", "system:permissions", "read"),
					rbacHandler.GetPermission)

				// 更新权限
				permissions.WithPermission().PUT("/:id",
					[]router.RouteOption{
						router.WithResource("permission"),
						router.WithAction("update"),
						router.WithResourceName("权限"),
						router.WithActionName("更新"),
						router.WithDescription("更新权限信息"),
					},
					permissionMiddleware.RequirePermission("api", "system:permissions", "update"),
					rbacHandler.UpdatePermission)

				// 删除权限
				permissions.WithPermission().DELETE("/:id",
					[]router.RouteOption{
						router.WithResource("permission"),
						router.WithAction("delete"),
						router.WithResourceName("权限"),
						router.WithActionName("删除"),
						router.WithDescription("删除权限"),
					},
					permissionMiddleware.RequirePermission("api", "system:permissions", "delete"),
					rbacHandler.DeletePermission)
			}

			// 获取当前用户权限
			rbac.WithPermission().GET("/current-user/permissions",
				[]router.RouteOption{
					router.WithResource("permission"),
					router.WithAction("read"),
					router.WithResourceName("权限"),
					router.WithActionName("当前用户权限"),
					router.WithDescription("获取当前用户权限"),
				},
				rbacHandler.GetCurrentUserPermissions)

			// 获取用户有效权限
			rbac.WithPermission().GET("/users/:userId/effective-permissions",
				[]router.RouteOption{
					router.WithResource("permission"),
					router.WithAction("read"),
					router.WithResourceName("权限"),
					router.WithActionName("用户有效权限"),
					router.WithDescription("获取用户有效权限"),
				},
				permissionMiddleware.RequirePermission("api", "system:permissions", "read"),
				rbacHandler.GetUserEffectivePermissions)


		}

		// 集群管理 - 需要集群管理权限
		clusters := authorized.Group("/clusters")
		{
			clusters.WithPermission().GET("",
				[]router.RouteOption{
					router.WithResource("cluster"),
					router.WithAction("list"),
					router.WithResourceName("集群"),
					router.WithActionName("列表"),
					router.WithDescription("查看集群列表"),
				},
				permissionMiddleware.RequirePermission("api", "system:clusters", "read"),
				clusterHandler.ListClusters)

			clusters.WithPermission().POST("",
				[]router.RouteOption{
					router.WithResource("cluster"),
					router.WithAction("create"),
					router.WithResourceName("集群"),
					router.WithActionName("创建"),
					router.WithDescription("创建新集群"),
				},
				permissionMiddleware.RequirePermission("api", "system:clusters", "create"),
				clusterHandler.CreateCluster)

			clusters.WithPermission().GET("/:id",
				[]router.RouteOption{
					router.WithResource("cluster"),
					router.WithAction("read"),
					router.WithResourceName("集群"),
					router.WithActionName("详情"),
					router.WithDescription("查看集群详情"),
				},
				permissionMiddleware.RequirePermission("api", "clusters:*", "read"),
				clusterHandler.GetCluster)

			clusters.WithPermission().PUT("/:id",
				[]router.RouteOption{
					router.WithResource("cluster"),
					router.WithAction("update"),
					router.WithResourceName("集群"),
					router.WithActionName("更新"),
					router.WithDescription("更新集群信息"),
				},
				permissionMiddleware.RequirePermission("api", "clusters:*", "update"),
				clusterHandler.UpdateCluster)

			clusters.WithPermission().DELETE("/:id",
				[]router.RouteOption{
					router.WithResource("cluster"),
					router.WithAction("delete"),
					router.WithResourceName("集群"),
					router.WithActionName("删除"),
					router.WithDescription("删除集群"),
				},
				permissionMiddleware.RequirePermission("api", "clusters:*", "delete"),
				clusterHandler.DeleteCluster)
		}

		// 审计相关 - 需要审计查看权限
		audit := authorized.Group("/audit")
		{
			audit.WithPermission().GET("/logs",
				[]router.RouteOption{
					router.WithResource("audit-log"),
					router.WithAction("read"),
					router.WithResourceName("审计日志"),
					router.WithActionName("查看"),
					router.WithDescription("查看审计日志"),
				},
				permissionMiddleware.RequirePermission("api", "system:audit-logs", "read"),
				auditHandler.ListAuditLogs)

			audit.WithPermission().GET("/archives",
				[]router.RouteOption{
					router.WithResource("audit-archive"),
					router.WithAction("read"),
					router.WithResourceName("审计归档"),
					router.WithActionName("查看"),
					router.WithDescription("查看审计归档"),
				},
				permissionMiddleware.RequirePermission("api", "system:audit-archives", "read"),
				auditHandler.ListAuditArchives)
		}

		// 系统配置相关 - 需要系统管理权限
		config := authorized.Group("/system/config")
		{
			config.WithPermission().GET("/basic",
				[]router.RouteOption{
					router.WithResource("config-basic"),
					router.WithAction("read"),
					router.WithResourceName("基本配置"),
					router.WithActionName("查看"),
					router.WithDescription("查看基本配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-basic", "read"),
				systemConfigHandler.GetBasicConfig)

			config.WithPermission().PUT("/basic",
				[]router.RouteOption{
					router.WithResource("config-basic"),
					router.WithAction("update"),
					router.WithResourceName("基本配置"),
					router.WithActionName("更新"),
					router.WithDescription("更新基本配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-basic", "update"),
				systemConfigHandler.UpdateBasicConfig)

			config.WithPermission().GET("/oidc",
				[]router.RouteOption{
					router.WithResource("config-oidc"),
					router.WithAction("read"),
					router.WithResourceName("OIDC配置"),
					router.WithActionName("查看"),
					router.WithDescription("查看OIDC配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-oidc", "read"),
				systemConfigHandler.GetOIDCConfig)

			config.WithPermission().PUT("/oidc",
				[]router.RouteOption{
					router.WithResource("config-oidc"),
					router.WithAction("update"),
					router.WithResourceName("OIDC配置"),
					router.WithActionName("更新"),
					router.WithDescription("更新OIDC配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-oidc", "update"),
				systemConfigHandler.UpdateOIDCConfig)

			config.WithPermission().POST("/oidc/test",
				[]router.RouteOption{
					router.WithResource("config-oidc"),
					router.WithAction("test"),
					router.WithResourceName("OIDC配置"),
					router.WithActionName("测试"),
					router.WithDescription("测试OIDC配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-oidc", "test"),
				systemConfigHandler.TestOIDCConfig)

			// 添加飞书配置路由
			config.WithPermission().GET("/feishu",
				[]router.RouteOption{
					router.WithResource("config-feishu"),
					router.WithAction("read"),
					router.WithResourceName("飞书配置"),
					router.WithActionName("查看"),
					router.WithDescription("查看飞书配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-feishu", "read"),
				systemConfigHandler.GetFeishuConfig)

			config.WithPermission().PUT("/feishu",
				[]router.RouteOption{
					router.WithResource("config-feishu"),
					router.WithAction("update"),
					router.WithResourceName("飞书配置"),
					router.WithActionName("更新"),
					router.WithDescription("更新飞书配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-feishu", "update"),
				systemConfigHandler.UpdateFeishuConfig)

			config.WithPermission().POST("/feishu/test",
				[]router.RouteOption{
					router.WithResource("config-feishu"),
					router.WithAction("test"),
					router.WithResourceName("飞书配置"),
					router.WithActionName("测试"),
					router.WithDescription("测试飞书配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-feishu", "test"),
				systemConfigHandler.TestFeishuConfig)

			// 添加OBS配置路由
			config.WithPermission().GET("/obs",
				[]router.RouteOption{
					router.WithResource("config-obs"),
					router.WithAction("read"),
					router.WithResourceName("对象存储配置"),
					router.WithActionName("查看"),
					router.WithDescription("查看对象存储配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-obs", "read"),
				systemConfigHandler.GetOBSConfig)

			config.WithPermission().PUT("/obs",
				[]router.RouteOption{
					router.WithResource("config-obs"),
					router.WithAction("update"),
					router.WithResourceName("对象存储配置"),
					router.WithActionName("更新"),
					router.WithDescription("更新对象存储配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-obs", "update"),
				systemConfigHandler.UpdateOBSConfig)

			config.WithPermission().POST("/obs/test",
				[]router.RouteOption{
					router.WithResource("config-obs"),
					router.WithAction("test"),
					router.WithResourceName("对象存储配置"),
					router.WithActionName("测试"),
					router.WithDescription("测试对象存储配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-obs", "test"),
				systemConfigHandler.TestOBSConfig)

			// 添加审计配置路由
			config.WithPermission().GET("/audit",
				[]router.RouteOption{
					router.WithResource("config-audit"),
					router.WithAction("read"),
					router.WithResourceName("审计配置"),
					router.WithActionName("查看"),
					router.WithDescription("查看审计配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-audit", "read"),
				systemConfigHandler.GetAuditConfig)

			config.WithPermission().PUT("/audit",
				[]router.RouteOption{
					router.WithResource("config-audit"),
					router.WithAction("update"),
					router.WithResourceName("审计配置"),
					router.WithActionName("更新"),
					router.WithDescription("更新审计配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-audit", "update"),
				systemConfigHandler.UpdateAuditConfig)

			config.WithPermission().GET("/keycloak-group-mappings",
				[]router.RouteOption{
					router.WithResource("config-keycloak"),
					router.WithAction("read"),
					router.WithResourceName("Keycloak组映射"),
					router.WithActionName("查看"),
					router.WithDescription("查看Keycloak组映射配置"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-keycloak", "read"),
				systemConfigHandler.GetKeycloakGroupMappings)

			config.WithPermission().POST("/keycloak-group-mappings",
				[]router.RouteOption{
					router.WithResource("config-keycloak"),
					router.WithAction("create"),
					router.WithResourceName("Keycloak组映射"),
					router.WithActionName("创建"),
					router.WithDescription("创建Keycloak组映射"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-keycloak", "create"),
				systemConfigHandler.CreateKeycloakGroupMapping)

			config.WithPermission().DELETE("/keycloak-group-mappings",
				[]router.RouteOption{
					router.WithResource("config-keycloak"),
					router.WithAction("delete"),
					router.WithResourceName("Keycloak组映射"),
					router.WithActionName("删除"),
					router.WithDescription("删除Keycloak组映射"),
				},
				permissionMiddleware.RequirePermission("api", "system:config-keycloak", "delete"),
				systemConfigHandler.DeleteKeycloakGroupMapping)
		}
	}
}
