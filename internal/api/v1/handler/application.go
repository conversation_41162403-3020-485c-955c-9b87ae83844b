package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"kubeops/internal/model"
	"kubeops/internal/service"
	"kubeops/internal/response"
)

// ApplicationHandler 应用处理器
type ApplicationHandler struct {
	applicationService service.ApplicationService
}

// NewApplicationHandler 创建应用处理器
func NewApplicationHandler(applicationService service.ApplicationService) *ApplicationHandler {
	return &ApplicationHandler{
		applicationService: applicationService,
	}
}

// ListApplications 获取应用列表
func (h *ApplicationHandler) ListApplications(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))
	projectIDStr := c.Query("project_id")

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	var projectID uint
	if projectIDStr != "" {
		if id, err := strconv.ParseUint(projectIDStr, 10, 32); err == nil {
			projectID = uint(id)
		}
	}

	applications, total, err := h.applicationService.ListApplications(c.Request.Context(), page, pageSize, projectID)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"items": applications,
		"total": total,
		"page":  page,
		"size":  pageSize,
	})
}

// GetApplication 获取应用详情
func (h *ApplicationHandler) GetApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	application, err := h.applicationService.GetApplicationByID(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, application)
}

// CreateApplication 创建应用
func (h *ApplicationHandler) CreateApplication(c *gin.Context) {
	var req model.ApplicationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的应用数据")
		return
	}

	// 从上下文获取当前用户ID
	userID, exists := c.Get("user_id")
	if exists {
		req.OwnerID = userID.(uint)
	}

	application, err := h.applicationService.CreateApplication(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, application, response.WithMessage("应用创建成功"))
}

// UpdateApplication 更新应用
func (h *ApplicationHandler) UpdateApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	var req model.ApplicationUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的应用数据")
		return
	}

	application, err := h.applicationService.UpdateApplication(c.Request.Context(), uint(applicationID), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, application, response.WithMessage("应用更新成功"))
}

// DeleteApplication 删除应用
func (h *ApplicationHandler) DeleteApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	err = h.applicationService.DeleteApplication(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用删除成功"))
}

// StartApplicationSync 启动应用同步
func (h *ApplicationHandler) StartApplicationSync(c *gin.Context) {
	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	err = h.applicationService.StartApplicationSync(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用同步启动成功"))
}

// StopApplicationSync 停止应用同步
func (h *ApplicationHandler) StopApplicationSync(c *gin.Context) {
	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	err = h.applicationService.StopApplicationSync(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用同步停止成功"))
}

// DiscoverApplicationsInCluster 在集群中发现应用
func (h *ApplicationHandler) DiscoverApplicationsInCluster(c *gin.Context) {
	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	projectIDStr := c.Query("project_id")
	var projectID uint
	if projectIDStr != "" {
		if id, err := strconv.ParseUint(projectIDStr, 10, 32); err == nil {
			projectID = uint(id)
		}
	}

	summary, err := h.applicationService.DiscoverApplicationsInCluster(c.Request.Context(), uint(clusterID), projectID)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, summary, response.WithMessage("应用发现完成"))
}

// SyncWorkloadToApplication 手动同步工作负载到应用
func (h *ApplicationHandler) SyncWorkloadToApplication(c *gin.Context) {
	var req struct {
		ClusterID    uint   `json:"cluster_id" binding:"required"`
		Namespace    string `json:"namespace" binding:"required"`
		WorkloadType string `json:"workload_type" binding:"required"`
		WorkloadName string `json:"workload_name" binding:"required"`
		EventType    string `json:"event_type" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	err := h.applicationService.SyncWorkloadToApplication(
		c.Request.Context(),
		req.ClusterID,
		req.Namespace,
		req.WorkloadType,
		req.WorkloadName,
		req.EventType,
	)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("工作负载同步成功"))
}

// GetIncompleteApplications 获取待完善的应用
func (h *ApplicationHandler) GetIncompleteApplications(c *gin.Context) {
	projectIDStr := c.Query("project_id")
	var projectID uint
	if projectIDStr != "" {
		if id, err := strconv.ParseUint(projectIDStr, 10, 32); err == nil {
			projectID = uint(id)
		}
	}

	applications, err := h.applicationService.GetIncompleteApplications(c.Request.Context(), projectID)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, applications)
}

// CheckDataConsistency 检查数据一致性
func (h *ApplicationHandler) CheckDataConsistency(c *gin.Context) {
	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	report, err := h.applicationService.CheckDataConsistency(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, report)
}

// ReconcileApplications 协调应用数据
func (h *ApplicationHandler) ReconcileApplications(c *gin.Context) {
	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	err = h.applicationService.ReconcileApplications(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用数据协调完成"))
}

// CompleteApplicationInfo 完善应用信息
func (h *ApplicationHandler) CompleteApplicationInfo(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	var req model.ApplicationCompleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	application, err := h.applicationService.CompleteApplicationInfo(c.Request.Context(), uint(applicationID), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, application, response.WithMessage("应用信息完善成功"))
}

// RefreshApplicationStatus 刷新应用状态
func (h *ApplicationHandler) RefreshApplicationStatus(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	// 获取应用的集群部署信息
	appClusters, err := h.applicationService.GetApplicationClusters(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	// 对每个集群进行数据一致性检查
	for _, appCluster := range appClusters {
		// 需要通过ProjectCluster获取实际的ClusterID
		// 这里简化处理，假设ProjectClusterID就是ClusterID
		_, err = h.applicationService.CheckDataConsistency(c.Request.Context(), appCluster.ProjectClusterID)
		if err != nil {
			// 记录错误但继续处理其他集群
			continue
		}
	}
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用状态刷新成功"))
}

// ActivateApplication 激活应用
func (h *ApplicationHandler) ActivateApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	err = h.applicationService.ActivateApplication(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用激活成功"))
}

// DeactivateApplication 停用应用
func (h *ApplicationHandler) DeactivateApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	err = h.applicationService.DeactivateApplication(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用停用成功"))
}

// ArchiveApplication 归档应用
func (h *ApplicationHandler) ArchiveApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	err = h.applicationService.ArchiveApplication(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用归档成功"))
}

// UnarchiveApplication 取消归档应用
func (h *ApplicationHandler) UnarchiveApplication(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	err = h.applicationService.UnarchiveApplication(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用取消归档成功"))
}

// DeployToCluster 部署到集群
func (h *ApplicationHandler) DeployToCluster(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	var req struct {
		ProjectClusterID uint                              `json:"project_cluster_id" binding:"required"`
		Config           *model.ApplicationClusterConfig  `json:"config"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	applicationCluster, err := h.applicationService.DeployToCluster(c.Request.Context(), uint(applicationID), req.ProjectClusterID, req.Config)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, applicationCluster, response.WithMessage("应用部署成功"))
}

// RemoveFromCluster 从集群移除
func (h *ApplicationHandler) RemoveFromCluster(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	projectClusterIDStr := c.Param("project_cluster_id")
	projectClusterID, err := strconv.ParseUint(projectClusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目集群ID")
		return
	}

	err = h.applicationService.RemoveFromCluster(c.Request.Context(), uint(applicationID), uint(projectClusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用从集群移除成功"))
}

// GetApplicationClusters 获取应用的集群部署信息
func (h *ApplicationHandler) GetApplicationClusters(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	clusters, err := h.applicationService.GetApplicationClusters(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, clusters)
}

// GetApplicationMembers 获取应用成员
func (h *ApplicationHandler) GetApplicationMembers(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	members, err := h.applicationService.GetApplicationMembers(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, members)
}

// AddApplicationMember 添加应用成员
func (h *ApplicationHandler) AddApplicationMember(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	var req model.ApplicationMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	err = h.applicationService.AddApplicationMember(c.Request.Context(), uint(applicationID), req.UserID, req.Role)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用成员添加成功"))
}

// RemoveApplicationMember 移除应用成员
func (h *ApplicationHandler) RemoveApplicationMember(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.applicationService.RemoveApplicationMember(c.Request.Context(), uint(applicationID), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用成员移除成功"))
}

// UpdateApplicationMemberRole 更新应用成员角色
func (h *ApplicationHandler) UpdateApplicationMemberRole(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	var req struct {
		Role string `json:"role" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	err = h.applicationService.UpdateApplicationMemberRole(c.Request.Context(), uint(applicationID), uint(userID), req.Role)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("应用成员角色更新成功"))
}

// GetApplicationStats 获取应用统计
func (h *ApplicationHandler) GetApplicationStats(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	stats, err := h.applicationService.GetApplicationStats(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, stats)
}

// GetProjectApplicationStats 获取项目应用统计
func (h *ApplicationHandler) GetProjectApplicationStats(c *gin.Context) {
	projectIDStr := c.Param("project_id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	stats, err := h.applicationService.GetProjectApplicationStats(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, stats)
}

// GetApplicationHealth 获取应用健康状态
func (h *ApplicationHandler) GetApplicationHealth(c *gin.Context) {
	applicationIDStr := c.Param("id")
	applicationID, err := strconv.ParseUint(applicationIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的应用ID")
		return
	}

	health, err := h.applicationService.GetApplicationHealth(c.Request.Context(), uint(applicationID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, health)
}

// BatchRefreshApplicationStatus 批量刷新应用状态
func (h *ApplicationHandler) BatchRefreshApplicationStatus(c *gin.Context) {
	projectIDStr := c.Param("project_id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	// 获取项目下的应用列表，然后对每个集群进行数据一致性检查
	applications, _, err := h.applicationService.ListApplications(c.Request.Context(), 1, 1000, uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	// 收集所有集群ID并去重
	clusterIDs := make(map[uint]bool)
	for _, app := range applications {
		// 获取应用的集群部署信息
		appClusters, err := h.applicationService.GetApplicationClusters(c.Request.Context(), app.ID)
		if err != nil {
			continue
		}
		for _, appCluster := range appClusters {
			clusterIDs[appCluster.ProjectClusterID] = true
		}
	}

	// 对每个集群进行数据一致性检查
	for clusterID := range clusterIDs {
		_, err = h.applicationService.CheckDataConsistency(c.Request.Context(), clusterID)
		if err != nil {
			// 记录错误但继续处理其他集群
			continue
		}
	}
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("批量应用状态刷新成功"))
}
