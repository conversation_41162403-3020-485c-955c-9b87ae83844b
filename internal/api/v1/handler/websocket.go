package handler

import (
	"context"
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/response"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	redisClient *redis.Client
	logger      *zap.Logger
	upgrader    websocket.Upgrader
	clients     map[*websocket.Conn]*Client
	clientsMux  sync.RWMutex
}

// Client WebSocket客户端
type Client struct {
	conn      *websocket.Conn
	userID    uint
	projectID uint
	clusterID uint
	send      chan []byte
}

// WebSocketMessage WebSocket消息
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(redisClient *redis.Client, logger *zap.Logger) *WebSocketHandler {
	return &WebSocketHandler{
		redisClient: redisClient,
		logger:      logger,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
		},
		clients: make(map[*websocket.Conn]*Client),
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.Error("Failed to upgrade websocket", zap.Error(err))
		response.ServerError(c, err)
		return
	}
	defer conn.Close()

	// 获取用户信息
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User not authenticated for websocket")
		conn.WriteMessage(websocket.CloseMessage, []byte("Unauthorized"))
		return
	}

	// 创建客户端
	client := &Client{
		conn:   conn,
		userID: userID.(uint),
		send:   make(chan []byte, 256),
	}

	// 注册客户端
	h.registerClient(client)
	defer h.unregisterClient(client)

	// 启动消息处理协程
	go h.writePump(client)
	go h.readPump(client)

	// 启动Redis Stream监听
	go h.listenRedisStream(client)

	// 保持连接
	select {}
}

// registerClient 注册客户端
func (h *WebSocketHandler) registerClient(client *Client) {
	h.clientsMux.Lock()
	defer h.clientsMux.Unlock()
	h.clients[client.conn] = client
	h.logger.Info("WebSocket client registered", zap.Uint("user_id", client.userID))
}

// unregisterClient 注销客户端
func (h *WebSocketHandler) unregisterClient(client *Client) {
	h.clientsMux.Lock()
	defer h.clientsMux.Unlock()
	if _, ok := h.clients[client.conn]; ok {
		delete(h.clients, client.conn)
		close(client.send)
		h.logger.Info("WebSocket client unregistered", zap.Uint("user_id", client.userID))
	}
}

// readPump 读取客户端消息
func (h *WebSocketHandler) readPump(client *Client) {
	defer client.conn.Close()

	client.conn.SetReadLimit(512)
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := client.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				h.logger.Error("WebSocket error", zap.Error(err))
			}
			break
		}

		// 处理客户端消息（如订阅特定事件）
		h.handleClientMessage(client, message)
	}
}

// writePump 向客户端发送消息
func (h *WebSocketHandler) writePump(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := client.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的消息
			n := len(client.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-client.send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage 处理客户端消息
func (h *WebSocketHandler) handleClientMessage(client *Client, message []byte) {
	var msg map[string]interface{}
	if err := json.Unmarshal(message, &msg); err != nil {
		h.logger.Error("Failed to unmarshal client message", zap.Error(err))
		return
	}

	msgType, ok := msg["type"].(string)
	if !ok {
		return
	}

	switch msgType {
	case "subscribe_project":
		if projectID, ok := msg["project_id"].(float64); ok {
			client.projectID = uint(projectID)
			h.logger.Info("Client subscribed to project", 
				zap.Uint("user_id", client.userID),
				zap.Uint("project_id", client.projectID),
			)
		}
	case "subscribe_cluster":
		if clusterID, ok := msg["cluster_id"].(float64); ok {
			client.clusterID = uint(clusterID)
			h.logger.Info("Client subscribed to cluster", 
				zap.Uint("user_id", client.userID),
				zap.Uint("cluster_id", client.clusterID),
			)
		}
	}
}

// listenRedisStream 监听Redis Stream
func (h *WebSocketHandler) listenRedisStream(client *Client) {
	ctx := context.Background()
	streamKey := "workload_events"

	for {
		// 读取Redis Stream
		streams, err := h.redisClient.XRead(ctx, &redis.XReadArgs{
			Streams: []string{streamKey, "$"},
			Block:   0, // 阻塞等待
		}).Result()

		if err != nil {
			h.logger.Error("Failed to read Redis stream", zap.Error(err))
			time.Sleep(5 * time.Second)
			continue
		}

		for _, stream := range streams {
			for _, message := range stream.Messages {
				h.processStreamMessage(client, message)
			}
		}
	}
}

// processStreamMessage 处理Stream消息
func (h *WebSocketHandler) processStreamMessage(client *Client, message redis.XMessage) {
	eventData, ok := message.Values["event"].(string)
	if !ok {
		return
	}

	var workloadEvent model.WorkloadEvent
	if err := json.Unmarshal([]byte(eventData), &workloadEvent); err != nil {
		h.logger.Error("Failed to unmarshal workload event", zap.Error(err))
		return
	}

	// 检查客户端是否订阅了相关事件
	if client.clusterID != 0 && client.clusterID != workloadEvent.ClusterID {
		return
	}

	// 创建WebSocket消息
	wsMessage := WebSocketMessage{
		Type:      "workload_event",
		Data:      workloadEvent,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(wsMessage)
	if err != nil {
		h.logger.Error("Failed to marshal websocket message", zap.Error(err))
		return
	}

	// 发送消息到客户端
	select {
	case client.send <- messageBytes:
	default:
		// 客户端发送队列已满，关闭连接
		h.unregisterClient(client)
	}
}

// BroadcastApplicationEvent 广播应用事件
func (h *WebSocketHandler) BroadcastApplicationEvent(eventType string, data interface{}) {
	message := WebSocketMessage{
		Type:      eventType,
		Data:      data,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		h.logger.Error("Failed to marshal broadcast message", zap.Error(err))
		return
	}

	h.clientsMux.RLock()
	defer h.clientsMux.RUnlock()

	for _, client := range h.clients {
		select {
		case client.send <- messageBytes:
		default:
			// 客户端发送队列已满，跳过
		}
	}
}

// GetConnectedClients 获取连接的客户端数量
func (h *WebSocketHandler) GetConnectedClients() int {
	h.clientsMux.RLock()
	defer h.clientsMux.RUnlock()
	return len(h.clients)
}
