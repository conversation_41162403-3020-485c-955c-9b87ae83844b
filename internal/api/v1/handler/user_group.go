package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/response"
	"kubeops/internal/service"
)

// UserGroupHandler 用户组管理处理器
// 专门负责用户组的CRUD操作和成员管理
type UserGroupHandler struct {
	userGroupService service.UserGroupService
	casbinService    *service.CasbinService // 用于权限相关操作
	logger           *zap.Logger
	tracer           trace.Tracer
}

// NewUserGroupHandler 创建用户组处理器
func NewUserGroupHandler(userGroupService service.UserGroupService, casbinService *service.CasbinService, logger *zap.Logger, tracer trace.Tracer) *UserGroupHandler {
	return &UserGroupHandler{
		userGroupService: userGroupService,
		casbinService:    casbinService,
		logger:           logger,
		tracer:           tracer,
	}
}

// ==================== 用户组CRUD操作 ====================

// ListUserGroups 获取用户组列表
func (h *UserGroupHandler) ListUserGroups(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.ListUserGroups")
	defer span.End()

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("page_size", pageSize),
	)

	groups, total, err := h.userGroupService.ListUserGroups(ctx, page, pageSize)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户组列表失败", zap.Error(err))
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"items":     groups,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetUserGroup 获取用户组详情
func (h *UserGroupHandler) GetUserGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.GetUserGroup")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	span.SetAttributes(attribute.Int64("group_id", int64(groupID)))

	group, err := h.userGroupService.GetUserGroupByID(ctx, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户组失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, group)
}

// CreateUserGroup 创建用户组
func (h *UserGroupHandler) CreateUserGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.CreateUserGroup")
	defer span.End()

	var req model.UserGroupCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.String("group_name", req.Name),
		attribute.String("domain", req.Domain),
	)

	group, err := h.userGroupService.CreateUserGroup(ctx, &req)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("创建用户组失败", zap.Error(err), zap.Any("request", req))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户组创建成功", 
		zap.String("group_name", group.Name),
		zap.Uint("group_id", group.ID))

	response.Success(c, group, response.WithMessage("用户组创建成功"))
}

// UpdateUserGroup 更新用户组
func (h *UserGroupHandler) UpdateUserGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.UpdateUserGroup")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	var req model.UserGroupUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.String("domain", req.Domain),
	)

	group, err := h.userGroupService.UpdateUserGroup(ctx, uint(groupID), &req)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("更新用户组失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户组更新成功", 
		zap.Uint64("group_id", groupID),
		zap.String("group_name", group.Name))

	response.Success(c, group, response.WithMessage("用户组更新成功"))
}

// DeleteUserGroup 删除用户组
func (h *UserGroupHandler) DeleteUserGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.DeleteUserGroup")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	span.SetAttributes(attribute.Int64("group_id", int64(groupID)))

	err = h.userGroupService.DeleteUserGroup(ctx, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("删除用户组失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户组删除成功", zap.Uint64("group_id", groupID))

	response.Success(c, nil, response.WithMessage("用户组删除成功"))
}

// ==================== 用户组成员管理 ====================

// GetGroupMembers 获取用户组成员
func (h *UserGroupHandler) GetGroupMembers(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.GetGroupMembers")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	span.SetAttributes(attribute.Int64("group_id", int64(groupID)))

	members, err := h.userGroupService.GetGroupMembers(ctx, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户组成员失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, members)
}

// AddUserToGroup 添加用户到用户组
func (h *UserGroupHandler) AddUserToGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.AddUserToGroup")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	var req struct {
		UserID uint `json:"user_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.Int64("user_id", int64(req.UserID)),
	)

	err = h.userGroupService.AddUserToGroup(ctx, req.UserID, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("添加用户到用户组失败",
			zap.Error(err),
			zap.Uint64("groupId", groupID),
			zap.Uint("userId", req.UserID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户添加到用户组成功",
		zap.Uint64("group_id", groupID),
		zap.Uint("user_id", req.UserID))

	response.Success(c, nil, response.WithMessage("用户添加到用户组成功"))
}

// RemoveUserFromGroup 从用户组移除用户
func (h *UserGroupHandler) RemoveUserFromGroup(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.RemoveUserFromGroup")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户ID", zap.Error(err), zap.String("userId", userIDStr))
		response.BadRequest(c, "无效的用户ID")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.Int64("user_id", int64(userID)),
	)

	err = h.userGroupService.RemoveUserFromGroup(ctx, uint(userID), uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("从用户组移除用户失败",
			zap.Error(err),
			zap.Uint64("groupId", groupID),
			zap.Uint64("userId", userID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("从用户组移除用户成功",
		zap.Uint64("group_id", groupID),
		zap.Uint64("user_id", userID))

	response.Success(c, nil, response.WithMessage("从用户组移除用户成功"))
}

// BatchUpdateGroupMembers 批量更新用户组成员
func (h *UserGroupHandler) BatchUpdateGroupMembers(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.BatchUpdateGroupMembers")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	var req struct {
		UserIDs []uint `json:"user_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.Int("user_count", len(req.UserIDs)),
	)

	// 获取当前用户组成员
	currentMembers, err := h.userGroupService.GetGroupMembers(ctx, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取当前用户组成员失败", zap.Error(err))
		response.ServerError(c, err)
		return
	}

	// 构建当前成员ID集合
	currentMemberIDs := make(map[uint]bool)
	for _, member := range currentMembers {
		currentMemberIDs[member.ID] = true
	}

	// 构建新成员ID集合
	newMemberIDs := make(map[uint]bool)
	for _, userID := range req.UserIDs {
		newMemberIDs[userID] = true
	}

	// 添加新成员
	for userID := range newMemberIDs {
		if !currentMemberIDs[userID] {
			err := h.userGroupService.AddUserToGroup(ctx, userID, uint(groupID))
			if err != nil {
				h.logger.Error("添加用户到用户组失败",
					zap.Error(err),
					zap.Uint64("groupId", groupID),
					zap.Uint("userId", userID))
				continue
			}
		}
	}

	// 移除不在新列表中的成员
	for _, member := range currentMembers {
		if !newMemberIDs[member.ID] {
			err := h.userGroupService.RemoveUserFromGroup(ctx, member.ID, uint(groupID))
			if err != nil {
				h.logger.Error("从用户组移除用户失败",
					zap.Error(err),
					zap.Uint64("groupId", groupID),
					zap.Uint("userId", member.ID))
				continue
			}
		}
	}

	h.logger.Info("批量更新用户组成员成功",
		zap.Uint64("group_id", groupID),
		zap.Int("user_count", len(req.UserIDs)))

	response.Success(c, nil, response.WithMessage("批量更新用户组成员成功"))
}

// ==================== 用户组权限管理 ====================

// GetGroupPermissions 获取用户组权限
func (h *UserGroupHandler) GetGroupPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.GetGroupPermissions")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	span.SetAttributes(attribute.Int64("group_id", int64(groupID)))

	permissions, err := h.userGroupService.GetGroupPermissions(ctx, uint(groupID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户组权限失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, permissions)
}

// AssignGroupPermissions 分配用户组权限
func (h *UserGroupHandler) AssignGroupPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.AssignGroupPermissions")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.Int("permission_count", len(req.PermissionIDs)),
	)

	err = h.userGroupService.AssignPermissionsToGroup(ctx, uint(groupID), req.PermissionIDs)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("分配用户组权限失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户组权限分配成功",
		zap.Uint64("group_id", groupID),
		zap.Int("permission_count", len(req.PermissionIDs)))

	response.Success(c, nil, response.WithMessage("用户组权限分配成功"))
}

// RemoveGroupPermissions 移除用户组权限
func (h *UserGroupHandler) RemoveGroupPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "UserGroupHandler.RemoveGroupPermissions")
	defer span.End()

	groupIDStr := c.Param("id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户组ID", zap.Error(err), zap.String("groupId", groupIDStr))
		response.BadRequest(c, "无效的用户组ID")
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("group_id", int64(groupID)),
		attribute.Int("permission_count", len(req.PermissionIDs)),
	)

	err = h.userGroupService.RemovePermissionsFromGroup(ctx, uint(groupID), req.PermissionIDs)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("移除用户组权限失败", zap.Error(err), zap.Uint64("groupId", groupID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("用户组权限移除成功",
		zap.Uint64("group_id", groupID),
		zap.Int("permission_count", len(req.PermissionIDs)))

	response.Success(c, nil, response.WithMessage("用户组权限移除成功"))
}
