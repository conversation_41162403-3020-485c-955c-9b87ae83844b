package handler

import (
	"errors"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/response"
	"kubeops/internal/service"
)

// SystemConfigHandler 系统配置处理器
type SystemConfigHandler struct {
	log             *zap.Logger
	configSvc       service.ConfigService
	userGroupSvc    service.UserGroupService
	systemConfigSvc service.SystemConfigService // 添加系统配置服务
}

// NewSystemConfigHandler 创建系统配置处理器
func NewSystemConfigHandler(
	log *zap.Logger,
	configSvc service.ConfigService,
	userGroupSvc service.UserGroupService,
	systemConfigSvc service.SystemConfigService, // 添加系统配置服务参数
) *SystemConfigHandler {
	return &SystemConfigHandler{
		log:             log,
		configSvc:       configSvc,
		userGroupSvc:    userGroupSvc,
		systemConfigSvc: systemConfigSvc, // 初始化系统配置服务
	}
}

// KeycloakGroupMapping Keycloak组映射请求结构
type KeycloakGroupMapping struct {
	KeycloakGroup string `json:"keycloak_group" binding:"required"` // Keycloak组路径
	PlatformGroup string `json:"platform_group" binding:"required"` // 平台用户组名称
}

// GetKeycloakGroupMappings 获取Keycloak组映射
// @Summary 获取Keycloak组映射配置
// @Description 获取Keycloak组到平台用户组的映射配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=[]KeycloakGroupMapping}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/keycloak-group-mappings [get]
func (h *SystemConfigHandler) GetKeycloakGroupMappings(c *gin.Context) {
	mappings, err := h.configSvc.GetKeycloakGroupMappings(c.Request.Context())
	if err != nil {
		h.log.Error("获取Keycloak组映射失败", zap.Error(err))
		response.ServerError(c, errors.New("获取映射配置失败"))
		return
	}

	response.Success(c, mappings)
}

// CreateKeycloakGroupMapping 创建Keycloak组映射
// @Summary 创建Keycloak组映射配置
// @Description 创建新的Keycloak组到平台用户组的映射配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param mapping body KeycloakGroupMapping true "映射配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/keycloak-group-mappings [post]
func (h *SystemConfigHandler) CreateKeycloakGroupMapping(c *gin.Context) {
	var mapping KeycloakGroupMapping
	if err := c.ShouldBindJSON(&mapping); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	// 检查平台用户组是否存在
	_, err := h.userGroupSvc.GetUserGroupByName(c.Request.Context(), mapping.PlatformGroup)
	if err != nil {
		// 如果用户组不存在，则创建
		groupReq := &model.UserGroupCreateRequest{
			Name:        mapping.PlatformGroup,
			Description: "从Keycloak映射创建: " + mapping.KeycloakGroup,
			Type:        "keycloak",
		}
		_, err = h.userGroupSvc.CreateUserGroup(c.Request.Context(), groupReq)
		if err != nil {
			h.log.Error("创建用户组失败",
				zap.String("group_name", mapping.PlatformGroup),
				zap.Error(err))
			response.ServerError(c, errors.New("创建用户组失败"))
			return
		}
	}

	// 创建映射配置
	if err := h.configSvc.CreateKeycloakGroupMapping(c.Request.Context(), mapping.KeycloakGroup, mapping.PlatformGroup); err != nil {
		h.log.Error("创建Keycloak组映射失败", zap.Error(err))
		response.ServerError(c, errors.New("创建映射配置失败"))
		return
	}

	response.Success(c, nil)
}

// DeleteKeycloakGroupMapping 删除Keycloak组映射
// @Summary 删除Keycloak组映射配置
// @Description 删除指定的Keycloak组映射配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param keycloak_group query string true "Keycloak组路径"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/keycloak-group-mappings [delete]
func (h *SystemConfigHandler) DeleteKeycloakGroupMapping(c *gin.Context) {
	keycloakGroup := c.Query("keycloak_group")
	if keycloakGroup == "" {
		response.BadRequest(c, "缺少Keycloak组参数")
		return
	}

	if err := h.configSvc.DeleteKeycloakGroupMapping(c.Request.Context(), keycloakGroup); err != nil {
		h.log.Error("删除Keycloak组映射失败", zap.Error(err))
		response.ServerError(c, errors.New("删除映射配置失败"))
		return
	}

	response.Success(c, nil)
}

// GetBasicConfig 获取基本系统配置
// @Summary 获取基本系统配置
// @Description 获取系统名称、Logo等基本配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=model.SystemBasicConfig}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/basic [get]
func (h *SystemConfigHandler) GetBasicConfig(c *gin.Context) {
	config, err := h.systemConfigSvc.GetBasicConfig(c.Request.Context())
	if err != nil {
		h.log.Error("获取基本系统配置失败", zap.Error(err))
		response.ServerError(c, errors.New("获取系统配置失败"))
		return
	}

	response.Success(c, config)
}

// UpdateBasicConfig 更新基本系统配置
// @Summary 更新基本系统配置
// @Description 更新系统名称、Logo等基本配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemBasicConfig true "基本配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/basic [put]
func (h *SystemConfigHandler) UpdateBasicConfig(c *gin.Context) {
	var config model.SystemBasicConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.UpdateBasicConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("更新基本系统配置失败", zap.Error(err))
		response.ServerError(c, errors.New("更新系统配置失败"))
		return
	}

	response.Success(c, nil)
}

// GetOIDCConfig 获取OIDC配置
// @Summary 获取OIDC配置
// @Description 获取OIDC认证相关配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=model.SystemOIDCConfig}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/oidc [get]
func (h *SystemConfigHandler) GetOIDCConfig(c *gin.Context) {
	config, err := h.systemConfigSvc.GetOIDCConfig(c.Request.Context())
	if err != nil {
		h.log.Error("获取OIDC配置失败", zap.Error(err))
		response.ServerError(c, errors.New("获取OIDC配置失败"))
		return
	}

	// 掩码处理敏感信息
	config.ClientSecret = service.MaskSensitiveData(config.ClientSecret)

	response.Success(c, config)
}

// UpdateOIDCConfig 更新OIDC配置
// @Summary 更新OIDC配置
// @Description 更新OIDC认证相关配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemOIDCConfig true "OIDC配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/oidc [put]
func (h *SystemConfigHandler) UpdateOIDCConfig(c *gin.Context) {
	var config model.SystemOIDCConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.UpdateOIDCConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("更新OIDC配置失败", zap.Error(err))
		response.ServerError(c, errors.New("更新OIDC配置失败"))
		return
	}

	response.Success(c, nil)
}

// TestOIDCConfig 测试OIDC配置
// @Summary 测试OIDC配置连接
// @Description 测试OIDC配置是否有效
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemOIDCConfig true "OIDC配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/oidc/test [post]
func (h *SystemConfigHandler) TestOIDCConfig(c *gin.Context) {
	var config model.SystemOIDCConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.TestOIDCConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("测试OIDC配置失败", zap.Error(err))
		response.Fail(c, response.CodeInvalidParams, nil, response.WithMessage("OIDC配置测试失败: "+err.Error()))
		return
	}

	response.Success(c, gin.H{"message": "OIDC配置测试成功"})
}

// GetFeishuConfig 获取飞书配置
// @Summary 获取飞书配置
// @Description 获取飞书集成相关配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=model.SystemFeishuConfig}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/feishu [get]
func (h *SystemConfigHandler) GetFeishuConfig(c *gin.Context) {
	config, err := h.systemConfigSvc.GetFeishuConfig(c.Request.Context())
	if err != nil {
		h.log.Error("获取飞书配置失败", zap.Error(err))
		response.ServerError(c, errors.New("获取飞书配置失败"))
		return
	}

	// 掩码处理敏感信息
	config.AppSecret = service.MaskSensitiveData(config.AppSecret)

	response.Success(c, config)
}

// UpdateFeishuConfig 更新飞书配置
// @Summary 更新飞书配置
// @Description 更新飞书集成相关配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemFeishuConfig true "飞书配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/feishu [put]
func (h *SystemConfigHandler) UpdateFeishuConfig(c *gin.Context) {
	var config model.SystemFeishuConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.UpdateFeishuConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("更新飞书配置失败", zap.Error(err))
		response.ServerError(c, errors.New("更新飞书配置失败"))
		return
	}

	response.Success(c, nil)
}

// TestFeishuConfig 测试飞书配置
// @Summary 测试飞书配置连接
// @Description 测试飞书配置是否有效
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemFeishuConfig true "飞书配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/feishu/test [post]
func (h *SystemConfigHandler) TestFeishuConfig(c *gin.Context) {
	var config model.SystemFeishuConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.TestFeishuConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("测试飞书配置失败", zap.Error(err))
		response.Fail(c, response.CodeInvalidParams, nil, response.WithMessage("飞书配置测试失败: "+err.Error()))
		return
	}

	response.Success(c, gin.H{"message": "飞书配置测试成功"})
}

// GetOBSConfig 获取对象存储配置
// @Summary 获取对象存储配置
// @Description 获取对象存储相关配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=model.SystemOBSConfig}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/obs [get]
func (h *SystemConfigHandler) GetOBSConfig(c *gin.Context) {
	config, err := h.systemConfigSvc.GetOBSConfig(c.Request.Context())
	if err != nil {
		h.log.Error("获取对象存储配置失败", zap.Error(err))
		response.ServerError(c, errors.New("获取对象存储配置失败"))
		return
	}

	// 掩码处理敏感信息
	config.SecretKey = service.MaskSensitiveData(config.SecretKey)
	config.EncryptionKey = service.MaskSensitiveData(config.EncryptionKey)

	response.Success(c, config)
}

// UpdateOBSConfig 更新对象存储配置
// @Summary 更新对象存储配置
// @Description 更新对象存储相关配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemOBSConfig true "对象存储配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/obs [put]
func (h *SystemConfigHandler) UpdateOBSConfig(c *gin.Context) {
	var config model.SystemOBSConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.UpdateOBSConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("更新对象存储配置失败", zap.Error(err))
		response.ServerError(c, errors.New("更新对象存储配置失败"))
		return
	}

	response.Success(c, nil)
}

// TestOBSConfig 测试对象存储配置
// @Summary 测试对象存储配置连接
// @Description 测试对象存储配置是否有效
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemOBSConfig true "对象存储配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/obs/test [post]
func (h *SystemConfigHandler) TestOBSConfig(c *gin.Context) {
	var config model.SystemOBSConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.TestOBSConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("测试对象存储配置失败", zap.Error(err))
		response.Fail(c, response.CodeInvalidParams, nil, response.WithMessage("对象存储配置测试失败: "+err.Error()))
		return
	}

	response.Success(c, gin.H{"message": "对象存储配置测试成功"})
}

// GetAuditConfig 获取审计配置
// @Summary 获取审计配置
// @Description 获取审计日志相关配置
// @Tags 系统配置
// @Produce json
// @Success 200 {object} response.Response{data=model.SystemAuditConfig}
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/audit [get]
func (h *SystemConfigHandler) GetAuditConfig(c *gin.Context) {
	config, err := h.systemConfigSvc.GetAuditConfig(c.Request.Context())
	if err != nil {
		h.log.Error("获取审计配置失败", zap.Error(err))
		response.ServerError(c, errors.New("获取审计配置失败"))
		return
	}

	response.Success(c, config)
}

// UpdateAuditConfig 更新审计配置
// @Summary 更新审计配置
// @Description 更新审计日志相关配置
// @Tags 系统配置
// @Accept json
// @Produce json
// @Param config body model.SystemAuditConfig true "审计配置"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/system/config/audit [put]
func (h *SystemConfigHandler) UpdateAuditConfig(c *gin.Context) {
	var config model.SystemAuditConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c, "无效的请求参数")
		return
	}

	if err := h.systemConfigSvc.UpdateAuditConfig(c.Request.Context(), &config); err != nil {
		h.log.Error("更新审计配置失败", zap.Error(err))
		response.ServerError(c, errors.New("更新审计配置失败"))
		return
	}

	response.Success(c, nil)
}
