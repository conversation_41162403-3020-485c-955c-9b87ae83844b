package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/response"
	"kubeops/internal/service"
)

// RBACHandler 权限检查和权限资源管理处理器
// 专门负责：
// - 权限检查
// - 权限资源管理（CRUD）
// 注意：用户组管理已移至独立的UserGroupHandler
type RBACHandler struct {
	rbacService service.RBACService
	logger      *zap.Logger
	tracer      trace.Tracer
}

// NewRBACHandler 创建RBAC处理器
func NewRBACHandler(rbacService service.RBACService, logger *zap.Logger, tracer trace.Tracer) *RBACHandler {
	return &RBACHandler{
		rbacService: rbacService,
		logger:      logger,
		tracer:      tracer,
	}
}

// ==================== 权限检查模块 ====================

// CheckPermission 检查权限
func (h *RBACHandler) CheckPermission(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.CheckPermission")
	defer span.End()

	var req struct {
		Resource string `json:"resource" binding:"required"`
		Action   string `json:"action" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	span.SetAttributes(
		attribute.String("resource", req.Resource),
		attribute.String("action", req.Action),
		attribute.Int64("user_id", int64(userID.(uint))),
	)

	allowed, err := h.rbacService.CheckPermission(ctx, userID.(uint), req.Resource, req.Action)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("权限检查失败", zap.Error(err))
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"allowed": allowed,
	})
}

// GetCurrentUserPermissions 获取当前用户权限
func (h *RBACHandler) GetCurrentUserPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.GetCurrentUserPermissions")
	defer span.End()

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}

	span.SetAttributes(attribute.Int64("user_id", int64(userID.(uint))))

	permissions, err := h.rbacService.GetUserPermissions(ctx, userID.(uint))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户权限失败", zap.Error(err), zap.Any("user_id", userID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, permissions)
}

// GetUserEffectivePermissions 获取用户有效权限（包括通过用户组继承的权限）
func (h *RBACHandler) GetUserEffectivePermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.GetUserEffectivePermissions")
	defer span.End()

	userIDStr := c.Param("userId")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的用户ID", zap.Error(err), zap.String("userId", userIDStr))
		response.BadRequest(c, "无效的用户ID")
		return
	}

	span.SetAttributes(attribute.Int64("user_id", int64(userID)))

	permissions, err := h.rbacService.GetUserEffectivePermissions(ctx, uint(userID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取用户有效权限失败", zap.Error(err), zap.Uint64("userId", userID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, permissions)
}

// ==================== 权限资源管理模块 ====================

// ListPermissions 获取权限列表
func (h *RBACHandler) ListPermissions(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.ListPermissions")
	defer span.End()

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("page_size", pageSize),
	)

	permissions, total, err := h.rbacService.ListPermissions(ctx, page, pageSize)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取权限列表失败", zap.Error(err))
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"permissions": permissions,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
	})
}

// GetPermission 获取权限详情
func (h *RBACHandler) GetPermission(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.GetPermission")
	defer span.End()

	permIDStr := c.Param("id")
	permID, err := strconv.ParseUint(permIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的权限ID", zap.Error(err), zap.String("permId", permIDStr))
		response.BadRequest(c, "无效的权限ID")
		return
	}

	span.SetAttributes(attribute.Int64("permission_id", int64(permID)))

	permission, err := h.rbacService.GetPermissionByID(ctx, uint(permID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("获取权限失败", zap.Error(err), zap.Uint64("permId", permID))
		response.ServerError(c, err)
		return
	}

	response.Success(c, permission)
}

// CreatePermission 创建权限
func (h *RBACHandler) CreatePermission(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.CreatePermission")
	defer span.End()

	var req model.PermissionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.String("permission_name", req.Name),
		attribute.String("resource_type", req.ResourceType),
		attribute.String("resource_path", req.ResourcePath),
	)

	permission, err := h.rbacService.CreatePermission(ctx, &req)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("创建权限失败", zap.Error(err), zap.Any("request", req))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("权限创建成功", 
		zap.String("permission_name", permission.Name),
		zap.Uint("permission_id", permission.ID))

	response.Success(c, permission, response.WithMessage("权限创建成功"))
}

// UpdatePermission 更新权限
func (h *RBACHandler) UpdatePermission(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.UpdatePermission")
	defer span.End()

	permIDStr := c.Param("id")
	permID, err := strconv.ParseUint(permIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的权限ID", zap.Error(err), zap.String("permId", permIDStr))
		response.BadRequest(c, "无效的权限ID")
		return
	}

	var req model.PermissionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		span.RecordError(err)
		h.logger.Error("请求参数无效", zap.Error(err))
		response.BadRequest(c, "请求参数无效")
		return
	}

	span.SetAttributes(
		attribute.Int64("permission_id", int64(permID)),
		attribute.String("permission_name", req.Name),
	)

	permission, err := h.rbacService.UpdatePermission(ctx, uint(permID), &req)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("更新权限失败", zap.Error(err), zap.Uint64("permId", permID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("权限更新成功", 
		zap.Uint64("permission_id", permID),
		zap.String("permission_name", permission.Name))

	response.Success(c, permission, response.WithMessage("权限更新成功"))
}

// DeletePermission 删除权限
func (h *RBACHandler) DeletePermission(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, span := h.tracer.Start(ctx, "RBACHandler.DeletePermission")
	defer span.End()

	permIDStr := c.Param("id")
	permID, err := strconv.ParseUint(permIDStr, 10, 32)
	if err != nil {
		span.RecordError(err)
		h.logger.Error("无效的权限ID", zap.Error(err), zap.String("permId", permIDStr))
		response.BadRequest(c, "无效的权限ID")
		return
	}

	span.SetAttributes(attribute.Int64("permission_id", int64(permID)))

	err = h.rbacService.DeletePermission(ctx, uint(permID))
	if err != nil {
		span.RecordError(err)
		h.logger.Error("删除权限失败", zap.Error(err), zap.Uint64("permId", permID))
		response.ServerError(c, err)
		return
	}

	h.logger.Info("权限删除成功", zap.Uint64("permission_id", permID))

	response.Success(c, nil, response.WithMessage("权限删除成功"))
}
