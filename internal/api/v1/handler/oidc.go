package handler

import (
	"crypto/rand"
	"encoding/base64"
	"fmt"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"

	"kubeops/internal/response"
	"kubeops/internal/service"
)

// OIDCHandler 处理OIDC相关的API请求
type OIDCHandler struct {
	authService service.AuthService
	tracer      trace.Tracer
}

// NewOIDCHandler 创建OIDC处理器
func NewOIDCHandler(authService service.AuthService, tracer trace.Tracer) *OIDCHandler {
	return &OIDCHandler{
		authService: authService,
		tracer:      tracer,
	}
}

// 生成随机state字符串
func generateRandomState() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// GetAuthURL 获取OIDC认证URL
func (h *OIDCHandler) GetAuthURL(c *gin.Context) {
	_, span := h.tracer.Start(c.Request.Context(), "OIDCHandler.GetAuthURL")
	defer span.End()

	// 生成随机state用于CSRF保护
	state, err := generateRandomState()
	if err != nil {
		response.ServerError(c, fmt.Errorf("生成state失败: %w", err))
		return
	}

	// 设置state cookie，用于回调时验证
	// 设置30分钟过期时间
	c.SetCookie("oidc_state", state, 1800, "/", "", false, true)

	// 获取带有state参数的认证URL
	authURL := h.authService.GetOIDCAuthURLWithState(state)
	if authURL == "" {
		response.BadRequest(c, "OIDC服务未配置或未启用")
		return
	}

	response.Success(c, gin.H{
		"auth_url": authURL,
	})
}

// HandleCallback 处理OIDC回调
func (h *OIDCHandler) HandleCallback(c *gin.Context) {
	ctx, span := h.tracer.Start(c.Request.Context(), "OIDCHandler.HandleCallback")
	defer span.End()

	// 从请求中获取授权码
	code := c.Query("code")
	if code == "" {
		code = c.PostForm("code")
	}

	// 获取state参数，用于验证请求合法性
	state := c.Query("state")
	if state == "" {
		state = c.PostForm("state")
	}

	if code == "" {
		response.BadRequest(c, "缺少授权码")
		return
	}

	// 验证state参数与Cookie中的值是否匹配
	stateCookie, err := c.Cookie("oidc_state")
	if err != nil || stateCookie != state {
		response.BadRequest(c, "无效的state参数，可能是CSRF攻击")
		return
	}

	// 清除state cookie
	c.SetCookie("oidc_state", "", -1, "/", "", false, true)

	// 处理OIDC回调
	tokenInfo, err := h.authService.HandleOIDCCallback(ctx, code)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	// 在生产环境中，应该从配置中获取前端URL
	// 如果有X-Forwarded-Host或其他头信息，也可以用来构建URL
	host := c.Request.Header.Get("X-Forwarded-Host")
	if host == "" {
		host = c.Request.Host
	}

	// 根据请求协议和主机构建前端URL
	scheme := "http"
	if c.Request.TLS != nil || c.Request.Header.Get("X-Forwarded-Proto") == "https" {
		scheme = "https"
	}

	// 使用当前请求的协议和主机构建重定向URL
	// 注意：这里假设前端和后端部署在同一域名下，只是路径不同
	frontendURL := fmt.Sprintf("%s://%s", scheme, host)

	// 构建带有token的重定向URL
	// 重定向到前端的登录页面，并附加token和过期时间参数
	redirectURL := frontendURL + "/#/login?token=" + tokenInfo.AccessToken + "&expires_in=" + fmt.Sprintf("%d", tokenInfo.ExpiresIn)

	// 记录重定向URL（不包含敏感信息）
	_, redirectSpan := h.tracer.Start(ctx, "OIDC.Redirect")
	redirectSpan.End()

	// 重定向到前端应用
	c.Redirect(302, redirectURL)
}

// Test 测试OIDC配置
func (h *OIDCHandler) Test(c *gin.Context) {
	oidcService := h.authService.GetOIDCService()
	if oidcService == nil {
		response.BadRequest(c, "OIDC服务未配置")
		return
	}

	// 获取认证URL，验证OIDC配置是否正确
	authURL := h.authService.GetOIDCAuthURL()
	if authURL == "" {
		response.BadRequest(c, "OIDC配置无效或未启用")
		return
	}

	response.Success(c, gin.H{
		"message":  "OIDC配置测试成功",
		"auth_url": authURL,
	})
}
