package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"

	"kubeops/internal/model"
	"kubeops/internal/service"
	"kubeops/internal/response"
)

// ClusterHandler 处理集群相关请求
type ClusterHandler struct {
	clusterService service.ClusterService
	tracer         trace.Tracer
}

// NewClusterHandler 创建集群处理器
func NewClusterHandler(clusterService service.ClusterService, tracer trace.Tracer) *ClusterHandler {
	return &ClusterHandler{
		clusterService: clusterService,
		tracer:         tracer,
	}
}

// ListClusters 获取集群列表
func (h *ClusterHandler) ListClusters(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	clusters, total, err := h.clusterService.ListClusters(c.Request.Context(), page, pageSize)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"items": clusters,
		"total": total,
		"page":  page,
		"size":  pageSize,
	})
}

// CreateCluster 创建集群
func (h *ClusterHandler) CreateCluster(c *gin.Context) {
	var req model.ClusterCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的集群数据")
		return
	}

	cluster, err := h.clusterService.CreateCluster(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, cluster, response.WithMessage("集群创建成功"))
}

// GetCluster 获取集群详情
func (h *ClusterHandler) GetCluster(c *gin.Context) {
	clusterIDStr := c.Param("id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	cluster, err := h.clusterService.GetClusterByID(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, cluster)
}

// UpdateCluster 更新集群
func (h *ClusterHandler) UpdateCluster(c *gin.Context) {
	clusterIDStr := c.Param("id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	var req model.ClusterUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的集群数据")
		return
	}

	cluster, err := h.clusterService.UpdateCluster(c.Request.Context(), uint(clusterID), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, cluster, response.WithMessage("集群更新成功"))
}

// DeleteCluster 删除集群
func (h *ClusterHandler) DeleteCluster(c *gin.Context) {
	clusterIDStr := c.Param("id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	err = h.clusterService.DeleteCluster(c.Request.Context(), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("集群删除成功"))
}

// ListNodes 列出集群节点
func (h *ClusterHandler) ListNodes(c *gin.Context) {
	id := c.Param("id")
	response.Success(c, gin.H{"message": "获取节点列表", "cluster_id": id})
}

// GetNode 获取节点详情
func (h *ClusterHandler) GetNode(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "获取节点详情", "cluster_id": id, "node_name": name})
}

// CordonNode 标记节点为不可调度
func (h *ClusterHandler) CordonNode(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "标记节点为不可调度", "cluster_id": id, "node_name": name})
}

// UncordonNode 标记节点为可调度
func (h *ClusterHandler) UncordonNode(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "标记节点为可调度", "cluster_id": id, "node_name": name})
}

// DrainNode 驱逐节点上的Pod
func (h *ClusterHandler) DrainNode(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "驱逐节点上的Pod", "cluster_id": id, "node_name": name})
}

// ListNamespaces 列出命名空间
func (h *ClusterHandler) ListNamespaces(c *gin.Context) {
	id := c.Param("id")
	response.Success(c, gin.H{"message": "获取命名空间列表", "cluster_id": id})
}

// CreateNamespace 创建命名空间
func (h *ClusterHandler) CreateNamespace(c *gin.Context) {
	id := c.Param("id")
	response.Success(c, gin.H{"message": "创建命名空间", "cluster_id": id})
}

// GetNamespace 获取命名空间详情
func (h *ClusterHandler) GetNamespace(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "获取命名空间详情", "cluster_id": id, "namespace": name})
}

// DeleteNamespace 删除命名空间
func (h *ClusterHandler) DeleteNamespace(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "删除命名空间", "cluster_id": id, "namespace": name})
}

// ListResources 列出资源
func (h *ClusterHandler) ListResources(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	response.Success(c, gin.H{
		"message":    "获取资源列表",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
	})
}

// CreateResource 创建资源
func (h *ClusterHandler) CreateResource(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	response.Success(c, gin.H{
		"message":    "创建资源",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
	})
}

// GetResource 获取资源详情
func (h *ClusterHandler) GetResource(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	resourceName := c.Param("resourceName")
	response.Success(c, gin.H{
		"message":    "获取资源详情",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
		"name":       resourceName,
	})
}

// UpdateResource 更新资源
func (h *ClusterHandler) UpdateResource(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	resourceName := c.Param("resourceName")
	response.Success(c, gin.H{
		"message":    "更新资源",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
		"name":       resourceName,
	})
}

// DeleteResource 删除资源
func (h *ClusterHandler) DeleteResource(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	resourceName := c.Param("resourceName")
	response.Success(c, gin.H{
		"message":    "删除资源",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
		"name":       resourceName,
	})
}

// ListEvents 列出集群事件
func (h *ClusterHandler) ListEvents(c *gin.Context) {
	id := c.Param("id")
	response.Success(c, gin.H{"message": "获取集群事件", "cluster_id": id})
}

// ListNamespaceEvents 列出命名空间事件
func (h *ClusterHandler) ListNamespaceEvents(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	response.Success(c, gin.H{"message": "获取命名空间事件", "cluster_id": id, "namespace": name})
}

// ListResourceEvents 列出资源事件
func (h *ClusterHandler) ListResourceEvents(c *gin.Context) {
	id := c.Param("id")
	name := c.Param("name")
	resource := c.Param("resource")
	resourceName := c.Param("resourceName")
	response.Success(c, gin.H{
		"message":    "获取资源事件",
		"cluster_id": id,
		"namespace":  name,
		"resource":   resource,
		"name":       resourceName,
	})
}
