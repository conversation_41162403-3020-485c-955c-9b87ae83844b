package handler

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"kubeops/internal/model"
	"kubeops/internal/service"
	"kubeops/internal/response"
)

// ProjectHandler 项目处理器
type ProjectHandler struct {
	projectService service.ProjectService
}

// NewProjectHandler 创建项目处理器
func NewProjectHandler(projectService service.ProjectService) *ProjectHandler {
	return &ProjectHandler{
		projectService: projectService,
	}
}

// ListProjects 获取项目列表
func (h *ProjectHandler) ListProjects(c *gin.Context) {
	page, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "10"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	projects, total, err := h.projectService.ListProjects(c.Request.Context(), page, pageSize)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, gin.H{
		"items": projects,
		"total": total,
		"page":  page,
		"size":  pageSize,
	})
}

// GetProject 获取项目详情
func (h *ProjectHandler) GetProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	project, err := h.projectService.GetProjectByID(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, project)
}

// CreateProject 创建项目
func (h *ProjectHandler) CreateProject(c *gin.Context) {
	var req model.ProjectCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的项目数据")
		return
	}

	// 从上下文获取当前用户ID
	_, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "用户未认证")
		return
	}
	// 注意：ProjectCreateRequest中没有CreatedBy字段，创建者信息由服务层处理

	project, err := h.projectService.CreateProject(c.Request.Context(), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, project, response.WithMessage("项目创建成功"))
}

// UpdateProject 更新项目
func (h *ProjectHandler) UpdateProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	var req model.ProjectUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的项目数据")
		return
	}

	project, err := h.projectService.UpdateProject(c.Request.Context(), uint(projectID), &req)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, project, response.WithMessage("项目更新成功"))
}

// DeleteProject 删除项目
func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	err = h.projectService.DeleteProject(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目删除成功"))
}

// ActivateProject 激活项目
func (h *ProjectHandler) ActivateProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	err = h.projectService.ActivateProject(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目激活成功"))
}

// DeactivateProject 停用项目
func (h *ProjectHandler) DeactivateProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	err = h.projectService.DeactivateProject(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目停用成功"))
}

// ArchiveProject 归档项目
func (h *ProjectHandler) ArchiveProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	err = h.projectService.ArchiveProject(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目归档成功"))
}

// UnarchiveProject 取消归档项目
func (h *ProjectHandler) UnarchiveProject(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	err = h.projectService.UnarchiveProject(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目取消归档成功"))
}

// AddProjectToCluster 将项目添加到集群
func (h *ProjectHandler) AddProjectToCluster(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	var req model.ProjectClusterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	projectCluster, err := h.projectService.AddProjectToCluster(c.Request.Context(), uint(projectID), req.ClusterID, req.Namespace)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, projectCluster, response.WithMessage("项目添加到集群成功"))
}

// RemoveProjectFromCluster 从集群移除项目
func (h *ProjectHandler) RemoveProjectFromCluster(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	clusterIDStr := c.Param("cluster_id")
	clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的集群ID")
		return
	}

	err = h.projectService.RemoveProjectFromCluster(c.Request.Context(), uint(projectID), uint(clusterID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目从集群移除成功"))
}

// GetProjectClusters 获取项目的集群部署信息
func (h *ProjectHandler) GetProjectClusters(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	clusters, err := h.projectService.GetProjectClusters(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, clusters)
}

// GetProjectMembers 获取项目成员
func (h *ProjectHandler) GetProjectMembers(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	members, err := h.projectService.GetProjectMembers(c.Request.Context(), uint(projectID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, members)
}

// AddProjectMember 添加项目成员
func (h *ProjectHandler) AddProjectMember(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	var req model.ProjectMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "无效的请求数据")
		return
	}

	err = h.projectService.AddProjectMember(c.Request.Context(), uint(projectID), req.UserID, req.Role)
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目成员添加成功"))
}

// RemoveProjectMember 移除项目成员
func (h *ProjectHandler) RemoveProjectMember(c *gin.Context) {
	projectIDStr := c.Param("id")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目ID")
		return
	}

	userIDStr := c.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的用户ID")
		return
	}

	err = h.projectService.RemoveProjectMember(c.Request.Context(), uint(projectID), uint(userID))
	if err != nil {
		response.ServerError(c, err)
		return
	}

	response.Success(c, nil, response.WithMessage("项目成员移除成功"))
}
