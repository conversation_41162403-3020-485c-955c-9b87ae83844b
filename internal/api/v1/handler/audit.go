package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/service"
)

// AuditHandler 审计处理器
type AuditHandler struct {
	auditService service.AuditService
	logger       *zap.Logger
}

// NewAuditHandler 创建审计处理器
func NewAuditHandler(auditService service.AuditService, logger *zap.Logger) *AuditHandler {
	return &AuditHandler{
		auditService: auditService,
		logger:       logger,
	}
}

// ListAuditLogs 获取审计日志列表
// @Summary 获取审计日志列表
// @Description 获取审计日志列表，支持多条件过滤和分页
// @Tags 审计
// @Accept json
// @Produce json
// @Param filter query model.AuditQueryFilter true "过滤条件"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/logs [get]
func (h *AuditHandler) ListAuditLogs(c *gin.Context) {
	var filter model.AuditQueryFilter

	// 解析查询参数
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid query parameters",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认分页
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}

	// 获取审计日志
	logs, total, err := h.auditService.ListAuditLogs(c.Request.Context(), &filter)
	if err != nil {
		h.logger.Error("Failed to get audit logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to get audit logs",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":  http.StatusOK,
		"data":  logs,
		"total": total,
		"page":  filter.Page,
		"size":  filter.PageSize,
	})
}

// ExportAuditLogs 导出审计日志
// @Summary 导出审计日志
// @Description 导出审计日志为CSV或JSON格式
// @Tags 审计
// @Accept json
// @Produce octet-stream
// @Param request body model.AuditExportRequest true "导出请求"
// @Success 200 {file} binary "导出文件"
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/export [post]
func (h *AuditHandler) ExportAuditLogs(c *gin.Context) {
	var req model.AuditExportRequest

	// 解析请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// 验证格式
	if req.Format != "csv" && req.Format != "json" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Unsupported format, must be 'csv' or 'json'",
		})
		return
	}

	// 导出数据
	data, err := h.auditService.ExportAuditLogs(c.Request.Context(), &req.Filter, req.Format)
	if err != nil {
		h.logger.Error("Failed to export audit logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to export audit logs",
			"error":   err.Error(),
		})
		return
	}

	// 设置响应头
	filename := "audit_logs"
	if req.Format == "csv" {
		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", "attachment; filename="+filename+".csv")
	} else {
		c.Header("Content-Type", "application/json")
		c.Header("Content-Disposition", "attachment; filename="+filename+".json")
	}

	c.Data(http.StatusOK, c.GetHeader("Content-Type"), data)
}

// ListAuditArchives 获取审计归档列表
// @Summary 获取审计归档列表
// @Description 获取审计归档列表，支持分页
// @Tags 审计
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/archives [get]
func (h *AuditHandler) ListAuditArchives(c *gin.Context) {
	// 解析分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	// 获取归档列表
	archives, total, err := h.auditService.ListAuditArchives(c.Request.Context(), page, size)
	if err != nil {
		h.logger.Error("Failed to get audit archives", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to get audit archives",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":  http.StatusOK,
		"data":  archives,
		"total": total,
		"page":  page,
		"size":  size,
	})
}

// GetAuditArchive 获取审计归档详情
// @Summary 获取审计归档详情
// @Description 获取单个审计归档的详细信息
// @Tags 审计
// @Accept json
// @Produce json
// @Param id path int true "归档ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/archives/{id} [get]
func (h *AuditHandler) GetAuditArchive(c *gin.Context) {
	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid archive ID",
			"error":   err.Error(),
		})
		return
	}

	// 获取归档详情
	archive, err := h.auditService.GetAuditArchive(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get audit archive", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to get audit archive",
			"error":   err.Error(),
		})
		return
	}

	if archive == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "Archive not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": http.StatusOK,
		"data": archive,
	})
}

// DeleteAuditArchive 删除审计归档
// @Summary 删除审计归档
// @Description 删除单个审计归档及其关联的OBS文件
// @Tags 审计
// @Accept json
// @Produce json
// @Param id path int true "归档ID"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/archives/{id} [delete]
func (h *AuditHandler) DeleteAuditArchive(c *gin.Context) {
	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid archive ID",
			"error":   err.Error(),
		})
		return
	}

	// 删除归档
	if err := h.auditService.DeleteAuditArchive(c.Request.Context(), uint(id)); err != nil {
		h.logger.Error("Failed to delete audit archive", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to delete audit archive",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Archive deleted successfully",
	})
}

// DownloadArchiveFile 下载归档文件
// @Summary 下载归档文件
// @Description 下载指定ID的审计日志归档文件
// @Tags 审计
// @Accept json
// @Produce octet-stream
// @Param id path int true "归档ID"
// @Success 200 {file} binary "CSV文件"
// @Failure 400 {object} gin.H
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/archives/{id}/download [get]
func (h *AuditHandler) DownloadArchiveFile(c *gin.Context) {
	// 解析ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid archive ID",
			"error":   err.Error(),
		})
		return
	}

	// 获取归档详情（用于文件名）
	archive, err := h.auditService.GetAuditArchive(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get audit archive", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to get audit archive",
			"error":   err.Error(),
		})
		return
	}

	if archive == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    http.StatusNotFound,
			"message": "Archive not found",
		})
		return
	}

	// 下载文件 - TODO: 实现DownloadArchiveFile方法
	// data, err := h.auditService.DownloadArchiveFile(c.Request.Context(), uint(id))
	data := []byte{} // 暂时返回空数据

	// if err != nil {
	// 	h.logger.Error("Failed to download archive file", zap.Error(err))
	// 	c.JSON(http.StatusInternalServerError, gin.H{
	// 		"code":    http.StatusInternalServerError,
	// 		"message": "Failed to download archive file",
	// 		"error":   err.Error(),
	// 	})
	// 	return
	// }

	// 设置响应头
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename="+archive.ArchiveName)

	c.Data(http.StatusOK, c.GetHeader("Content-Type"), data)
}

// CreateArchive 创建归档
// @Summary 手动触发创建归档
// @Description 手动触发创建上一季度的审计日志归档
// @Tags 审计
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/archives/create [post]
func (h *AuditHandler) CreateArchive(c *gin.Context) {
	// 获取当前时间，计算上一季度
	now := time.Now()
	year := now.Year()
	quarter := (int(now.Month())-1)/3 + 1

	// 如果是第一季度，则年份减1，季度设为4
	if quarter == 1 {
		year--
		quarter = 4
	} else {
		quarter--
	}

	// 创建归档
	if err := h.auditService.CreateQuarterlyArchive(c.Request.Context(), year, quarter); err != nil {
		h.logger.Error("Failed to create archive", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to create archive",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Archive creation started successfully",
	})
}

// GetArchiveConfig 获取归档配置
// @Summary 获取归档配置
// @Description 获取审计日志归档的配置信息
// @Tags 审计
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/config [get]
func (h *AuditHandler) GetArchiveConfig(c *gin.Context) {
	// 获取配置
	config, err := h.auditService.GetAuditArchiveConfig(c.Request.Context())
	if err != nil {
		h.logger.Error("Failed to get archive config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to get archive config",
			"error":   err.Error(),
		})
		return
	}

	// 安全起见，不返回加密密钥
	config.EncryptionKey = ""

	c.JSON(http.StatusOK, gin.H{
		"code": http.StatusOK,
		"data": config,
	})
}

// UpdateArchiveConfig 更新归档配置
// @Summary 更新归档配置
// @Description 更新审计日志归档的配置信息
// @Tags 审计
// @Accept json
// @Produce json
// @Param config body model.AuditArchiveConfig true "归档配置"
// @Success 200 {object} gin.H
// @Failure 400 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/config [put]
func (h *AuditHandler) UpdateArchiveConfig(c *gin.Context) {
	var config model.AuditArchiveConfig

	// 解析请求体
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// 验证归档周期
	if config.ArchiveInterval != "quarterly" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "Invalid archive interval, must be 'quarterly'",
		})
		return
	}

	// 更新配置
	if err := h.auditService.UpdateAuditArchiveConfig(c.Request.Context(), &config); err != nil {
		h.logger.Error("Failed to update archive config", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to update archive config",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Archive config updated successfully",
	})
}

// CleanupExpiredData 清理过期数据
// @Summary 清理过期数据
// @Description 手动触发清理过期的审计日志数据
// @Tags 审计
// @Accept json
// @Produce json
// @Success 200 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /api/v1/audit/cleanup [post]
func (h *AuditHandler) CleanupExpiredData(c *gin.Context) {
	// 清理过期数据
	if err := h.auditService.CleanupExpiredData(c.Request.Context()); err != nil {
		h.logger.Error("Failed to cleanup expired data", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "Failed to cleanup expired data",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    http.StatusOK,
		"message": "Expired data cleanup completed successfully",
	})
}
