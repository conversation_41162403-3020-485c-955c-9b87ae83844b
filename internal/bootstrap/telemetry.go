package bootstrap

import (
	"context"

	"go.uber.org/zap"

	"kubeops/internal/config"
	"kubeops/internal/logger"
	"kubeops/internal/telemetry"
)

// initTelemetry 初始化OpenTelemetry
func initTelemetry(ctx context.Context, cfg *config.Config) (*telemetry.Provider, *telemetry.MetricsProvider, error) {
	telemetryConfig := &telemetry.Config{
		ServiceName:    "kubeops",
		ServiceVersion: "1.0.0",
		Environment:    cfg.Server.Mode,
		OTLPEndpoint:   cfg.Telemetry.Endpoint,
		Enabled:        cfg.Telemetry.Enabled,
		SamplingRate:   1.0, // 采样率设为1.0，确保所有请求都被采样
	}

	// 输出遥测配置信息
	logger.InfoContext(ctx, "初始化遥测系统",
		zap.Bool("enabled", cfg.Telemetry.Enabled),
		zap.String("endpoint", cfg.Telemetry.Endpoint),
		zap.String("service", "kubeops"),
		zap.Float64("sampling_rate", telemetryConfig.SamplingRate))

	provider, err := telemetry.NewProvider(ctx, telemetryConfig)
	if err != nil {
		logger.WarnContext(ctx, "初始化遥测提供者失败，将使用无操作实现",
			zap.Error(err),
			zap.Bool("telemetry_enabled", cfg.Telemetry.Enabled),
			zap.String("endpoint", cfg.Telemetry.Endpoint))

		// 返回一个空的提供者，确保应用可以继续运行
		provider = &telemetry.Provider{}
	} else {
		logger.InfoContext(ctx, "遥测提供者初始化成功",
			zap.Bool("enabled", cfg.Telemetry.Enabled),
			zap.String("endpoint", cfg.Telemetry.Endpoint))
	}

	// 如果提供者初始化成功但 Tracer 为空，日志记录警告
	if provider.Tracer() == nil {
		logger.WarnContext(ctx, "遥测Tracer为空，可能无法记录跟踪信息")
	}

	// 即使提供者初始化失败，我们也创建一个指标提供者
	metricsProvider := telemetry.NewMetricsProvider(provider.MeterProvider(), "kubeops")

	// 注册常用指标
	_, _ = metricsProvider.RegisterCounter("http.requests.total", "HTTP请求总数")
	_, _ = metricsProvider.RegisterHistogram("http.request.duration", "HTTP请求处理时间")

	return provider, metricsProvider, nil
}
