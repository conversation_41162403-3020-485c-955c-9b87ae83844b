package bootstrap

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"kubeops/internal/api"
	"kubeops/internal/auth"
	"kubeops/internal/config"
	"kubeops/internal/model"
	"kubeops/internal/oidc"
	"kubeops/internal/redis"
	"kubeops/internal/repository"
	"kubeops/internal/service"
	"kubeops/internal/telemetry"
	"kubeops/pkg/k8s"
	"kubeops/pkg/lark"
	"kubeops/pkg/obs"
)

// App 应用实例 - 重构版本
type App struct {
	Config            *config.Config             // 应用配置
	Logger            *zap.Logger                // 日志记录器
	Server            *http.Server               // HTTP服务器
	TelemetryProvider *telemetry.Provider        // 遥测提供者
	MetricsProvider   *telemetry.MetricsProvider // 指标提供者
	RedisManager      redis.RedisManager         // Redis管理器
	JWTService        auth.JWTService            // JWT服务
	Service           service.Service            // 服务层
	ClusterManager    *k8s.ClusterManager        // 集群管理器
	CronManager       *cron.Cron                 // 定时任务管理器
}

// NewApp 创建应用实例
func NewApp(cfg *config.Config, log *zap.Logger) (*App, error) {
	// 步骤1: 使用传入的日志实例
	// 记录日志系统信息
	log.Info("使用已初始化的日志系统",
		zap.String("level", cfg.Log.Level),
		zap.String("format", cfg.Log.Format))

	// 步骤2: 初始化遥测系统
	log.Info("正在初始化遥测系统...")
	ctx := context.Background()
	telemetryProvider, metricsProvider, err := initTelemetry(ctx, cfg)
	if err != nil {
		log.Warn("遥测系统初始化失败，将使用空导出器",
			zap.Error(err),
			zap.Bool("enabled", cfg.Telemetry.Enabled),
			zap.String("endpoint", cfg.Telemetry.Endpoint))
	} else {
		log.Info("遥测系统初始化成功",
			zap.String("service", "kubeops"),
			zap.String("otlp_endpoint", cfg.Telemetry.Endpoint))
	}

	// 步骤3: 初始化Redis管理器
	log.Info("正在初始化Redis管理器...")
	redisManager, err := redis.NewRedisManager(cfg)
	if err != nil {
		log.Error("Redis管理器初始化失败",
			zap.Error(err))
		return nil, fmt.Errorf("初始化Redis失败: %w", err)
	}
	log.Info("Redis管理器初始化成功",
		zap.String("mode", cfg.Redis.Global.Mode),
		zap.Strings("nodes", cfg.Redis.Global.Nodes))

	// 步骤4: 初始化数据库
	log.Info("正在初始化数据库连接...")
	repo, err := repository.Init(cfg)
	if err != nil {
		log.Error("数据库连接初始化失败",
			zap.Error(err))
		return nil, fmt.Errorf("初始化数据库失败: %w", err)
	}
	log.Info("数据库连接初始化成功",
		zap.String("type", cfg.Database.Type),
		zap.String("db_name", cfg.Database.DBName))

	// 步骤5: 初始化JWT服务
	log.Info("正在初始化JWT服务...")
	jwtService := auth.NewJWTService(cfg, redisManager)
	log.Info("JWT服务初始化成功")

	// 步骤6: 初始化RBAC权限系统
	log.Info("正在初始化RBAC权限系统...")
	_, err = initRBAC(context.Background(), repo, log, telemetryProvider.Tracer())
	if err != nil {
		log.Error("RBAC权限系统初始化失败",
			zap.Error(err))
		return nil, fmt.Errorf("初始化RBAC失败: %w", err)
	}
	log.Info("RBAC权限系统初始化成功")

	// 步骤4: 初始化Kubernetes集群管理器
	log.Info("正在初始化Kubernetes集群管理器...")
	clusterManager := k8s.NewClusterManager()
	log.Info("Kubernetes集群管理器初始化成功")

	// 步骤5: 初始化服务层
	log.Info("正在初始化服务层...")

	// 创建Casbin服务
	// 获取数据库连接
	type dbGetter interface {
		GetDB() (*gorm.DB, error)
	}
	dbRepo, ok := repo.(dbGetter)
	if !ok {
		return nil, fmt.Errorf("repository不支持GetDB方法")
	}
	db, err := dbRepo.GetDB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	casbinService, err := service.NewCasbinService(db, repo, log, telemetryProvider.Tracer())
	if err != nil {
		return nil, fmt.Errorf("创建Casbin服务失败: %w", err)
	}

	// 创建基础服务 - 使用新的JWT架构
	svc := service.NewServiceWithCasbin(
		repo,
		log,
		telemetryProvider.Tracer(),
		cfg,
		redisManager,
		jwtService,
		nil,
		casbinService,
		nil,
	)

	// 步骤6: 从数据库获取系统配置
	configCtx := context.Background()

	// 从数据库获取OIDC配置
	oidcSystemConfig, oidcErr := svc.SystemConfig().GetRawOIDCConfig(configCtx)
	if oidcErr != nil {
		log.Warn("获取OIDC配置失败，使用默认配置", zap.Error(oidcErr))
		oidcSystemConfig = &model.SystemOIDCConfig{
			Enabled:     false,
			IssuerURL:   "",
			ClientID:    "",
			RedirectURI: "",
			Scopes:      "openid,profile,email",
		}
	}

	// 转换为oidc.OIDCConfig
	oidcConfig := &oidc.OIDCConfig{
		Enabled:      oidcSystemConfig.Enabled,
		IssuerURL:    oidcSystemConfig.IssuerURL,
		ClientID:     oidcSystemConfig.ClientID,
		ClientSecret: oidcSystemConfig.ClientSecret, // 使用数据库中存储的实际密钥
		RedirectURI:  oidcSystemConfig.RedirectURI,
		Scopes:       oidcSystemConfig.Scopes,
		GroupsClaim:  oidcSystemConfig.GroupsClaim,
	}

	// 记录OIDC配置信息，但隐藏敏感信息
	log.Info("初始化OIDC服务",
		zap.Bool("enabled", oidcConfig.Enabled),
		zap.String("issuer_url", oidcConfig.IssuerURL),
		zap.String("client_id", oidcConfig.ClientID),
		zap.Bool("has_client_secret", oidcConfig.ClientSecret != ""),
		zap.String("redirect_uri", oidcConfig.RedirectURI))

	// 更新认证服务中的OIDC配置
	if oidcSystemConfig.Enabled {
		log.Info("正在初始化OIDC服务...",
			zap.String("issuer_url", oidcSystemConfig.IssuerURL),
			zap.String("client_id", oidcSystemConfig.ClientID),
			zap.Bool("has_client_secret", oidcSystemConfig.ClientSecret != ""),
			zap.String("redirect_uri", oidcSystemConfig.RedirectURI))

		svc.Auth().UpdateOIDCConfig(oidcConfig)
	}

	// 从数据库获取飞书配置 - 仅用于机器人交互
	feishuSystemConfig, feishuErr := svc.SystemConfig().GetRawFeishuConfig(configCtx)
	if feishuErr != nil {
		log.Warn("获取飞书配置失败，使用默认配置", zap.Error(feishuErr))
		feishuSystemConfig = &model.SystemFeishuConfig{
			AppID:     "",
			AppSecret: "",
		}
	}

	// 转换为lark.FeishuConfig - 仅用于机器人交互
	feishuConfig := &lark.FeishuConfig{
		AppID:     feishuSystemConfig.AppID,
		AppSecret: feishuSystemConfig.AppSecret,
	}

	// 记录飞书配置信息，但隐藏敏感信息
	log.Info("初始化飞书服务",
		zap.String("app_id", feishuConfig.AppID),
		zap.Bool("has_app_secret", feishuConfig.AppSecret != ""))

	// 更新集成服务中的飞书配置
	svc.Integration().UpdateFeishuConfig(feishuConfig)

	// 从数据库获取OBS配置
	obsSystemConfig, obsErr := svc.SystemConfig().GetRawOBSConfig(configCtx)
	if obsErr != nil {
		log.Warn("获取OBS配置失败，部分功能可能不可用", zap.Error(obsErr))
		obsSystemConfig = &model.SystemOBSConfig{
			Enabled:       false,
			Endpoint:      "https://obs.cn-north-4.myhuaweicloud.com",
			AccessKey:     "",
			SecretKey:     "",
			Bucket:        "kubeops-audit-logs",
			Region:        "cn-north-4",
			EncryptionKey: "",
		}
	}

	// 步骤7: 初始化OBS客户端
	log.Info("正在初始化OBS客户端...")
	obsConfig := &obs.OBSConfig{
		Enabled:       obsSystemConfig.Enabled,
		Endpoint:      obsSystemConfig.Endpoint,
		AccessKey:     obsSystemConfig.AccessKey,
		SecretKey:     obsSystemConfig.SecretKey,
		Bucket:        obsSystemConfig.Bucket,
		Region:        obsSystemConfig.Region,
		EncryptionKey: obsSystemConfig.EncryptionKey,
	}

	// 通过集成服务更新OBS客户端
	if err := svc.Integration().UpdateOBSConfig(obsConfig); err != nil {
		log.Warn("OBS客户端初始化失败，部分功能可能不可用",
			zap.Error(err),
			zap.Bool("enabled", obsSystemConfig.Enabled))
	} else {
		log.Info("OBS客户端初始化成功",
			zap.Bool("enabled", obsSystemConfig.Enabled),
			zap.String("endpoint", obsSystemConfig.Endpoint),
			zap.String("bucket", obsSystemConfig.Bucket))
	}

	// 获取OBS客户端并更新审计服务
	obsClient := svc.Integration().GetOBSClient()
	svc.Audit().UpdateOBSClient(obsClient)
	log.Info("服务层初始化成功")

	// 步骤8: 初始化基础用户组
	log.Info("正在初始化系统权限...")
	if rbacErr := svc.RBAC().InitSystemPermissions(configCtx); rbacErr != nil {
		// 使用警告级别，因为这可以后续手动配置
		log.Warn("系统权限初始化失败，请手动配置", zap.Error(rbacErr))
	} else {
		log.Info("系统权限初始化成功")
	}

	// 步骤9: 设置HTTP路由
	log.Info("正在设置HTTP路由...")
	router := api.SetupRouter(cfg, svc, jwtService, telemetryProvider, metricsProvider, log)
	log.Info("HTTP路由设置完成")

	// 步骤10: 创建HTTP服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}
	log.Info("HTTP服务器创建完成", zap.Int("port", cfg.Server.Port))

	// 步骤11: 初始化定时任务管理器
	log.Info("正在初始化定时任务管理器...")
	cronManager := cron.New(cron.WithSeconds())
	log.Info("定时任务管理器初始化完成")

	// 返回完整的应用实例
	log.Info("KubeOps应用初始化完成，准备启动")
	return &App{
		Config:            cfg,
		Logger:            log,
		Server:            server,
		TelemetryProvider: telemetryProvider,
		MetricsProvider:   metricsProvider,
		RedisManager:      redisManager,
		JWTService:        jwtService,
		Service:           svc,
		ClusterManager:    clusterManager,
		CronManager:       cronManager,
	}, nil
}

// Start 启动应用
func (a *App) Start() error {
	a.Logger.Info("正在启动KubeOps服务...",
		zap.Int("port", a.Config.Server.Port),
		zap.String("mode", a.Config.Server.Mode))

	// 初始化系统权限，包括UI和API权限
	a.Logger.Info("正在初始化系统权限...")
	if err := a.Service.RBAC().InitSystemPermissions(context.Background()); err != nil {
		a.Logger.Warn("系统权限初始化失败，请手动初始化", zap.Error(err))
	} else {
		a.Logger.Info("系统权限初始化完成")
	}

	// 设置并启动定时任务
	a.setupCronJobs()
	a.CronManager.Start()
	a.Logger.Info("定时任务管理器已启动")

	// 启动HTTP服务器
	if err := a.Server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		a.Logger.Error("HTTP服务器启动失败", zap.Error(err))
		return fmt.Errorf("启动服务器失败: %w", err)
	}

	return nil
}

// Stop 停止应用
func (a *App) Stop(ctx context.Context) error {
	a.Logger.Info("正在优雅关闭KubeOps服务...")

	// 停止定时任务
	if a.CronManager != nil {
		a.CronManager.Stop()
		a.Logger.Info("定时任务管理器已停止")
	}

	// 关闭HTTP服务器
	if err := a.Server.Shutdown(ctx); err != nil {
		a.Logger.Error("HTTP服务器关闭失败", zap.Error(err))
		return fmt.Errorf("关闭HTTP服务器失败: %w", err)
	}
	a.Logger.Info("HTTP服务器已关闭")

	// 关闭集群连接
	if err := a.ClusterManager.Close(); err != nil {
		a.Logger.Error("集群连接关闭失败", zap.Error(err))
	} else {
		a.Logger.Info("所有集群连接已关闭")
	}

	// 关闭遥测系统
	if a.TelemetryProvider != nil {
		if err := a.TelemetryProvider.Shutdown(ctx); err != nil {
			a.Logger.Error("遥测系统关闭失败", zap.Error(err))
		} else {
			a.Logger.Info("遥测系统已关闭")
		}
	}

	// 同步日志
	a.Logger.Info("KubeOps服务已完全关闭")
	_ = a.Logger.Sync() // 忽略可能的错误，标准输出通常不支持同步

	return nil
}

// setupCronJobs 设置定时任务
func (a *App) setupCronJobs() {
	// 获取审计服务
	auditService := a.Service.Audit()

	// 每天凌晨3点执行清理过期数据
	_, err := a.CronManager.AddFunc("0 0 3 * * *", func() {
		ctx := context.Background()
		a.Logger.Info("执行清理过期审计日志任务")
		if err := auditService.CleanupExpiredData(ctx); err != nil {
			a.Logger.Error("清理过期审计日志失败", zap.Error(err))
		} else {
			a.Logger.Info("清理过期审计日志完成")
		}
	})
	if err != nil {
		a.Logger.Error("添加清理任务失败", zap.Error(err))
	}

	// 每个季度的第一天凌晨2点执行归档
	// 1月1日、4月1日、7月1日、10月1日
	_, err = a.CronManager.AddFunc("0 0 2 1 1,4,7,10 *", func() {
		ctx := context.Background()
		a.Logger.Info("执行季度审计日志归档任务")

		// 获取当前年份和季度
		now := time.Now()
		year := now.Year()

		// 计算当前季度
		currentMonth := now.Month()
		quarter := (int(currentMonth)-1)/3 + 1

		err := auditService.CreateQuarterlyArchive(ctx, year, quarter)
		if err != nil {
			a.Logger.Error("创建季度审计归档失败", zap.Error(err))
		} else {
			a.Logger.Info("季度审计归档任务已启动")
		}
	})
	if err != nil {
		a.Logger.Error("添加归档任务失败", zap.Error(err))
	}
}
