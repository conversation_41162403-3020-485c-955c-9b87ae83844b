package router_test

import (
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/router"
	"kubeops/internal/service"
)

// 此文件仅作为示例使用，不会被实际运行
func ExampleWithPermission() {
	// 示例代码
	var (
		engine *gin.Engine
		resourcePermService service.ResourcePermissionService
		logger *zap.Logger
		tracer trace.Tracer
	)

	// 创建路由注册器
	registrar := router.NewRouteRegistrar(engine, resourcePermService, logger, tracer)

	// 创建API路由组
	apiGroup := registrar.Group("/api")
	v1Group := apiGroup.Group("/v1")

	// 方法1: 标准方式 - 自动解析权限资源和操作
	// 路径: /api/v1/standard-path
	// 权限: api:standard-path:list
	v1Group.GET("/standard-path", func(c *gin.Context) {})

	// 方法2: 自定义权限信息 - 使用WithPermission()方法
	// 路径: /api/v1/custom-path
	// 权限: api:custom-resource:custom-action
	v1Group.WithPermission().GET("/custom-path", 
		[]router.RouteOption{
			router.WithResource("custom-resource"),
			router.WithAction("custom-action"),
			router.WithResourceName("自定义资源"),
			router.WithActionName("自定义操作"),
			router.WithDescription("这是一个自定义权限资源示例"),
		},
		func(c *gin.Context) {})

	// 方法3: 跳过权限注册 - 对于不需要权限检查的路由
	// 路径: /api/v1/public-path
	// 不会创建权限资源
	v1Group.WithPermission().GET("/public-path", 
		[]router.RouteOption{
			router.SkipPermissionRegistration(),
		},
		func(c *gin.Context) {})
}
