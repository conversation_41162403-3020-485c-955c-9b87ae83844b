package router

import (
	"context"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/service"
)

// RouteRegistrar 路由注册器，包装Gin路由器以收集API路由信息
type RouteRegistrar struct {
	engine    *gin.Engine
	collector *RouteCollector
	logger    *zap.Logger
	tracer    trace.Tracer
}

// NewRouteRegistrar 创建新的路由注册器
func NewRouteRegistrar(
	engine *gin.Engine,
	rbacService service.RBACService,
	logger *zap.Logger,
	tracer trace.Tracer,
) *RouteRegistrar {
	return &RouteRegistrar{
		engine:    engine,
		collector: NewRouteCollector(rbacService, logger, tracer),
		logger:    logger,
		tracer:    tracer,
	}
}

// Group 创建路由组
func (r *RouteRegistrar) Group(relativePath string, handlers ...gin.HandlerFunc) *RouteGroup {
	return &RouteGroup{
		group:     r.engine.Group(relativePath, handlers...),
		registrar: r,
		basePath:  relativePath,
	}
}

// RegisterResources 注册收集到的API路由为系统资源
func (r *RouteRegistrar) RegisterResources(ctx context.Context) error {
	return r.collector.RegisterResources(ctx)
}

// RouteGroup 路由组，包装Gin路由组以收集API路由信息
type RouteGroup struct {
	group     *gin.RouterGroup
	registrar *RouteRegistrar
	basePath  string
}

// Group 创建子路由组
func (g *RouteGroup) Group(relativePath string, handlers ...gin.HandlerFunc) *RouteGroup {
	return &RouteGroup{
		group:     g.group.Group(relativePath, handlers...),
		registrar: g.registrar,
		basePath:  g.basePath + relativePath,
	}
}

// method 统一处理HTTP方法注册
func (g *RouteGroup) method(method, relativePath string, options []RouteOption, handlers []gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute(method, g.basePath+relativePath, "", options...)

	switch method {
	case "GET":
		return g.group.GET(relativePath, handlers...)
	case "POST":
		return g.group.POST(relativePath, handlers...)
	case "PUT":
		return g.group.PUT(relativePath, handlers...)
	case "DELETE":
		return g.group.DELETE(relativePath, handlers...)
	case "PATCH":
		return g.group.PATCH(relativePath, handlers...)
	case "HEAD":
		return g.group.HEAD(relativePath, handlers...)
	case "OPTIONS":
		return g.group.OPTIONS(relativePath, handlers...)
	default:
		return g.group.GET(relativePath, handlers...)
	}
}

// GET 注册GET请求处理器
func (g *RouteGroup) GET(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("GET", g.basePath+relativePath, "")
	return g.group.GET(relativePath, handlers...)
}

// POST 注册POST请求处理器
func (g *RouteGroup) POST(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("POST", g.basePath+relativePath, "")
	return g.group.POST(relativePath, handlers...)
}

// PUT 注册PUT请求处理器
func (g *RouteGroup) PUT(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("PUT", g.basePath+relativePath, "")
	return g.group.PUT(relativePath, handlers...)
}

// DELETE 注册DELETE请求处理器
func (g *RouteGroup) DELETE(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("DELETE", g.basePath+relativePath, "")
	return g.group.DELETE(relativePath, handlers...)
}

// PATCH 注册PATCH请求处理器
func (g *RouteGroup) PATCH(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("PATCH", g.basePath+relativePath, "")
	return g.group.PATCH(relativePath, handlers...)
}

// HEAD 注册HEAD请求处理器
func (g *RouteGroup) HEAD(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("HEAD", g.basePath+relativePath, "")
	return g.group.HEAD(relativePath, handlers...)
}

// OPTIONS 注册OPTIONS请求处理器
func (g *RouteGroup) OPTIONS(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("OPTIONS", g.basePath+relativePath, "")
	return g.group.OPTIONS(relativePath, handlers...)
}

// Any 注册所有HTTP方法请求处理器
func (g *RouteGroup) Any(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes {
	g.registrar.collector.AddRoute("GET", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("POST", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("PUT", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("DELETE", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("PATCH", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("HEAD", g.basePath+relativePath, "")
	g.registrar.collector.AddRoute("OPTIONS", g.basePath+relativePath, "")
	return g.group.Any(relativePath, handlers...)
}

// Use 使用中间件
func (g *RouteGroup) Use(middleware ...gin.HandlerFunc) gin.IRoutes {
	return g.group.Use(middleware...)
}

// RouteWithPermission 使用自定义权限信息注册路由
type RouteWithPermission struct {
	Route *RouteGroup
}

// GET 注册带权限信息的GET请求
func (r *RouteWithPermission) GET(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("GET", relativePath, options, handlers)
}

// POST 注册带权限信息的POST请求
func (r *RouteWithPermission) POST(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("POST", relativePath, options, handlers)
}

// PUT 注册带权限信息的PUT请求
func (r *RouteWithPermission) PUT(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("PUT", relativePath, options, handlers)
}

// DELETE 注册带权限信息的DELETE请求
func (r *RouteWithPermission) DELETE(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("DELETE", relativePath, options, handlers)
}

// PATCH 注册带权限信息的PATCH请求
func (r *RouteWithPermission) PATCH(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("PATCH", relativePath, options, handlers)
}

// HEAD 注册带权限信息的HEAD请求
func (r *RouteWithPermission) HEAD(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("HEAD", relativePath, options, handlers)
}

// OPTIONS 注册带权限信息的OPTIONS请求
func (r *RouteWithPermission) OPTIONS(relativePath string, options []RouteOption, handlers ...gin.HandlerFunc) gin.IRoutes {
	return r.Route.method("OPTIONS", relativePath, options, handlers)
}

// WithPermission 获取带权限设置的路由注册器
func (g *RouteGroup) WithPermission() *RouteWithPermission {
	return &RouteWithPermission{Route: g}
}
