package router

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/service"
)

// RouteInfo 路由信息结构体，记录API路由信息
type RouteInfo struct {
	Method      string // HTTP方法
	Path        string // 路由路径
	Resource    string // 资源名称
	Action      string // 操作类型
	Description string // 描述信息
}

// RouteCollector 路由收集器，用于收集API路由并注册为资源
type RouteCollector struct {
	resourcePermService service.ResourcePermissionService
	logger              *zap.Logger
	tracer              trace.Tracer
	routes              []RouteInfo
	// 用于映射HTTP方法到动词的映射
	methodToAction map[string]string
	// 路径参数匹配正则
	paramRegex *regexp.Regexp
}

// NewRouteCollector 创建新的路由收集器
func NewRouteCollector(resourcePermService service.ResourcePermissionService, logger *zap.Logger, tracer trace.Tracer) *RouteCollector {
	return &RouteCollector{
		resourcePermService: resourcePermService,
		logger:              logger,
		tracer:              tracer,
		routes:              make([]RouteInfo, 0),
		methodToAction: map[string]string{
			"GET":    "list",
			"POST":   "create",
			"PUT":    "update",
			"PATCH":  "update",
			"DELETE": "delete",
		},
		paramRegex: regexp.MustCompile(`:[^/]+`),
	}
}

// AddRoute 添加路由信息
func (rc *RouteCollector) AddRoute(method, path, basePath string) {
	// 完整路径，用于日志记录
	fullPath := path

	// 如果basePath为空，则尝试从path中提取
	if basePath == "" {
		// 通常API路径是 /api/v1/...
		parts := strings.Split(strings.TrimPrefix(path, "/"), "/")
		if len(parts) >= 2 {
			// 提取前两个部分作为basePath（比如/api/v1）
			basePath = "/" + parts[0] + "/" + parts[1]
		}
	}

	resource, action := rc.parseRouteToResourceAction(method, path, basePath)
	if resource == "" || action == "" {
		rc.logger.Debug("忽略无法解析的路由",
			zap.String("method", method),
			zap.String("path", path),
			zap.String("basePath", basePath))
		return
	}

	rc.logger.Debug("收集到路由",
		zap.String("method", method),
		zap.String("path", fullPath),
		zap.String("resource", resource),
		zap.String("action", action))

	rc.routes = append(rc.routes, RouteInfo{
		Method:      method,
		Path:        fullPath,
		Resource:    resource,
		Action:      action,
		Description: fmt.Sprintf("%s %s", method, fullPath),
	})
}

// 根据路由路径解析资源和操作
func (rc *RouteCollector) parseRouteToResourceAction(method, path, basePath string) (string, string) {
	// 移除API前缀和版本
	trimmedPath := path
	if basePath != "" {
		trimmedPath = strings.TrimPrefix(path, basePath)
	}

	// 移除URL参数
	if idx := strings.Index(trimmedPath, "?"); idx != -1 {
		trimmedPath = trimmedPath[:idx]
	}

	// 分割路径
	segments := strings.Split(strings.Trim(trimmedPath, "/"), "/")
	if len(segments) == 0 {
		return "", ""
	}

	// 规范化路径，将参数替换为通用标识符
	normalizedSegments := rc.normalizePath(segments)

	// 路径处理逻辑 - 处理不同的路由模式

	// 资源名通常是第一段
	resource := normalizedSegments[0]

	// 特殊情况：RBAC子路由如 /rbac/roles
	if resource == "rbac" && len(normalizedSegments) > 1 {
		resource = normalizedSegments[1]
	}

	// 特殊情况：系统配置子路由如 /system/config/basic
	if resource == "system" && len(normalizedSegments) > 2 {
		if normalizedSegments[1] == "config" {
			resource = "config-" + normalizedSegments[2]
		}
	}

	// 如果路径包含参数，则为单资源操作
	isDetailPath := false
	for _, segment := range segments {
		if strings.HasPrefix(segment, ":") {
			isDetailPath = true
			break
		}
	}

	// 特殊情况：嵌套资源如 /clusters/:id/namespaces
	if len(normalizedSegments) > 2 && normalizedSegments[1] == "id" {
		if len(normalizedSegments) > 2 {
			// 处理嵌套资源：如 /clusters/:id/namespaces
			resource = normalizedSegments[2]
		}
	}

	// 根据HTTP方法和路径确定操作
	var action string
	if val, exists := rc.methodToAction[method]; exists {
		action = val

		// 对于GET请求，根据路径确定是list还是read操作
		if method == "GET" {
			if isDetailPath {
				action = "read"
			} else {
				action = "list"
			}
		}
	} else {
		action = "exec" // 默认操作
	}

	return resource, action
}

// 规范化路径，将参数替换为通用标识符
func (rc *RouteCollector) normalizePath(segments []string) []string {
	if len(segments) == 0 {
		return segments
	}

	// 对于以冒号开头的参数，统一替换为 "id"
	normalized := make([]string, len(segments))
	for i, segment := range segments {
		if strings.HasPrefix(segment, ":") {
			normalized[i] = "id"
		} else {
			normalized[i] = segment
		}
	}
	return normalized
}

// RegisterResources 将收集的路由注册为系统资源
func (rc *RouteCollector) RegisterResources(ctx context.Context) error {
	ctx, span := rc.tracer.Start(ctx, "RouteCollector.RegisterResources")
	defer span.End()

	// 按资源分组
	resourceMap := make(map[string]map[string]bool)
	for _, route := range rc.routes {
		if _, exists := resourceMap[route.Resource]; !exists {
			resourceMap[route.Resource] = make(map[string]bool)
		}
		resourceMap[route.Resource][route.Action] = true
	}

	span.SetAttributes(attribute.Int("collected_resources", len(resourceMap)))
	rc.logger.Info("收集到的API资源", zap.Int("数量", len(resourceMap)))

	// 资源名称映射表，提供更友好的显示名称
	resourceNameMap := map[string]string{
		"users":             "用户",
		"roles":             "角色",
		"permissions":       "权限",
		"groups":            "用户组",
		"resources":         "资源",
		"clusters":          "集群",
		"namespaces":        "命名空间",
		"nodes":             "节点",
		"pods":              "容器组",
		"deployments":       "部署",
		"services":          "服务",
		"ingresses":         "入口",
		"configmaps":        "配置映射",
		"secrets":           "密钥",
		"persistentvolumes": "持久卷",
		"events":            "事件",
		"logs":              "日志",
		"audit":             "审计",
		"approvals":         "审批",
		"config":            "配置",
		"auth":              "认证",
		"oidc":              "OIDC认证",
		"id":                "资源ID",
		"rbac":              "权限管理",
		"system":            "系统",
		"user":              "用户",
		"role":              "角色",
		"permission":        "权限",
		"group":             "用户组",
		"resource":          "资源",
		"cluster":           "集群",
		"namespace":         "命名空间",
		"node":              "节点",
		"pod":               "容器组",
		"deployment":        "部署",
		"service":           "服务",
		"ingress":           "入口",
		"configmap":         "配置映射",
		"secret":            "密钥",
		"persistentvolume":  "持久卷",
		"event":             "事件",
		"log":               "日志",
		"approval":          "审批",
	}

	// 动作名称映射表
	actionNameMap := map[string]string{
		"list":   "列表",
		"create": "创建",
		"read":   "详情",
		"update": "更新",
		"delete": "删除",
		"exec":   "执行",
	}

	// 已创建的资源权限计数
	createdCount := 0
	existingCount := 0

	// 注册资源
	for resource, actions := range resourceMap {
		// 跳过一些特殊路径
		if resource == "ping" || resource == "swagger" || resource == "docs" {
			continue
		}

		// 获取资源友好名称
		resourceName := resource
		if name, exists := resourceNameMap[resource]; exists {
			resourceName = name
		}

		// 收集操作
		actionsList := make([]string, 0, len(actions))
		for action := range actions {
			actionsList = append(actionsList, action)
		}

		// 为每个操作创建资源权限
		for _, action := range actionsList {
			// 获取操作友好名称
			actionName := action
			if name, exists := actionNameMap[action]; exists {
				actionName = name
			}

			resourcePermission := &model.ResourcePermission{
				Name:         fmt.Sprintf("%s%s", resourceName, actionName),
				Type:         model.ResourceTypeAPI,
				ResourceCode: resource,
				ActionCode:   action,
				FullCode:     fmt.Sprintf("%s:%s:%s", model.ResourceTypeAPI, resource, action),
				Description:  fmt.Sprintf("%s相关API操作", resource),
			}

			// 检查资源权限是否已存在
			existingResourcePermission, err := rc.resourcePermService.GetResourcePermissionByFullCode(ctx, resourcePermission.FullCode)
			if err == nil && existingResourcePermission != nil {
				// 资源权限已存在，跳过
				existingCount++
				continue
			}

			// 创建资源权限
			if err := rc.resourcePermService.CreateResourcePermission(ctx, resourcePermission); err != nil {
				rc.logger.Error("创建API资源权限失败", zap.Error(err), zap.String("resource", resource), zap.String("action", action))
				span.SetStatus(codes.Error, err.Error())
				continue
			}

			createdCount++
			rc.logger.Debug("自动注册API资源权限成功", zap.String("资源", resource), zap.String("操作", action))
		}
	}

	rc.logger.Info("API资源权限注册完成",
		zap.Int("新创建", createdCount),
		zap.Int("已存在", existingCount),
		zap.Int("总资源数", len(resourceMap)))

	return nil
}
