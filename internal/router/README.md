# 路由与权限资源自动注册

本模块实现了在注册Gin路由时自动注册API权限资源的功能。

## 主要特性

1. 自动解析API路径，识别资源和操作
2. 支持自定义资源权限信息
3. 支持跳过特定路由的权限注册
4. 支持自定义资源和操作的友好名称

## 使用方法

### 1. 标准方式（自动解析权限）

```go
// 路径: /api/v1/users
// 权限: api:users:list
apiGroup.GET("/users", handler.ListUsers)
```

### 2. 自定义权限信息

```go
// 路径: /api/v1/custom-path
// 权限: api:custom-resource:custom-action
apiGroup.WithPermission().GET("/custom-path", 
    []router.RouteOption{
        router.WithResource("custom-resource"),
        router.WithAction("custom-action"),
        router.WithResourceName("自定义资源"),
        router.WithActionName("自定义操作"),
        router.WithDescription("这是一个自定义权限资源示例"),
    },
    handler.CustomHandler)
```

### 3. 跳过权限注册

```go
// 路径: /api/v1/public-path
// 不会创建权限资源
apiGroup.WithPermission().GET("/public-path", 
    []router.RouteOption{
        router.SkipPermissionRegistration(),
    },
    handler.PublicHandler)
```

## 可用选项函数

- `WithResource(resource string)` - 设置资源标识
- `WithAction(action string)` - 设置操作类型
- `WithResourceName(name string)` - 设置资源友好名称
- `WithActionName(name string)` - 设置操作友好名称
- `WithDescription(desc string)` - 设置描述信息
- `WithResourceType(resourceType string)` - 设置资源类型（默认为"api"）
- `SkipPermissionRegistration()` - 跳过权限注册

## 注意事项

1. 在注册路由时，系统会自动将路由信息收集到内存中
2. 在应用启动时，系统会将收集到的API路径注册为权限资源
3. 对于标准RESTful API，系统会自动识别资源和操作
4. 对于特殊或自定义API，可以使用`WithPermission()`方法指定权限信息
5. 公共接口可以使用`SkipPermissionRegistration()`选项跳过权限注册
