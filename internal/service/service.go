package service

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/auth"
	"kubeops/internal/config"
	"kubeops/internal/oidc"
	redisManager "kubeops/internal/redis"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"
	"kubeops/pkg/lark"
	"kubeops/pkg/obs"
)



// service 服务层实现 - 清理版本
type service struct {
	repo                repository.Repository
	userService         UserService
	auditService        AuditService
	clusterService      ClusterService
	projectService      ProjectService
	applicationService  ApplicationService
	configService       ConfigService
	authService         AuthService
	casbinService       *CasbinService
	rbacService         RBACService         // 统一RBAC服务
	jwtService          auth.JWTService     // 新版JWT服务
	redisManager        redisManager.RedisManager  // Redis管理器
	oidcService         OIDCService
	approvalService     ApprovalService
	larkService         LarkService
	systemConfigService SystemConfigService
	userGroupService    UserGroupService
	integrationService  IntegrationService
	k8sRBACService      k8s.RBACService
	logger              *zap.Logger
	tracer              trace.Tracer
}

// NewServiceWithCasbin 创建服务实例（包含Casbin服务）- 清理版本
func NewServiceWithCasbin(
	repo repository.Repository,
	logger *zap.Logger,
	tracer trace.Tracer,
	cfg *config.Config,
	redisManager redisManager.RedisManager,
	jwtService auth.JWTService,
	feishuConfig *lark.FeishuConfig,
	casbinService *CasbinService,
	obsClient *obs.OBSClient,
) Service {
	userSvc := NewUserService(repo, logger, tracer)

	// 创建集成服务
	integrationSvc := NewIntegrationService(repo, logger, tracer)

	// 如果提供了飞书配置，更新集成服务
	if feishuConfig != nil {
		integrationSvc.UpdateFeishuConfig(feishuConfig)
	}

	systemConfigSvc := NewSystemConfigService(repo, logger, tracer)

	// 从系统配置加载OIDC配置
	var oidcConfig *oidc.OIDCConfig
	sysOIDCConfig, err := systemConfigSvc.GetRawOIDCConfig(context.Background())
	if err == nil && sysOIDCConfig != nil {
		oidcConfig = &oidc.OIDCConfig{
			Enabled:      sysOIDCConfig.Enabled,
			IssuerURL:    sysOIDCConfig.IssuerURL,
			ClientID:     sysOIDCConfig.ClientID,
			ClientSecret: sysOIDCConfig.ClientSecret,
			RedirectURI:  sysOIDCConfig.RedirectURI,
			Scopes:       sysOIDCConfig.Scopes,
			GroupsClaim:  sysOIDCConfig.GroupsClaim,
		}

		// 记录OIDC配置加载情况（不记录敏感信息）
		logger.Info("已加载OIDC配置",
			zap.Bool("enabled", oidcConfig.Enabled),
			zap.String("issuer_url", oidcConfig.IssuerURL),
			zap.String("client_id", oidcConfig.ClientID),
			zap.Bool("has_client_secret", oidcConfig.ClientSecret != ""),
			zap.String("redirect_uri", oidcConfig.RedirectURI))
	} else {
		logger.Warn("加载OIDC配置失败，使用默认空配置", zap.Error(err))
		oidcConfig = &oidc.OIDCConfig{
			Enabled:      false,
			IssuerURL:    "",
			ClientID:     "",
			ClientSecret: "",
			RedirectURI:  "",
			Scopes:       "openid,profile,email",
			GroupsClaim:  "groups",
		}
	}

	// 从系统配置加载OBS配置
	var actualOBSClient *obs.OBSClient
	if obsClient != nil {
		actualOBSClient = obsClient
	} else {
		sysOBSConfig, obsErr := systemConfigSvc.GetRawOBSConfig(context.Background())
		if obsErr == nil && sysOBSConfig != nil && sysOBSConfig.Enabled {
			var err error
			actualOBSClient, err = obs.NewOBSClient(&obs.OBSConfig{
				Endpoint:      sysOBSConfig.Endpoint,
				AccessKey:     sysOBSConfig.AccessKey,
				SecretKey:     sysOBSConfig.SecretKey,
				Bucket:        sysOBSConfig.Bucket,
				Region:        sysOBSConfig.Region,
				EncryptionKey: sysOBSConfig.EncryptionKey,
			})
			if err != nil {
				logger.Error("初始化OBS客户端失败", zap.Error(err))
			} else {
				logger.Info("已从数据库初始化OBS客户端",
					zap.String("endpoint", sysOBSConfig.Endpoint),
					zap.String("bucket", sysOBSConfig.Bucket))
			}
		}
	}

	// 创建认证服务 - 使用新的JWT架构
	authSvc, err := NewAuthService(
		repo,
		logger,
		tracer,
		cfg,
		redisManager,
		jwtService,
		oidcConfig,
	)
	if err != nil {
		logger.Error("创建认证服务失败", zap.Error(err))
		// 继续流程，不要因为认证服务创建失败而中断整个服务初始化
	}

	// 创建集群管理器
	clusterManager := k8s.NewClusterManager()

	// 创建集群服务
	clusterSvc := NewClusterService(repo, clusterManager, logger, tracer)

	// 创建审计服务
	auditSvc := NewAuditService(repo, tracer)
	// 如果OBS客户端初始化成功，传递给审计服务
	if actualOBSClient != nil {
		if auditService, ok := auditSvc.(interface {
			UpdateOBSClient(obsClient interface{}) error
		}); ok {
			if err := auditService.UpdateOBSClient(actualOBSClient); err != nil {
				logger.Error("更新审计服务的OBS客户端失败", zap.Error(err))
			}
		}
	}

	// 创建审批服务
	approvalSvc := NewApprovalService(repo, tracer)

	// 创建用户组服务
	userGroupSvc := NewUserGroupService(repo, tracer)

	// RBAC服务已经通过参数传入，集群管理器已经创建

	// 创建项目服务
	projectSvc := NewProjectService(repo, clusterManager, casbinService, logger, tracer)



	// 创建应用同步服务
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379", // 这里应该从配置中读取
	})
	applicationSyncSvc := NewApplicationSyncService(repo, clusterManager, redisClient, logger, tracer)

	// 创建应用服务
	applicationSvc := NewApplicationService(repo, clusterManager, applicationSyncSvc, logger, tracer)

	// 创建K8s RBAC服务
	k8sRBACSvc := k8s.NewRBACService(clusterManager)

	// 创建统一RBAC服务
	rbacSvc := NewRBACService(casbinService, k8sRBACSvc, repo, logger, tracer)

	// 用户服务、认证服务、系统配置服务、集成服务已经在前面创建

	return &service{
		repo:                repo,
		authService:         authSvc,
		userService:         userSvc,
		casbinService:       casbinService,
		rbacService:         rbacSvc,
		clusterService:      clusterSvc,
		projectService:      projectSvc,
		applicationService:  applicationSvc,
		auditService:        auditSvc,
		approvalService:     approvalSvc,
		systemConfigService: systemConfigSvc,
		userGroupService:    userGroupSvc,
		integrationService:  integrationSvc,
		k8sRBACService:      k8sRBACSvc,
		jwtService:          jwtService,      // 新版JWT服务
		redisManager:        redisManager,    // Redis管理器
		logger:              logger,
		tracer:              tracer,
	}
}

// Integration 获取集成服务
func (s *service) Integration() IntegrationService {
	return s.integrationService
}

// Auth 获取认证服务
func (s *service) Auth() AuthService {
	return s.authService
}

// User 获取用户服务
func (s *service) User() UserService {
	return s.userService
}

// Casbin 获取Casbin服务
func (s *service) Casbin() *CasbinService {
	return s.casbinService
}

// JWT 获取JWT服务
func (s *service) JWT() auth.JWTService {
	return s.jwtService
}



// Cluster 获取集群服务
func (s *service) Cluster() ClusterService {
	return s.clusterService
}

// Audit 获取审计服务
func (s *service) Audit() AuditService {
	return s.auditService
}

// Approval 获取审批服务
func (s *service) Approval() ApprovalService {
	return s.approvalService
}

// SystemConfig 获取系统配置服务
func (s *service) SystemConfig() SystemConfigService {
	return s.systemConfigService
}

// Project 获取项目服务
func (s *service) Project() ProjectService {
	return s.projectService
}

// Application 获取应用服务
func (s *service) Application() ApplicationService {
	return s.applicationService
}



// Config 获取配置服务（存根实现）
func (s *service) Config() ConfigService {
	// 返回一个简单的存根实现
	return &stubConfigService{}
}

// stubConfigService 配置服务存根实现
type stubConfigService struct{}

func (s *stubConfigService) GetConfig(ctx context.Context, key string) (string, error) {
	return "", fmt.Errorf("config service not implemented")
}

func (s *stubConfigService) SetConfig(ctx context.Context, key, value string) error {
	return fmt.Errorf("config service not implemented")
}

func (s *stubConfigService) CreateKeycloakGroupMapping(ctx context.Context, keycloakGroupName, localGroupName string) error {
	return fmt.Errorf("CreateKeycloakGroupMapping not implemented")
}

func (s *stubConfigService) DeleteKeycloakGroupMapping(ctx context.Context, keycloakGroupName string) error {
	return fmt.Errorf("DeleteKeycloakGroupMapping not implemented")
}

func (s *stubConfigService) GetKeycloakGroupMappings(ctx context.Context) ([]KeycloakGroupMappingConfig, error) {
	return nil, fmt.Errorf("GetKeycloakGroupMappings not implemented")
}

// UserGroup 获取用户组服务
func (s *service) UserGroup() UserGroupService {
	return s.userGroupService
}

// RBAC 获取RBAC服务
func (s *service) RBAC() RBACService {
	return s.rbacService
}

// K8sRBAC 获取K8s RBAC服务
func (s *service) K8sRBAC() k8s.RBACService {
	return s.k8sRBACService
}



// Lark 获取飞书服务
func (s *service) Lark() LarkService {
	return s.larkService
}

// OIDC 获取OIDC服务
func (s *service) OIDC() OIDCService {
	return s.oidcService
}

// Close 关闭服务层资源
func (s *service) Close(ctx context.Context) error {
	// 这里可以添加任何需要的清理操作
	s.logger.Info("Shutting down services...")
	return nil
}

// GetRepo 获取仓库实例
func (s *service) GetRepo() repository.Repository {
	return s.repo
}
