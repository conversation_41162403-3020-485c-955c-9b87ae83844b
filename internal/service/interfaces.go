package service

import (
	"context"

	"kubeops/internal/auth"
	"kubeops/internal/model"

	"github.com/casbin/casbin/v2"
)

// RBACService RBAC服务接口 - 统一的权限管理服务
type RBACService interface {
	// 核心权限检查
	CheckPermission(ctx context.Context, userID uint, resourcePath string, action string) (bool, error)
	BatchCheckPermission(ctx context.Context, userID uint, permissions []string) (map[string]bool, error)
	CheckPermissionWithAttrs(ctx context.Context, userID uint, resourcePath string, action string, attrs map[string]interface{}) (bool, error)
	CheckHierarchicalPermission(ctx context.Context, userID uint, resourcePath string, action string, scopeLevel string) (bool, error)

	// 权限资源管理
	CreatePermission(ctx context.Context, req *model.PermissionCreateRequest) (*model.ResourcePermission, error)
	GetPermissionByID(ctx context.Context, id uint) (*model.ResourcePermission, error)
	GetPermissionByPath(ctx context.Context, resourcePath string) (*model.ResourcePermission, error)
	UpdatePermission(ctx context.Context, id uint, req *model.PermissionUpdateRequest) (*model.ResourcePermission, error)
	DeletePermission(ctx context.Context, id uint) error
	ListPermissions(ctx context.Context, page, pageSize int) ([]*model.ResourcePermission, int64, error)
	ValidatePermissionPath(resourcePath string) error

	// 用户权限管理
	GetUserPermissions(ctx context.Context, userID uint) ([]string, error)
	GetUserEffectivePermissions(ctx context.Context, userID uint) ([]*model.ResourcePermission, error)
	GrantUserPermission(ctx context.Context, userID uint, req *model.UserPermissionRequest, grantedBy uint) error
	RevokeUserPermission(ctx context.Context, userID, permissionID uint) error
	GetUserDirectPermissions(ctx context.Context, userID uint) ([]*model.UserResourcePermission, error)

	// 用户组权限管理
	AssignGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error
	RemoveGroupPermissions(ctx context.Context, groupID uint, permissionIDs []uint) error
	GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error)

	// 权限缓存管理
	RefreshUserPermissionCache(ctx context.Context, userID uint) error
	BatchRefreshPermissionCache(ctx context.Context, userIDs []uint) error

	// Casbin策略管理
	AddPolicy(ctx context.Context, subject, object, action string) error
	RemovePolicy(ctx context.Context, subject, object, action string) error
	LoadPolicies(ctx context.Context) error
	SavePolicies(ctx context.Context) error

	// RBAC模型管理
	GetActiveRBACModel(ctx context.Context) (*model.RBACModel, error)
	CreateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error
	UpdateRBACModel(ctx context.Context, rbacModel *model.RBACModel) error
	SetActiveRBACModel(ctx context.Context, id uint) error

	// 系统初始化
	InitSystemPermissions(ctx context.Context) error

	// GetEnforcer 获取Casbin执行器
	GetEnforcer() *casbin.Enforcer
}

// UserService 用户服务接口
type UserService interface {
	// 基础CRUD操作
	CreateUser(ctx context.Context, req *model.UserCreateRequest) (*model.User, error)
	GetUserByID(ctx context.Context, id uint) (*model.User, error)
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	UpdateUser(ctx context.Context, id uint, req *model.UserUpdateRequest) (*model.User, error)
	DeleteUser(ctx context.Context, id uint) error
	ListUsers(ctx context.Context, page, pageSize int) ([]*model.User, int64, error)

	// 认证相关
	AuthenticateUser(ctx context.Context, username, password string) (*model.User, error)
	UpdateLastLogin(ctx context.Context, id uint) error
	ChangePassword(ctx context.Context, id uint, oldPassword, newPassword string) error

	// OIDC集成
	CreateOrUpdateOIDCUser(ctx context.Context, userInfo *model.OIDCUserInfo) (*model.User, error)
	GetUserByOIDCSubject(ctx context.Context, subject string) (*model.User, error)

	// 飞书集成
	GetUserByFeishuID(ctx context.Context, feishuOpenID, feishuUnionID string) (*model.User, error)
	SyncFeishuUser(ctx context.Context, userInfo *model.OIDCUserInfo) (*model.User, error)

	// 状态管理
	ActivateUser(ctx context.Context, id uint) error
	DeactivateUser(ctx context.Context, id uint) error
	LockUser(ctx context.Context, id uint) error
	UnlockUser(ctx context.Context, id uint) error
}



// UserGroupService 用户组服务接口 - 专注于用户组CRUD和成员管理
type UserGroupService interface {
	// 基础CRUD操作
	CreateUserGroup(ctx context.Context, req *model.UserGroupCreateRequest) (*model.UserGroup, error)
	GetUserGroupByID(ctx context.Context, id uint) (*model.UserGroup, error)
	GetUserGroupByName(ctx context.Context, name string) (*model.UserGroup, error)
	UpdateUserGroup(ctx context.Context, id uint, req *model.UserGroupUpdateRequest) (*model.UserGroup, error)
	DeleteUserGroup(ctx context.Context, id uint) error
	ListUserGroups(ctx context.Context, page, pageSize int) ([]*model.UserGroup, int64, error)

	// 成员关系管理
	AddUserToGroup(ctx context.Context, userID, groupID uint) error
	RemoveUserFromGroup(ctx context.Context, userID, groupID uint) error
	AddUsersToGroup(ctx context.Context, groupID uint, userIDs []uint) error
	RemoveUsersFromGroup(ctx context.Context, groupID uint, userIDs []uint) error
	SetGroupMembers(ctx context.Context, groupID uint, userIDs []uint) error
	GetGroupMembers(ctx context.Context, groupID uint) ([]*model.User, error)
	GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error)
	IsUserInGroup(ctx context.Context, userID, groupID uint) (bool, error)

	// 权限管理
	GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error)
	AssignPermissionsToGroup(ctx context.Context, groupID uint, permissionIDs []uint) error
	RemovePermissionsFromGroup(ctx context.Context, groupID uint, permissionIDs []uint) error

	// Keycloak集成
	SyncKeycloakGroups(ctx context.Context) error
	CreateOrUpdateKeycloakGroup(ctx context.Context, externalID, name, displayName string) (*model.UserGroup, error)
	GetUserGroupByExternalID(ctx context.Context, externalID string) (*model.UserGroup, error)
	SyncUserGroupMembership(ctx context.Context, userID uint, keycloakGroups []string) error
}

// ProjectService 项目服务接口
type ProjectService interface {
	// 基础CRUD操作
	CreateProject(ctx context.Context, req *model.ProjectCreateRequest) (*model.Project, error)
	GetProjectByID(ctx context.Context, id uint) (*model.Project, error)
	GetProjectByName(ctx context.Context, name string) (*model.Project, error)
	UpdateProject(ctx context.Context, id uint, req *model.ProjectUpdateRequest) (*model.Project, error)
	DeleteProject(ctx context.Context, id uint) error
	ListProjects(ctx context.Context, page, pageSize int) ([]*model.Project, int64, error)

	// 状态管理
	ActivateProject(ctx context.Context, id uint) error
	DeactivateProject(ctx context.Context, id uint) error
	ArchiveProject(ctx context.Context, id uint) error
	UnarchiveProject(ctx context.Context, id uint) error

	// 多集群部署支持
	AddProjectToCluster(ctx context.Context, projectID, clusterID uint, namespace string) (*model.ProjectCluster, error)
	RemoveProjectFromCluster(ctx context.Context, projectID, clusterID uint) error
	GetProjectClusters(ctx context.Context, projectID uint) ([]*model.ProjectCluster, error)
	UpdateProjectClusterConfig(ctx context.Context, projectClusterID uint, config map[string]interface{}) error

	// 项目成员关系管理（仅用于界面展示）
	AddProjectMember(ctx context.Context, projectID, userID uint, role string) error
	RemoveProjectMember(ctx context.Context, projectID, userID uint) error
	UpdateProjectMemberRole(ctx context.Context, projectID, userID uint, role string) error
	GetProjectMembers(ctx context.Context, projectID uint) ([]*model.User, error)
	GetUserProjects(ctx context.Context, userID uint) ([]*model.Project, error)
	IsProjectMember(ctx context.Context, projectID, userID uint) (bool, error)
}

// ClusterService 集群服务接口
type ClusterService interface {
	// 基础CRUD操作
	CreateCluster(ctx context.Context, req *model.ClusterCreateRequest) (*model.Cluster, error)
	GetClusterByID(ctx context.Context, id uint) (*model.Cluster, error)
	GetClusterByName(ctx context.Context, name string) (*model.Cluster, error)
	UpdateCluster(ctx context.Context, id uint, req *model.ClusterUpdateRequest) (*model.Cluster, error)
	DeleteCluster(ctx context.Context, id uint) error
	ListClusters(ctx context.Context, page, pageSize int) ([]*model.Cluster, int64, error)

	// 集群连接和健康检查
	TestClusterConnection(ctx context.Context, kubeconfig string) (*model.ClusterInfo, error)
	HealthCheckCluster(ctx context.Context, clusterID uint) error
	GetClusterInfo(ctx context.Context, clusterID uint) (*model.ClusterInfo, error)
	UpdateClusterStatus(ctx context.Context, clusterID uint, status model.ClusterStatus) error

	// 集群元数据缓存（Informer + Redis混合架构）
	StartClusterInformers(ctx context.Context, clusterID uint) error
	StopClusterInformers(ctx context.Context, clusterID uint) error
	RestartClusterInformers(ctx context.Context, clusterID uint) error
	GetInformerStatus(ctx context.Context, clusterID uint) (map[string]interface{}, error)

	// 集群资源监控和事件处理
	GetClusterResources(ctx context.Context, clusterID uint, resourceType string) (interface{}, error)
	GetClusterEvents(ctx context.Context, clusterID uint, namespace string) (interface{}, error)
	GetClusterMetrics(ctx context.Context, clusterID uint) (map[string]interface{}, error)

	// 集群状态管理
	EnableCluster(ctx context.Context, id uint) error
	DisableCluster(ctx context.Context, id uint) error
	SetMaintenanceMode(ctx context.Context, id uint) error
	ExitMaintenanceMode(ctx context.Context, id uint) error
}



// ApplicationService 应用服务接口
type ApplicationService interface {
	// 基础CRUD操作
	CreateApplication(ctx context.Context, req *model.ApplicationCreateRequest) (*model.Application, error)
	GetApplicationByID(ctx context.Context, id uint) (*model.Application, error)
	GetApplicationByName(ctx context.Context, projectID uint, name string) (*model.Application, error)
	UpdateApplication(ctx context.Context, id uint, req *model.ApplicationUpdateRequest) (*model.Application, error)
	DeleteApplication(ctx context.Context, id uint) error
	ListApplications(ctx context.Context, page, pageSize int, projectID uint) ([]*model.Application, int64, error)

	// 实时同步和监听（核心功能）
	StartApplicationSync(ctx context.Context, clusterID uint) error
	StopApplicationSync(ctx context.Context, clusterID uint) error
	HandleWorkloadEvent(ctx context.Context, event *model.WorkloadEvent) error
	SyncWorkloadToApplication(ctx context.Context, clusterID uint, namespace, workloadType, workloadName string, eventType string) error

	// 应用发现和完善
	DiscoverApplicationsInCluster(ctx context.Context, clusterID uint, projectID uint) (*model.DiscoverSummary, error)
	CompleteApplicationInfo(ctx context.Context, id uint, req *model.ApplicationCompleteRequest) (*model.Application, error)
	GetIncompleteApplications(ctx context.Context, projectID uint) ([]*model.Application, error)

	// 数据一致性检查
	CheckDataConsistency(ctx context.Context, clusterID uint) (*model.ConsistencyReport, error)
	DetectOrphanApplications(ctx context.Context, clusterID uint) ([]*model.Application, error)
	DetectMissingApplications(ctx context.Context, clusterID uint) ([]*model.WorkloadInfo, error)
	ReconcileApplications(ctx context.Context, clusterID uint) error

	// 应用状态管理
	ActivateApplication(ctx context.Context, id uint) error
	DeactivateApplication(ctx context.Context, id uint) error
	ArchiveApplication(ctx context.Context, id uint) error
	UnarchiveApplication(ctx context.Context, id uint) error

	// 多集群部署管理
	DeployToCluster(ctx context.Context, applicationID, projectClusterID uint, config *model.ApplicationClusterConfig) (*model.ApplicationCluster, error)
	UpdateClusterDeployment(ctx context.Context, applicationClusterID uint, config *model.ApplicationClusterConfig) (*model.ApplicationCluster, error)
	RemoveFromCluster(ctx context.Context, applicationID, projectClusterID uint) error
	GetApplicationClusters(ctx context.Context, applicationID uint) ([]*model.ApplicationCluster, error)
	GetClusterApplications(ctx context.Context, projectClusterID uint) ([]*model.ApplicationCluster, error)

	// 应用成员管理（仅用于界面展示）
	AddApplicationMember(ctx context.Context, applicationID, userID uint, role string) error
	RemoveApplicationMember(ctx context.Context, applicationID, userID uint) error
	UpdateApplicationMemberRole(ctx context.Context, applicationID, userID uint, role string) error
	GetApplicationMembers(ctx context.Context, applicationID uint) ([]*model.User, error)
	GetUserApplications(ctx context.Context, userID uint) ([]*model.Application, error)
	IsApplicationMember(ctx context.Context, applicationID, userID uint) (bool, error)

	// 应用统计和监控
	GetApplicationStats(ctx context.Context, applicationID uint) (map[string]interface{}, error)
	GetProjectApplicationStats(ctx context.Context, projectID uint) (map[string]interface{}, error)
	GetApplicationHealth(ctx context.Context, applicationID uint) (map[string]interface{}, error)
}

// Service 服务聚合接口，提供访问各种服务的方法
type Service interface {
	// Auth 认证服务
	Auth() AuthService

	// User 用户服务
	User() UserService

	// Casbin 权限管理服务
	Casbin() *CasbinService

	// RBAC 统一权限管理服务
	RBAC() RBACService

	// JWT 认证服务
	JWT() auth.JWTService



	// UserGroup 用户组服务
	UserGroup() UserGroupService

	// Cluster 集群服务
	Cluster() ClusterService

	// Audit 审计服务
	Audit() AuditService

	// SystemConfig 系统配置服务
	SystemConfig() SystemConfigService

	// Config 配置服务
	Config() ConfigService

	// Integration 集成服务
	Integration() IntegrationService
}
