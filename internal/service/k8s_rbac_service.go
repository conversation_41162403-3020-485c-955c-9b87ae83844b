package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"kubeops/internal/repository"
)

// K8sRBACService K8s RBAC权限同步服务 - 按照technical-design.md实现
type K8sRBACService struct {
	k8sClients  map[string]kubernetes.Interface
	repo        repository.Repository
	logger      *zap.Logger
	tracer      trace.Tracer
}

// NewK8sRBACService 创建K8s RBAC服务
func NewK8sRBACService(k8sClients map[string]kubernetes.Interface, repo repository.Repository, logger *zap.Logger, tracer trace.Tracer) *K8sRBACService {
	return &K8sRBACService{
		k8sClients: k8sClients,
		repo:       repo,
		logger:     logger,
		tracer:     tracer,
	}
}

// SyncRequest 权限同步请求
type SyncRequest struct {
	SubjectType    string   // user, group
	SubjectName    string   // 用户名或用户组名
	SubjectID      uint     // 用户ID或用户组ID
	ResourceType   string   // k8s
	ResourcePath   string   // 权限路径
	Actions        []string // 操作列表
	ClusterNames   []string // 目标集群列表
	ForceRecreate  bool     // 是否强制重新创建
}

// ResourcePathInfo 权限路径解析结果
type ResourcePathInfo struct {
	ClusterName     string
	Namespace       string
	Application     string
	ResourceType    string
	PermissionLevel string // cluster, project, application
}

// SyncPermissionsToK8s 同步权限到K8s集群
func (s *K8sRBACService) SyncPermissionsToK8s(ctx context.Context, req *SyncRequest) error {
	ctx, span := s.tracer.Start(ctx, "K8sRBACService.SyncPermissionsToK8s")
	defer span.End()

	// 1. 解析权限路径
	pathInfo, err := s.parseResourcePath(req.ResourcePath)
	if err != nil {
		return fmt.Errorf("解析权限路径失败: %w", err)
	}

	// 2. 确定目标集群
	targetClusters := req.ClusterNames
	if len(targetClusters) == 0 && pathInfo.ClusterName != "" && pathInfo.ClusterName != "*" {
		targetClusters = []string{pathInfo.ClusterName}
	}

	// 3. 为每个集群同步权限
	for _, clusterName := range targetClusters {
		client, err := s.getK8sClient(clusterName)
		if err != nil {
			s.logger.Error("获取K8s客户端失败", zap.String("cluster", clusterName), zap.Error(err))
			continue
		}

		// 更新路径信息中的集群名称
		pathInfo.ClusterName = clusterName

		// 根据授权类型同步权限
		switch req.SubjectType {
		case "user":
			err = s.syncUserPermissions(ctx, client, req, pathInfo)
		case "group":
			err = s.syncGroupPermissions(ctx, client, req, pathInfo)
		default:
			err = fmt.Errorf("不支持的主体类型: %s", req.SubjectType)
		}

		if err != nil {
			s.logger.Error("同步权限失败",
				zap.String("cluster", clusterName),
				zap.String("subject_type", req.SubjectType),
				zap.String("subject_name", req.SubjectName),
				zap.Error(err))
		} else {
			s.logger.Info("权限同步成功",
				zap.String("cluster", clusterName),
				zap.String("subject_type", req.SubjectType),
				zap.String("subject_name", req.SubjectName))
		}
	}

	return nil
}

// syncGroupPermissions 同步用户组权限
func (s *K8sRBACService) syncGroupPermissions(ctx context.Context, client kubernetes.Interface, req *SyncRequest, pathInfo *ResourcePathInfo) error {
	// 1. 生成Role资源
	role := s.generateRole(req.SubjectName, pathInfo, req.Actions, "group")

	// 2. 创建或更新Role
	_, err := client.RbacV1().Roles(pathInfo.Namespace).Get(ctx, role.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			_, err = client.RbacV1().Roles(pathInfo.Namespace).Create(ctx, role, metav1.CreateOptions{})
		} else {
			return fmt.Errorf("获取Role失败: %w", err)
		}
	} else {
		if req.ForceRecreate {
			// 删除后重新创建
			err = client.RbacV1().Roles(pathInfo.Namespace).Delete(ctx, role.Name, metav1.DeleteOptions{})
			if err != nil {
				return fmt.Errorf("删除Role失败: %w", err)
			}
			_, err = client.RbacV1().Roles(pathInfo.Namespace).Create(ctx, role, metav1.CreateOptions{})
		} else {
			_, err = client.RbacV1().Roles(pathInfo.Namespace).Update(ctx, role, metav1.UpdateOptions{})
		}
	}

	if err != nil {
		return fmt.Errorf("创建/更新Role失败: %w", err)
	}

	// 3. 生成RoleBinding资源
	roleBinding := s.generateRoleBinding(req.SubjectName, pathInfo, "group")

	// 4. 创建或更新RoleBinding
	_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Get(ctx, roleBinding.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Create(ctx, roleBinding, metav1.CreateOptions{})
		} else {
			return fmt.Errorf("获取RoleBinding失败: %w", err)
		}
	} else {
		if req.ForceRecreate {
			// 删除后重新创建
			err = client.RbacV1().RoleBindings(pathInfo.Namespace).Delete(ctx, roleBinding.Name, metav1.DeleteOptions{})
			if err != nil {
				return fmt.Errorf("删除RoleBinding失败: %w", err)
			}
			_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Create(ctx, roleBinding, metav1.CreateOptions{})
		} else {
			_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Update(ctx, roleBinding, metav1.UpdateOptions{})
		}
	}

	if err != nil {
		return fmt.Errorf("创建/更新RoleBinding失败: %w", err)
	}

	return nil
}

// syncUserPermissions 同步用户权限
func (s *K8sRBACService) syncUserPermissions(ctx context.Context, client kubernetes.Interface, req *SyncRequest, pathInfo *ResourcePathInfo) error {
	// 用户权限同步逻辑与用户组类似，但使用User类型的Subject
	role := s.generateRole(req.SubjectName, pathInfo, req.Actions, "user")
	roleBinding := s.generateRoleBinding(req.SubjectName, pathInfo, "user")

	// 创建或更新Role
	_, err := client.RbacV1().Roles(pathInfo.Namespace).Get(ctx, role.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			_, err = client.RbacV1().Roles(pathInfo.Namespace).Create(ctx, role, metav1.CreateOptions{})
		} else {
			return fmt.Errorf("获取Role失败: %w", err)
		}
	} else {
		_, err = client.RbacV1().Roles(pathInfo.Namespace).Update(ctx, role, metav1.UpdateOptions{})
	}

	if err != nil {
		return fmt.Errorf("创建/更新Role失败: %w", err)
	}

	// 创建或更新RoleBinding
	_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Get(ctx, roleBinding.Name, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Create(ctx, roleBinding, metav1.CreateOptions{})
		} else {
			return fmt.Errorf("获取RoleBinding失败: %w", err)
		}
	} else {
		_, err = client.RbacV1().RoleBindings(pathInfo.Namespace).Update(ctx, roleBinding, metav1.UpdateOptions{})
	}

	if err != nil {
		return fmt.Errorf("创建/更新RoleBinding失败: %w", err)
	}

	return nil
}

// generateRole 生成Role资源
func (s *K8sRBACService) generateRole(subjectName string, pathInfo *ResourcePathInfo, actions []string, subjectType string) *rbacv1.Role {
	return &rbacv1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("kubeops-%s-%s-%s", subjectType, subjectName, pathInfo.Namespace),
			Namespace: pathInfo.Namespace,
			Labels: map[string]string{
				"kubeops.io/managed":      "true",
				"kubeops.io/subject-type": subjectType,
				"kubeops.io/subject-name": subjectName,
				"kubeops.io/sync-time":    time.Now().Format(time.RFC3339),
			},
		},
		Rules: []rbacv1.PolicyRule{
			{
				APIGroups: s.getAPIGroups(pathInfo.ResourceType),
				Resources: []string{pathInfo.ResourceType},
				Verbs:     s.convertActions(actions),
			},
		},
	}
}

// generateRoleBinding 生成RoleBinding资源
func (s *K8sRBACService) generateRoleBinding(subjectName string, pathInfo *ResourcePathInfo, subjectType string) *rbacv1.RoleBinding {
	var subjects []rbacv1.Subject

	if subjectType == "group" {
		subjects = []rbacv1.Subject{
			{
				Kind:     "Group",
				Name:     fmt.Sprintf("kubeops:group:%s", subjectName),
				APIGroup: "rbac.authorization.k8s.io",
			},
		}
	} else {
		subjects = []rbacv1.Subject{
			{
				Kind:     "User",
				Name:     fmt.Sprintf("kubeops:user:%s", subjectName),
				APIGroup: "rbac.authorization.k8s.io",
			},
		}
	}

	return &rbacv1.RoleBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      fmt.Sprintf("kubeops-%s-%s-%s", subjectType, subjectName, pathInfo.Namespace),
			Namespace: pathInfo.Namespace,
			Labels: map[string]string{
				"kubeops.io/managed":      "true",
				"kubeops.io/subject-type": subjectType,
				"kubeops.io/subject-name": subjectName,
				"kubeops.io/sync-time":    time.Now().Format(time.RFC3339),
			},
		},
		Subjects: subjects,
		RoleRef: rbacv1.RoleRef{
			Kind:     "Role",
			Name:     fmt.Sprintf("kubeops-%s-%s-%s", subjectType, subjectName, pathInfo.Namespace),
			APIGroup: "rbac.authorization.k8s.io",
		},
	}
}

// parseResourcePath 解析权限路径
func (s *K8sRBACService) parseResourcePath(resourcePath string) (*ResourcePathInfo, error) {
	parts := strings.Split(resourcePath, ":")
	if len(parts) < 4 {
		return nil, fmt.Errorf("权限路径格式无效")
	}

	info := &ResourcePathInfo{}

	for i := 0; i < len(parts); i += 2 {
		if i+1 >= len(parts) {
			break
		}

		key := parts[i]
		value := parts[i+1]

		switch key {
		case "cluster":
			info.ClusterName = value
		case "project":
			info.Namespace = value
			info.PermissionLevel = "project"
		case "application":
			info.Application = value
			info.PermissionLevel = "application"
		case "k8s":
			info.ResourceType = value
		}
	}

	return info, nil
}

// getK8sClient 获取K8s客户端
func (s *K8sRBACService) getK8sClient(clusterName string) (kubernetes.Interface, error) {
	client, exists := s.k8sClients[clusterName]
	if !exists {
		return nil, fmt.Errorf("集群 %s 的客户端不存在", clusterName)
	}
	return client, nil
}

// getAPIGroups 获取资源对应的API组
func (s *K8sRBACService) getAPIGroups(resourceType string) []string {
	coreResources := []string{"pods", "services", "configmaps", "secrets", "persistentvolumeclaims"}
	for _, resource := range coreResources {
		if resource == resourceType {
			return []string{""}
		}
	}

	appsResources := []string{"deployments", "statefulsets", "daemonsets", "replicasets"}
	for _, resource := range appsResources {
		if resource == resourceType {
			return []string{"apps"}
		}
	}

	// 默认返回所有常用API组
	return []string{"", "apps", "extensions"}
}

// convertActions 转换操作到K8s动词
func (s *K8sRBACService) convertActions(actions []string) []string {
	var verbs []string
	actionToVerb := map[string][]string{
		"read":   {"get", "list", "watch"},
		"create": {"create"},
		"update": {"update", "patch"},
		"delete": {"delete"},
		"get":    {"get"},
		"list":   {"list"},
		"watch":  {"watch"},
		"exec":   {"create"}, // exec需要create权限
		"*":      {"*"},
	}

	for _, action := range actions {
		if mappedVerbs, exists := actionToVerb[action]; exists {
			verbs = append(verbs, mappedVerbs...)
		}
	}

	// 去重
	verbMap := make(map[string]bool)
	var uniqueVerbs []string
	for _, verb := range verbs {
		if !verbMap[verb] {
			verbMap[verb] = true
			uniqueVerbs = append(uniqueVerbs, verb)
		}
	}

	return uniqueVerbs
}
