package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"

	"kubeops/internal/auth"
	"kubeops/internal/config"
)

// MockJWTService JWT服务的Mock
type MockJWTService struct {
	mock.Mock
}

func (m *MockJWTService) GenerateTokenPair(ctx context.Context, userInfo *auth.UserInfo, deviceInfo *auth.DeviceInfo) (*auth.TokenPair, error) {
	args := m.Called(ctx, userInfo, deviceInfo)
	return args.Get(0).(*auth.TokenPair), args.Error(1)
}

func (m *MockJWTService) VerifyAccessToken(ctx context.Context, token string) (*auth.JWTClaims, error) {
	args := m.Called(ctx, token)
	return args.Get(0).(*auth.JWTClaims), args.Error(1)
}

func (m *MockJWTService) VerifyRefreshToken(ctx context.Context, token string) (*auth.JWTClaims, error) {
	args := m.Called(ctx, token)
	return args.Get(0).(*auth.JWTClaims), args.Error(1)
}

func (m *MockJWTService) RefreshToken(ctx context.Context, refreshToken string) (*auth.TokenPair, error) {
	args := m.Called(ctx, refreshToken)
	return args.Get(0).(*auth.TokenPair), args.Error(1)
}

func (m *MockJWTService) RevokeToken(ctx context.Context, token string, reason string) error {
	args := m.Called(ctx, token, reason)
	return args.Error(0)
}

func (m *MockJWTService) RevokeUserTokens(ctx context.Context, userID uint, reason string) error {
	args := m.Called(ctx, userID, reason)
	return args.Error(0)
}

func (m *MockJWTService) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	args := m.Called(ctx, tokenID)
	return args.Bool(0), args.Error(1)
}

// MockRedisManager Redis管理器的Mock
type MockRedisManager struct {
	mock.Mock
}

func (m *MockRedisManager) Get(ctx context.Context, key string) (string, error) {
	args := m.Called(ctx, key)
	return args.String(0), args.Error(1)
}

func (m *MockRedisManager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	args := m.Called(ctx, key, value, expiration)
	return args.Error(0)
}

func (m *MockRedisManager) Del(ctx context.Context, keys ...string) error {
	args := m.Called(ctx, keys)
	return args.Error(0)
}

func (m *MockRedisManager) Exists(ctx context.Context, keys ...string) (int64, error) {
	args := m.Called(ctx, keys)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisManager) HSet(ctx context.Context, key string, values ...interface{}) error {
	args := m.Called(ctx, key, values)
	return args.Error(0)
}

func (m *MockRedisManager) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(map[string]string), args.Error(1)
}

func (m *MockRedisManager) Keys(ctx context.Context, pattern string) ([]string, error) {
	args := m.Called(ctx, pattern)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockRedisManager) TTL(ctx context.Context, key string) (time.Duration, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(time.Duration), args.Error(1)
}

func (m *MockRedisManager) Expire(ctx context.Context, key string, expiration time.Duration) error {
	args := m.Called(ctx, key, expiration)
	return args.Error(0)
}

func (m *MockRedisManager) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockRedisManager) Close() error {
	args := m.Called()
	return args.Error(0)
}

// 实现其他必需的方法（简化版本）
func (m *MockRedisManager) ZAdd(ctx context.Context, key string, members ...interface{}) error { return nil }
func (m *MockRedisManager) ZCard(ctx context.Context, key string) (int64, error) { return 0, nil }
func (m *MockRedisManager) ZRemRangeByScore(ctx context.Context, key string, opt interface{}) error { return nil }

// TestAuthService_Login 测试登录功能
func TestAuthService_Login(t *testing.T) {
	// 创建Mock对象
	mockJWTService := new(MockJWTService)
	mockRedisManager := new(MockRedisManager)
	
	// 创建测试配置
	cfg := &config.Config{}
	cfg.RedisModules.AuthSecurity.Security.MaxLoginAttempts = 5
	cfg.RedisModules.AuthSecurity.Security.LockoutDuration = 15 * time.Minute
	
	// 创建AuthService（这里需要Mock Repository等其他依赖）
	// 由于依赖较多，这里只测试核心逻辑
	
	t.Run("successful login", func(t *testing.T) {
		// 设置Mock期望
		expectedTokenPair := &auth.TokenPair{
			AccessToken:  "test-access-token",
			RefreshToken: "test-refresh-token",
			TokenType:    "Bearer",
			ExpiresIn:    3600,
		}
		
		mockJWTService.On("GenerateTokenPair", mock.Anything, mock.Anything, mock.Anything).
			Return(expectedTokenPair, nil)
		
		// 验证Mock调用
		mockJWTService.AssertExpectations(t)
	})
}

// TestDeviceInfoBuilder 测试设备信息构建
func TestDeviceInfoBuilder(t *testing.T) {
	tests := []struct {
		name           string
		userAgent      string
		expectedType   string
		expectedDevice string
	}{
		{
			name:         "mobile device",
			userAgent:    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)",
			expectedType: "mobile",
		},
		{
			name:         "desktop browser",
			userAgent:    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
			expectedType: "web",
		},
		{
			name:         "tablet device",
			userAgent:    "Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)",
			expectedType: "tablet",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 这里可以测试设备类型推断逻辑
			// 实际实现中需要从handler中提取相关逻辑
		})
	}
}

// TestTokenPairConversion 测试Token对转换
func TestTokenPairConversion(t *testing.T) {
	// 测试数据
	userInfo := &UserInfo{
		UserID:      1,
		Username:    "testuser",
		Email:       "<EMAIL>",
		DisplayName: "Test User",
		Roles:       []string{"user"},
		Groups:      []string{"developers"},
		Permissions: []string{"read", "write"},
		Status:      "active",
	}
	
	tokenPair := &auth.TokenPair{
		AccessToken:  "test-access-token",
		RefreshToken: "test-refresh-token",
		TokenType:    "Bearer",
		ExpiresIn:    3600,
	}
	
	// 转换为响应格式
	response := ConvertTokenPairToResponse(tokenPair, userInfo, "session-123")
	
	// 验证转换结果
	assert.NotNil(t, response)
	assert.Equal(t, tokenPair.AccessToken, response.AccessToken)
	assert.Equal(t, tokenPair.RefreshToken, response.RefreshToken)
	assert.Equal(t, tokenPair.TokenType, response.TokenType)
	assert.Equal(t, tokenPair.ExpiresIn, response.ExpiresIn)
	assert.Equal(t, userInfo, response.User)
	assert.Equal(t, "session-123", response.SessionID)
}

// TestJWTClaimsConversion 测试JWT Claims转换
func TestJWTClaimsConversion(t *testing.T) {
	// 创建测试JWT Claims
	jwtClaims := &auth.JWTClaims{
		UserID:       1,
		Username:     "testuser",
		Email:        "<EMAIL>",
		DisplayName:  "Test User",
		Roles:        []string{"user"},
		Groups:       []string{"developers"},
		Permissions:  []string{"read", "write"},
		SessionID:    "session-123",
		TokenType:    "access_token",
		TokenVersion: 1,
		LoginTime:    time.Now().Unix(),
		LastActivity: time.Now().Unix(),
	}
	
	// 转换为UserClaims
	userClaims := ConvertJWTClaimsToUserClaims(jwtClaims)
	
	// 验证转换结果
	assert.NotNil(t, userClaims)
	assert.Equal(t, jwtClaims.UserID, userClaims.UserID)
	assert.Equal(t, jwtClaims.Username, userClaims.Username)
	assert.Equal(t, jwtClaims.Email, userClaims.Email)
	assert.Equal(t, jwtClaims.Roles, userClaims.Roles)
	assert.Equal(t, jwtClaims.SessionID, userClaims.SessionID)
	assert.Equal(t, jwtClaims.TokenType, userClaims.TokenType)
}

// TestLoginAttemptLimiting 测试登录尝试限制
func TestLoginAttemptLimiting(t *testing.T) {
	// 这里可以测试登录尝试限制的逻辑
	// 包括：
	// 1. 正常登录不受限制
	// 2. 失败次数达到上限后账户被锁定
	// 3. 锁定时间过后可以重新尝试
	// 4. 成功登录后清除失败记录
	
	t.Run("account locked after max attempts", func(t *testing.T) {
		// 模拟多次失败登录
		// 验证账户被锁定
	})
	
	t.Run("account unlocked after timeout", func(t *testing.T) {
		// 模拟锁定超时
		// 验证可以重新登录
	})
}

// BenchmarkLogin 登录性能测试
func BenchmarkLogin(b *testing.B) {
	// 性能测试可以帮助确保新的JWT架构不会显著影响性能
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		// 执行登录操作
		// 测量性能指标
	}
}
