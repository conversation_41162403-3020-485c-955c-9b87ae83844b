package service

import (
	"sync"

	"kubeops/internal/oidc"
	"kubeops/pkg/lark"
	"kubeops/pkg/obs"
)

// ConfigUpdateHandler 配置更新处理器接口
type ConfigUpdateHandler interface {
	// 处理OIDC配置更新
	HandleOIDCConfigUpdate(config *oidc.OIDCConfig)

	// 处理飞书配置更新
	HandleFeishuConfigUpdate(config *lark.FeishuConfig)

	// 处理OBS配置更新
	HandleOBSConfigUpdate(config *obs.OBSConfig)
}

// ConfigUpdateHandlers 配置更新处理器集合
type ConfigUpdateHandlers struct {
	authService        AuthService
	integrationService IntegrationService
	mu                 sync.RWMutex // 保护处理器引用的读写锁
}

// NewConfigUpdateHandlers 创建配置更新处理器
func NewConfigUpdateHandlers(
	authService AuthService,
	integrationService IntegrationService,
) *ConfigUpdateHandlers {
	return &ConfigUpdateHandlers{
		authService:        authService,
		integrationService: integrationService,
	}
}

// HandleOIDCConfigUpdate 处理OIDC配置更新
func (h *ConfigUpdateHandlers) HandleOIDCConfigUpdate(config *oidc.OIDCConfig) {
	if config == nil {
		return
	}

	h.mu.RLock()
	authService := h.authService
	h.mu.RUnlock()

	if authService != nil {
		authService.UpdateOIDCConfig(config)
	}
}

// HandleFeishuConfigUpdate 处理飞书配置更新
func (h *ConfigUpdateHandlers) HandleFeishuConfigUpdate(config *lark.FeishuConfig) {
	if config == nil {
		return
	}

	h.mu.RLock()
	integrationService := h.integrationService
	h.mu.RUnlock()

	if integrationService != nil {
		integrationService.UpdateFeishuConfig(config)
	}
}

// HandleOBSConfigUpdate 处理OBS配置更新
func (h *ConfigUpdateHandlers) HandleOBSConfigUpdate(config *obs.OBSConfig) {
	if config == nil {
		return
	}

	h.mu.RLock()
	integrationService := h.integrationService
	h.mu.RUnlock()

	if integrationService != nil {
		integrationService.UpdateOBSConfig(config)
	}
}

// UpdateAuthService 更新认证服务引用
func (h *ConfigUpdateHandlers) UpdateAuthService(authService AuthService) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.authService = authService
}

// UpdateIntegrationService 更新集成服务引用
func (h *ConfigUpdateHandlers) UpdateIntegrationService(integrationService IntegrationService) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.integrationService = integrationService
}
