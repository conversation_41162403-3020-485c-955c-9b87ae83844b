package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"kubeops/internal/model"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"
)

// applicationService 应用服务实现
type applicationService struct {
	repo           repository.Repository
	clusterManager *k8s.ClusterManager
	syncService    *ApplicationSyncService
	logger         *zap.Logger
	tracer         trace.Tracer
}

// NewApplicationService 创建应用服务
func NewApplicationService(
	repo repository.Repository,
	clusterManager *k8s.ClusterManager,
	syncService *ApplicationSyncService,
	logger *zap.Logger,
	tracer trace.Tracer,
) ApplicationService {
	return &applicationService{
		repo:           repo,
		clusterManager: clusterManager,
		syncService:    syncService,
		logger:         logger,
		tracer:         tracer,
	}
}

// CreateApplication 创建应用
func (s *applicationService) CreateApplication(ctx context.Context, req *model.ApplicationCreateRequest) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.CreateApplication")
	defer span.End()
	span.SetAttributes(
		attribute.String("application.name", req.Name),
		attribute.Int64("project.id", int64(req.ProjectID)),
	)

	// 检查项目是否存在
	project, err := s.repo.Project().GetProjectByID(ctx, req.ProjectID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if project == nil {
		return nil, errors.New("project not found")
	}

	// 检查应用名称是否已存在
	existingApp, err := s.repo.Application().GetApplicationByName(ctx, req.ProjectID, req.Name)
	if err == nil && existingApp != nil {
		return nil, fmt.Errorf("application name %s already exists in project", req.Name)
	}

	// 创建应用对象
	application := &model.Application{
		Name:         req.Name,
		DisplayName:  req.DisplayName,
		Description:  req.Description,
		ProjectID:    req.ProjectID,
		WorkloadType: req.WorkloadType,
		WorkloadName: req.WorkloadName,
		Namespace:    req.Namespace,
		Status:       model.ApplicationStatusActive,
		SyncStatus:   model.ApplicationSyncStatusSynced,
		Labels:       req.Labels,
		Annotations:  req.Annotations,
		Config:       req.Config,
		OwnerID:      &req.OwnerID,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err = s.repo.Application().CreateApplication(ctx, application)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// 添加创建者为应用成员
	if req.OwnerID > 0 {
		err = s.repo.Application().AddApplicationMember(ctx, application.ID, req.OwnerID, model.ApplicationRoleOwner)
		if err != nil {
			s.logger.Error("Failed to add application owner", zap.Error(err))
		}
	}

	return application, nil
}

// GetApplicationByID 根据ID获取应用
func (s *applicationService) GetApplicationByID(ctx context.Context, id uint) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationByID")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(id)))

	return s.repo.Application().GetApplicationByID(ctx, id)
}

// GetApplicationByName 根据名称获取应用
func (s *applicationService) GetApplicationByName(ctx context.Context, projectID uint, name string) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationByName")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.String("application.name", name),
	)

	return s.repo.Application().GetApplicationByName(ctx, projectID, name)
}

// UpdateApplication 更新应用
func (s *applicationService) UpdateApplication(ctx context.Context, id uint, req *model.ApplicationUpdateRequest) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.UpdateApplication")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(id)))

	// 检查应用是否存在
	application, err := s.repo.Application().GetApplicationByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if application == nil {
		return nil, errors.New("application not found")
	}

	// 更新字段
	if req.DisplayName != "" {
		application.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		application.Description = req.Description
	}
	if req.Labels != nil {
		application.Labels = req.Labels
	}
	if req.Annotations != nil {
		application.Annotations = req.Annotations
	}
	if req.Config != nil {
		application.Config = req.Config
	}
	application.Status = req.Status
	application.SyncStatus = req.SyncStatus
	application.UpdatedAt = time.Now()

	err = s.repo.Application().UpdateApplication(ctx, application)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return application, nil
}

// DeleteApplication 删除应用
func (s *applicationService) DeleteApplication(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DeleteApplication")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(id)))

	// 检查应用是否存在
	application, err := s.repo.Application().GetApplicationByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if application == nil {
		return errors.New("application not found")
	}

	return s.repo.Application().DeleteApplication(ctx, id)
}

// ListApplications 获取应用列表
func (s *applicationService) ListApplications(ctx context.Context, page, pageSize int, projectID uint) ([]*model.Application, int64, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.ListApplications")
	defer span.End()
	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("pageSize", pageSize),
		attribute.Int64("project.id", int64(projectID)),
	)

	return s.repo.Application().ListApplicationsPaginated(ctx, page, pageSize, projectID)
}

// StartApplicationSync 启动应用同步
func (s *applicationService) StartApplicationSync(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.StartApplicationSync")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	return s.syncService.StartApplicationSync(ctx, clusterID)
}

// StopApplicationSync 停止应用同步
func (s *applicationService) StopApplicationSync(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.StopApplicationSync")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	return s.syncService.StopApplicationSync(ctx, clusterID)
}

// HandleWorkloadEvent 处理工作负载事件
func (s *applicationService) HandleWorkloadEvent(ctx context.Context, event *model.WorkloadEvent) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.HandleWorkloadEvent")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(event.ClusterID)),
		attribute.String("namespace", event.Namespace),
		attribute.String("workload.type", event.WorkloadType),
		attribute.String("workload.name", event.WorkloadName),
		attribute.String("event.type", event.EventType),
	)

	// 委托给同步服务处理
	s.syncService.processWorkloadEvent(ctx, event)
	return nil
}

// SyncWorkloadToApplication 同步工作负载到应用
func (s *applicationService) SyncWorkloadToApplication(ctx context.Context, clusterID uint, namespace, workloadType, workloadName string, eventType string) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.SyncWorkloadToApplication")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.String("namespace", namespace),
		attribute.String("workload.type", workloadType),
		attribute.String("workload.name", workloadName),
		attribute.String("event.type", eventType),
	)

	// 创建工作负载事件
	event := &model.WorkloadEvent{
		ClusterID:    clusterID,
		Namespace:    namespace,
		WorkloadType: workloadType,
		WorkloadName: workloadName,
		EventType:    eventType,
		Timestamp:    time.Now(),
	}

	s.syncService.processWorkloadEvent(ctx, event)
	return nil
}

// DiscoverApplicationsInCluster 在集群中发现应用
func (s *applicationService) DiscoverApplicationsInCluster(ctx context.Context, clusterID uint, projectID uint) (*model.DiscoverSummary, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DiscoverApplicationsInCluster")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.Int64("project.id", int64(projectID)),
	)

	// 获取集群信息
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, errors.New("cluster not found")
	}

	// 获取K8s客户端
	k8sClient, err := s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster client failed: %w", err)
	}

	summary := &model.DiscoverSummary{}

	// 如果指定了项目ID，只在该项目的命名空间中发现
	if projectID > 0 {
		project, err := s.repo.Project().GetProjectByID(ctx, projectID)
		if err != nil {
			span.RecordError(err)
			return nil, fmt.Errorf("get project failed: %w", err)
		}
		if project == nil {
			return nil, errors.New("project not found")
		}

		// 在项目对应的命名空间中发现工作负载
		count, err := s.discoverWorkloadsInNamespace(ctx, k8sClient, clusterID, project.ID, project.Name)
		if err != nil {
			s.logger.Error("Failed to discover workloads in project namespace",
				zap.Error(err),
				zap.String("namespace", project.Name),
			)
		} else {
			summary.TotalDiscovered += count
		}
	} else {
		// 获取所有活跃项目，在它们的命名空间中发现工作负载
		projects, _, err := s.repo.Project().ListProjectsPaginated(ctx, 1, 1000)
		if err != nil {
			span.RecordError(err)
			return nil, fmt.Errorf("get projects failed: %w", err)
		}

		for _, project := range projects {
			if project.Status == model.ProjectStatusActive {
				count, err := s.discoverWorkloadsInNamespace(ctx, k8sClient, clusterID, project.ID, project.Name)
				if err != nil {
					s.logger.Error("Failed to discover workloads in project namespace",
						zap.Error(err),
						zap.String("namespace", project.Name),
						zap.Uint("project_id", project.ID),
					)
					continue
				}
				summary.TotalDiscovered += count
			}
		}
	}

	s.logger.Info("Application discovery completed",
		zap.Uint("cluster_id", clusterID),
		zap.Uint("project_id", projectID),
		zap.Int("total_discovered", summary.TotalDiscovered),
	)

	return summary, nil
}

// discoverWorkloadsInNamespace 在指定命名空间中发现工作负载
func (s *applicationService) discoverWorkloadsInNamespace(ctx context.Context, k8sClient k8s.K8sClient, clusterID, projectID uint, namespace string) (int, error) {
	totalDiscovered := 0
	workloadTypes := []string{"deployment", "statefulset", "daemonset", "job", "cronjob"}

	for _, workloadType := range workloadTypes {
		count, err := s.discoverWorkloadTypeInNamespace(ctx, k8sClient, clusterID, projectID, namespace, workloadType)
		if err != nil {
			s.logger.Error("Failed to discover workload type in namespace",
				zap.Error(err),
				zap.String("workload_type", workloadType),
				zap.String("namespace", namespace),
			)
			continue
		}
		totalDiscovered += count
	}

	return totalDiscovered, nil
}

// discoverWorkloadTypeInNamespace 在指定命名空间中发现特定类型的工作负载
func (s *applicationService) discoverWorkloadTypeInNamespace(ctx context.Context, k8sClient k8s.K8sClient, clusterID, projectID uint, namespace, workloadType string) (int, error) {
	// TODO: 实现具体的工作负载发现逻辑
	// 1. 根据工作负载类型和命名空间查询K8s资源
	// 2. 解析资源信息
	// 3. 创建或更新应用记录
	// 4. 设置同步状态为待完善

	s.logger.Debug("Discovering workloads",
		zap.String("workload_type", workloadType),
		zap.String("namespace", namespace),
		zap.Uint("cluster_id", clusterID),
		zap.Uint("project_id", projectID),
	)

	return 0, nil
}

// GetIncompleteApplications 获取待完善的应用
func (s *applicationService) GetIncompleteApplications(ctx context.Context, projectID uint) ([]*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetIncompleteApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(projectID)))

	return s.repo.Application().GetIncompleteApplications(ctx, projectID)
}

// CheckDataConsistency 检查数据一致性
func (s *applicationService) CheckDataConsistency(ctx context.Context, clusterID uint) (*model.ConsistencyReport, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.CheckDataConsistency")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	report := &model.ConsistencyReport{
		ClusterID: clusterID,
		CheckedAt: time.Now(),
	}

	// 检查孤儿应用（数据库中存在但K8s中不存在）
	orphanApps, err := s.DetectOrphanApplications(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	report.OrphanApps = orphanApps

	// 检查缺失应用（K8s中存在但数据库中缺失）
	missingApps, err := s.DetectMissingApplications(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	report.MissingApps = missingApps

	// 统计信息
	report.TotalApplications = len(orphanApps) // 需要从数据库查询实际数量
	report.TotalWorkloads = len(missingApps)   // 需要从K8s查询实际数量

	return report, nil
}

// DetectOrphanApplications 检测孤儿应用
func (s *applicationService) DetectOrphanApplications(ctx context.Context, clusterID uint) ([]*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DetectOrphanApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// TODO: 实现孤儿应用检测逻辑
	// 1. 获取数据库中该集群的所有应用
	// 2. 检查K8s中是否存在对应的工作负载
	// 3. 返回不存在的应用列表

	return []*model.Application{}, nil
}

// DetectMissingApplications 检测缺失应用
func (s *applicationService) DetectMissingApplications(ctx context.Context, clusterID uint) ([]*model.WorkloadInfo, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DetectMissingApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// TODO: 实现缺失应用检测逻辑
	// 1. 获取K8s中的所有工作负载
	// 2. 检查数据库中是否存在对应的应用
	// 3. 返回不存在的工作负载列表

	return []*model.WorkloadInfo{}, nil
}

// ReconcileApplications 协调应用数据
func (s *applicationService) ReconcileApplications(ctx context.Context, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.ReconcileApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("cluster.id", int64(clusterID)))

	// 检查数据一致性
	report, err := s.CheckDataConsistency(ctx, clusterID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 处理孤儿应用
	for _, app := range report.OrphanApps {
		// 可以选择删除或标记为已删除
		s.logger.Info("Found orphan application",
			zap.Uint("app_id", app.ID),
			zap.String("app_name", app.Name),
		)
	}

	// 处理缺失应用
	for _, workload := range report.MissingApps {
		// 创建应用记录
		s.logger.Info("Found missing application",
			zap.String("workload", fmt.Sprintf("%s/%s/%s", workload.Namespace, workload.WorkloadType, workload.WorkloadName)),
		)
	}

	s.logger.Info("Application reconciliation completed",
		zap.Uint("cluster_id", clusterID),
		zap.Int("orphan_count", len(report.OrphanApps)),
		zap.Int("missing_count", len(report.MissingApps)),
	)

	return nil
}

// DiscoverApplications 发现应用
func (s *applicationService) DiscoverApplications(ctx context.Context, req *model.ApplicationDiscoverRequest) ([]*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DiscoverApplications")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(req.ProjectID)),
		attribute.Int64("cluster.id", int64(req.ClusterID)),
	)

	// 获取集群信息
	cluster, err := s.repo.Cluster().GetClusterByID(ctx, req.ClusterID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if cluster == nil {
		return nil, errors.New("cluster not found")
	}

	// 获取K8s客户端
	_, err = s.clusterManager.GetCluster(cluster.Name)
	if err != nil {
		span.RecordError(err)
		return nil, fmt.Errorf("get cluster client failed: %w", err)
	}

	// TODO: 实现应用发现逻辑
	// 1. 根据命名空间和标签选择器查询工作负载
	// 2. 解析工作负载信息
	// 3. 创建应用对象
	// 4. 设置同步状态为待完善

	var discoveredApps []*model.Application

	s.logger.Info("Application discovery completed",
		zap.Int64("project_id", int64(req.ProjectID)),
		zap.Int64("cluster_id", int64(req.ClusterID)),
		zap.Int("discovered_count", len(discoveredApps)),
	)

	return discoveredApps, nil
}

// SyncApplicationFromK8s 从K8s同步应用
func (s *applicationService) SyncApplicationFromK8s(ctx context.Context, clusterID uint, namespace, workloadType, workloadName string) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.SyncApplicationFromK8s")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.String("namespace", namespace),
		attribute.String("workload.type", workloadType),
		attribute.String("workload.name", workloadName),
	)

	// TODO: 实现从K8s同步应用的逻辑
	// 1. 获取K8s工作负载详情
	// 2. 解析应用信息
	// 3. 创建或更新应用记录
	// 4. 设置同步状态

	return nil, fmt.Errorf("sync application from k8s not implemented")
}

// CompleteApplicationInfo 完善应用信息
func (s *applicationService) CompleteApplicationInfo(ctx context.Context, id uint, req *model.ApplicationCompleteRequest) (*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.CompleteApplicationInfo")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(id)))

	// 获取应用
	application, err := s.repo.Application().GetApplicationByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if application == nil {
		return nil, errors.New("application not found")
	}

	// 更新应用信息
	if req.DisplayName != "" {
		application.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		application.Description = req.Description
	}
	if req.Labels != nil {
		application.Labels = req.Labels
	}
	if req.Annotations != nil {
		application.Annotations = req.Annotations
	}
	if req.Config != nil {
		application.Config = req.Config
	}
	if req.OwnerID > 0 {
		application.OwnerID = &req.OwnerID
	}

	// 更新同步状态为已完善
	application.SyncStatus = model.ApplicationSyncStatusSynced
	application.UpdatedAt = time.Now()

	err = s.repo.Application().UpdateApplication(ctx, application)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// 添加负责人为应用成员
	if req.OwnerID > 0 {
		err = s.repo.Application().AddApplicationMember(ctx, application.ID, req.OwnerID, model.ApplicationRoleOwner)
		if err != nil {
			s.logger.Error("Failed to add application owner", zap.Error(err))
		}
	}

	return application, nil
}

// RefreshApplicationStatus 刷新应用状态
func (s *applicationService) RefreshApplicationStatus(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.RefreshApplicationStatus")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(id)))

	// TODO: 实现应用状态刷新逻辑
	// 1. 获取应用信息
	// 2. 查询K8s中的工作负载状态
	// 3. 更新应用状态

	s.logger.Info("Application status refreshed", zap.Uint("application_id", id))
	return nil
}

// BatchRefreshApplicationStatus 批量刷新应用状态
func (s *applicationService) BatchRefreshApplicationStatus(ctx context.Context, projectID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.BatchRefreshApplicationStatus")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(projectID)))

	// TODO: 实现批量应用状态刷新逻辑
	// 1. 获取项目下所有应用
	// 2. 批量查询K8s状态
	// 3. 批量更新应用状态

	s.logger.Info("Batch application status refresh completed", zap.Uint("project_id", projectID))
	return nil
}

// ActivateApplication 激活应用
func (s *applicationService) ActivateApplication(ctx context.Context, id uint) error {
	return s.updateApplicationStatus(ctx, id, model.ApplicationStatusActive)
}

// DeactivateApplication 停用应用
func (s *applicationService) DeactivateApplication(ctx context.Context, id uint) error {
	return s.updateApplicationStatus(ctx, id, model.ApplicationStatusInactive)
}

// ArchiveApplication 归档应用
func (s *applicationService) ArchiveApplication(ctx context.Context, id uint) error {
	return s.updateApplicationStatus(ctx, id, model.ApplicationStatusArchived)
}

// UnarchiveApplication 取消归档应用
func (s *applicationService) UnarchiveApplication(ctx context.Context, id uint) error {
	return s.updateApplicationStatus(ctx, id, model.ApplicationStatusActive)
}

// updateApplicationStatus 更新应用状态的内部方法
func (s *applicationService) updateApplicationStatus(ctx context.Context, id uint, status model.ApplicationStatus) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.updateApplicationStatus")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(id)),
		attribute.Int("application.status", int(status)),
	)

	application, err := s.repo.Application().GetApplicationByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if application == nil {
		return errors.New("application not found")
	}

	application.Status = status
	application.UpdatedAt = time.Now()

	return s.repo.Application().UpdateApplication(ctx, application)
}

// DeployToCluster 部署到集群
func (s *applicationService) DeployToCluster(ctx context.Context, applicationID, projectClusterID uint, config *model.ApplicationClusterConfig) (*model.ApplicationCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.DeployToCluster")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("project_cluster.id", int64(projectClusterID)),
	)

	// 检查应用是否存在
	application, err := s.repo.Application().GetApplicationByID(ctx, applicationID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if application == nil {
		return nil, errors.New("application not found")
	}

	// 检查是否已部署到该集群
	existing, err := s.repo.Application().GetApplicationCluster(ctx, applicationID, projectClusterID)
	if err == nil && existing != nil {
		return nil, errors.New("application already deployed to this cluster")
	}

	// 创建应用集群部署记录
	applicationCluster := &model.ApplicationCluster{
		ApplicationID:    applicationID,
		ProjectClusterID: projectClusterID,
		Status:           model.ApplicationClusterStatusDeploying,
	}

	err = s.repo.Application().CreateApplicationCluster(ctx, applicationCluster)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// TODO: 实际部署到K8s集群
	// 1. 获取集群信息
	// 2. 生成K8s资源清单
	// 3. 应用到集群
	// 4. 更新部署状态

	return applicationCluster, nil
}

// UpdateClusterDeployment 更新集群部署
func (s *applicationService) UpdateClusterDeployment(ctx context.Context, applicationClusterID uint, config *model.ApplicationClusterConfig) (*model.ApplicationCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.UpdateClusterDeployment")
	defer span.End()
	span.SetAttributes(attribute.Int64("application_cluster.id", int64(applicationClusterID)))

	// TODO: 实现集群部署更新逻辑
	// 1. 获取应用集群部署记录
	// 2. 更新配置
	// 3. 重新部署到K8s

	return nil, fmt.Errorf("update cluster deployment not implemented")
}

// RemoveFromCluster 从集群移除
func (s *applicationService) RemoveFromCluster(ctx context.Context, applicationID, projectClusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.RemoveFromCluster")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("project_cluster.id", int64(projectClusterID)),
	)

	// TODO: 实现从集群移除应用的逻辑
	// 1. 从K8s集群删除资源
	// 2. 删除部署记录

	return s.repo.Application().DeleteApplicationCluster(ctx, applicationID, projectClusterID)
}

// GetApplicationClusters 获取应用的集群部署信息
func (s *applicationService) GetApplicationClusters(ctx context.Context, applicationID uint) ([]*model.ApplicationCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationClusters")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(applicationID)))

	return s.repo.Application().GetApplicationClusters(ctx, applicationID)
}

// GetClusterApplications 获取集群的应用部署信息
func (s *applicationService) GetClusterApplications(ctx context.Context, projectClusterID uint) ([]*model.ApplicationCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetClusterApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("project_cluster.id", int64(projectClusterID)))

	return s.repo.Application().GetClusterApplications(ctx, projectClusterID)
}

// AddApplicationMember 添加应用成员
func (s *applicationService) AddApplicationMember(ctx context.Context, applicationID, userID uint, role string) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.AddApplicationMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("user.id", int64(userID)),
		attribute.String("role", role),
	)

	// 检查应用是否存在
	application, err := s.repo.Application().GetApplicationByID(ctx, applicationID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if application == nil {
		return errors.New("application not found")
	}

	return s.repo.Application().AddApplicationMember(ctx, applicationID, userID, role)
}

// RemoveApplicationMember 移除应用成员
func (s *applicationService) RemoveApplicationMember(ctx context.Context, applicationID, userID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.RemoveApplicationMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("user.id", int64(userID)),
	)

	return s.repo.Application().RemoveApplicationMember(ctx, applicationID, userID)
}

// UpdateApplicationMemberRole 更新应用成员角色
func (s *applicationService) UpdateApplicationMemberRole(ctx context.Context, applicationID, userID uint, role string) error {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.UpdateApplicationMemberRole")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("user.id", int64(userID)),
		attribute.String("role", role),
	)

	return s.repo.Application().UpdateApplicationMemberRole(ctx, applicationID, userID, role)
}

// GetApplicationMembers 获取应用成员
func (s *applicationService) GetApplicationMembers(ctx context.Context, applicationID uint) ([]*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationMembers")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(applicationID)))

	return s.repo.Application().GetApplicationMembers(ctx, applicationID)
}

// GetUserApplications 获取用户的应用
func (s *applicationService) GetUserApplications(ctx context.Context, userID uint) ([]*model.Application, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetUserApplications")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	return s.repo.Application().GetUserApplications(ctx, userID)
}

// IsApplicationMember 检查用户是否为应用成员
func (s *applicationService) IsApplicationMember(ctx context.Context, applicationID, userID uint) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.IsApplicationMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("application.id", int64(applicationID)),
		attribute.Int64("user.id", int64(userID)),
	)

	return s.repo.Application().IsApplicationMember(ctx, applicationID, userID)
}

// GetApplicationStats 获取应用统计
func (s *applicationService) GetApplicationStats(ctx context.Context, applicationID uint) (map[string]interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationStats")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(applicationID)))

	// 获取应用信息
	application, err := s.repo.Application().GetApplicationByID(ctx, applicationID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if application == nil {
		return nil, errors.New("application not found")
	}

	// 获取集群部署数量
	clusters, err := s.repo.Application().GetApplicationClusters(ctx, applicationID)
	if err != nil {
		clusters = []*model.ApplicationCluster{}
	}

	// 获取成员数量
	members, err := s.repo.Application().GetApplicationMembers(ctx, applicationID)
	if err != nil {
		members = []*model.User{}
	}

	stats := map[string]interface{}{
		"application_id": applicationID,
		"cluster_count":  len(clusters),
		"member_count":   len(members),
		"status":         application.Status,
		"sync_status":    application.SyncStatus,
		"workload_type":  application.WorkloadType,
		"created_at":     application.CreatedAt,
		"updated_at":     application.UpdatedAt,
	}

	return stats, nil
}

// GetProjectApplicationStats 获取项目应用统计
func (s *applicationService) GetProjectApplicationStats(ctx context.Context, projectID uint) (map[string]interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetProjectApplicationStats")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(projectID)))

	// 获取项目应用总数
	totalCount, err := s.repo.Application().GetApplicationCountByProject(ctx, projectID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// 获取各状态应用数量
	statusCounts := make(map[string]int64)
	statuses := []model.ApplicationStatus{
		model.ApplicationStatusActive,
		model.ApplicationStatusInactive,
		model.ApplicationStatusArchived,
	}

	for _, status := range statuses {
		count, err := s.repo.Application().GetApplicationCountByStatus(ctx, projectID, status)
		if err != nil {
			count = 0
		}
		statusCounts[string(status)] = count
	}

	stats := map[string]interface{}{
		"project_id":    projectID,
		"total_count":   totalCount,
		"status_counts": statusCounts,
		"collected_at":  time.Now(),
	}

	return stats, nil
}

// GetApplicationHealth 获取应用健康状态
func (s *applicationService) GetApplicationHealth(ctx context.Context, applicationID uint) (map[string]interface{}, error) {
	ctx, span := s.tracer.Start(ctx, "ApplicationService.GetApplicationHealth")
	defer span.End()
	span.SetAttributes(attribute.Int64("application.id", int64(applicationID)))

	// TODO: 实现应用健康检查逻辑
	// 1. 获取应用在各集群的部署状态
	// 2. 查询K8s中的工作负载健康状态
	// 3. 汇总健康信息

	health := map[string]interface{}{
		"application_id": applicationID,
		"overall_status": "healthy",
		"clusters":       []map[string]interface{}{},
		"checked_at":     time.Now(),
	}

	return health, nil
}
