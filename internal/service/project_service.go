package service

import (
	"context"
	"fmt"

	"kubeops/internal/model"
	"kubeops/internal/repository"
	"kubeops/pkg/k8s"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)



// projectService 项目服务实现
type projectService struct {
	repo           repository.Repository
	clusterManager *k8s.ClusterManager
	casbinService  *CasbinService
	logger         *zap.Logger
	tracer         trace.Tracer
}

// NewProjectService 创建项目服务实例
func NewProjectService(
	repo repository.Repository,
	clusterManager *k8s.ClusterManager,
	casbinService *CasbinService,
	logger *zap.Logger,
	tracer trace.Tracer,
) ProjectService {
	return &projectService{
		repo:           repo,
		clusterManager: clusterManager,
		casbinService:  casbinService,
		logger:         logger,
		tracer:         tracer,
	}
}

// CreateProject 创建项目
func (s *projectService) CreateProject(ctx context.Context, req *model.ProjectCreateRequest) (*model.Project, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.CreateProject")
	defer span.End()
	span.SetAttributes(attribute.String("project.name", req.Name))

	// 检查项目名称是否已存在
	existingProject, err := s.repo.Project().GetProjectByName(ctx, req.Name)
	if err == nil && existingProject != nil {
		return nil, fmt.Errorf("project name already exists: %s", req.Name)
	}

	// 创建项目对象
	project := &model.Project{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Status:      model.ProjectStatusActive,
		// CreatedBy will be set by the caller if needed
	}

	err = s.repo.Project().CreateProject(ctx, project)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	// TODO: 添加创建者为项目成员（需要从上下文获取用户信息）

	return project, nil
}

// GetProjectByID 根据ID获取项目
func (s *projectService) GetProjectByID(ctx context.Context, id uint) (*model.Project, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.GetProjectByID")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(id)))

	return s.repo.Project().GetProjectByID(ctx, id)
}

// GetProjectByName 根据名称获取项目
func (s *projectService) GetProjectByName(ctx context.Context, name string) (*model.Project, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.GetProjectByName")
	defer span.End()
	span.SetAttributes(attribute.String("project.name", name))

	return s.repo.Project().GetProjectByName(ctx, name)
}

// UpdateProject 更新项目
func (s *projectService) UpdateProject(ctx context.Context, id uint, req *model.ProjectUpdateRequest) (*model.Project, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.UpdateProject")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(id)))

	// 检查项目是否存在
	project, err := s.repo.Project().GetProjectByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if project == nil {
		return nil, fmt.Errorf("project not found")
	}

	// 更新字段
	if req.DisplayName != "" {
		project.DisplayName = req.DisplayName
	}
	if req.Description != "" {
		project.Description = req.Description
	}
	if req.Status != nil {
		project.Status = *req.Status
	}

	err = s.repo.Project().UpdateProject(ctx, project)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return project, nil
}

// DeleteProject 删除项目
func (s *projectService) DeleteProject(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.DeleteProject")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(id)))

	// 检查项目是否存在
	project, err := s.repo.Project().GetProjectByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if project == nil {
		return fmt.Errorf("project not found")
	}

	return s.repo.Project().DeleteProject(ctx, id)
}

// ListProjects 获取项目列表
func (s *projectService) ListProjects(ctx context.Context, page, pageSize int) ([]*model.Project, int64, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.ListProjects")
	defer span.End()
	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("pageSize", pageSize),
	)

	return s.repo.Project().ListProjectsPaginated(ctx, page, pageSize)
}

// ActivateProject 激活项目
func (s *projectService) ActivateProject(ctx context.Context, id uint) error {
	return s.updateProjectStatus(ctx, id, model.ProjectStatusActive)
}

// DeactivateProject 停用项目
func (s *projectService) DeactivateProject(ctx context.Context, id uint) error {
	return s.updateProjectStatus(ctx, id, model.ProjectStatusInactive)
}

// ArchiveProject 归档项目
func (s *projectService) ArchiveProject(ctx context.Context, id uint) error {
	return s.updateProjectStatus(ctx, id, model.ProjectStatusArchived)
}

// UnarchiveProject 取消归档项目
func (s *projectService) UnarchiveProject(ctx context.Context, id uint) error {
	return s.updateProjectStatus(ctx, id, model.ProjectStatusActive)
}

// updateProjectStatus 更新项目状态的内部方法
func (s *projectService) updateProjectStatus(ctx context.Context, id uint, status model.ProjectStatus) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.updateProjectStatus")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(id)),
		attribute.Int("project.status", int(status)),
	)

	project, err := s.repo.Project().GetProjectByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if project == nil {
		return fmt.Errorf("project not found")
	}

	project.Status = status
	return s.repo.Project().UpdateProject(ctx, project)
}

// AddProjectToCluster 将项目添加到集群
func (s *projectService) AddProjectToCluster(ctx context.Context, projectID, clusterID uint, namespace string) (*model.ProjectCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.AddProjectToCluster")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("cluster.id", int64(clusterID)),
		attribute.String("namespace", namespace),
	)

	// 检查项目是否存在
	project, err := s.repo.Project().GetProjectByID(ctx, projectID)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if project == nil {
		return nil, fmt.Errorf("project not found")
	}

	// 检查是否已存在
	existing, err := s.repo.Project().GetProjectCluster(ctx, projectID, clusterID)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("project already exists in cluster")
	}

	// 创建项目集群关联
	projectCluster := &model.ProjectCluster{
		ProjectID: projectID,
		ClusterID: clusterID,
		Namespace: namespace,
		Config:    datatypes.JSON("{}"),
	}

	err = s.repo.Project().CreateProjectCluster(ctx, projectCluster)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return projectCluster, nil
}

// RemoveProjectFromCluster 从集群移除项目
func (s *projectService) RemoveProjectFromCluster(ctx context.Context, projectID, clusterID uint) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.RemoveProjectFromCluster")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("cluster.id", int64(clusterID)),
	)

	return s.repo.Project().DeleteProjectCluster(ctx, projectID, clusterID)
}

// GetProjectClusters 获取项目的集群部署信息
func (s *projectService) GetProjectClusters(ctx context.Context, projectID uint) ([]*model.ProjectCluster, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.GetProjectClusters")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(projectID)))

	return s.repo.Project().GetProjectClusters(ctx, projectID)
}

// UpdateProjectClusterConfig 更新项目集群配置
func (s *projectService) UpdateProjectClusterConfig(ctx context.Context, projectClusterID uint, config map[string]interface{}) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.UpdateProjectClusterConfig")
	defer span.End()
	span.SetAttributes(attribute.Int64("project_cluster.id", int64(projectClusterID)))

	// TODO: 实现项目集群配置更新逻辑
	// 这需要根据具体的ProjectCluster模型来实现
	return fmt.Errorf("not implemented")
}

// AddProjectMember 添加项目成员
func (s *projectService) AddProjectMember(ctx context.Context, projectID, userID uint, role string) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.AddProjectMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("user.id", int64(userID)),
		attribute.String("role", role),
	)

	// 检查项目是否存在
	project, err := s.repo.Project().GetProjectByID(ctx, projectID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if project == nil {
		return fmt.Errorf("project not found")
	}

	return s.repo.Project().AddProjectMember(ctx, projectID, userID, role)
}

// RemoveProjectMember 移除项目成员
func (s *projectService) RemoveProjectMember(ctx context.Context, projectID, userID uint) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.RemoveProjectMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("user.id", int64(userID)),
	)

	return s.repo.Project().RemoveProjectMember(ctx, projectID, userID)
}

// UpdateProjectMemberRole 更新项目成员角色
func (s *projectService) UpdateProjectMemberRole(ctx context.Context, projectID, userID uint, role string) error {
	ctx, span := s.tracer.Start(ctx, "ProjectService.UpdateProjectMemberRole")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("user.id", int64(userID)),
		attribute.String("role", role),
	)

	return s.repo.Project().UpdateProjectMemberRole(ctx, projectID, userID, role)
}

// GetProjectMembers 获取项目成员
func (s *projectService) GetProjectMembers(ctx context.Context, projectID uint) ([]*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.GetProjectMembers")
	defer span.End()
	span.SetAttributes(attribute.Int64("project.id", int64(projectID)))

	return s.repo.Project().GetProjectMembers(ctx, projectID)
}

// GetUserProjects 获取用户的项目
func (s *projectService) GetUserProjects(ctx context.Context, userID uint) ([]*model.Project, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.GetUserProjects")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	return s.repo.Project().GetUserProjects(ctx, userID)
}

// IsProjectMember 检查用户是否为项目成员
func (s *projectService) IsProjectMember(ctx context.Context, projectID, userID uint) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "ProjectService.IsProjectMember")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("project.id", int64(projectID)),
		attribute.Int64("user.id", int64(userID)),
	)

	return s.repo.Project().IsProjectMember(ctx, projectID, userID)
}


