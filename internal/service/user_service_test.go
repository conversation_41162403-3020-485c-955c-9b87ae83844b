package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.opentelemetry.io/otel"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// MockUserRepository 用户仓库模拟
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) CreateUser(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByOIDCSubject(ctx context.Context, subject string) (*model.User, error) {
	args := m.Called(ctx, subject)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) GetUserByFeishuID(ctx context.Context, feishuOpenID, feishuUnionID string) (*model.User, error) {
	args := m.Called(ctx, feishuOpenID, feishuUnionID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.User), args.Error(1)
}

func (m *MockUserRepository) UpdateUser(ctx context.Context, user *model.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) DeleteUser(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) ListUsersPaginated(ctx context.Context, page, pageSize int) ([]*model.User, int64, error) {
	args := m.Called(ctx, page, pageSize)
	return args.Get(0).([]*model.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*model.UserGroup), args.Error(1)
}

func (m *MockUserRepository) AddUserToGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	args := m.Called(ctx, userID, groupIDs)
	return args.Error(0)
}

func (m *MockUserRepository) RemoveUserFromGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	args := m.Called(ctx, userID, groupIDs)
	return args.Error(0)
}

func (m *MockUserRepository) SyncUserGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	args := m.Called(ctx, userID, groupIDs)
	return args.Error(0)
}

// MockRepository 仓库模拟
type MockRepository struct {
	userRepo *MockUserRepository
}

func (m *MockRepository) User() repository.UserRepository {
	return m.userRepo
}

func (m *MockRepository) UserGroup() repository.UserGroupRepository {
	return nil // 暂时不需要
}

func (m *MockRepository) Project() repository.ProjectRepository {
	return nil // 暂时不需要
}

func (m *MockRepository) Cluster() repository.ClusterRepository {
	return nil // 暂时不需要
}

func (m *MockRepository) Application() repository.ApplicationRepository {
	return nil // 暂时不需要
}

func (m *MockRepository) Permission() repository.PermissionRepository {
	return nil // 暂时不需要
}

func TestUserService_CreateUser(t *testing.T) {
	// 准备测试数据
	mockUserRepo := &MockUserRepository{}
	mockRepo := &MockRepository{userRepo: mockUserRepo}
	
	userService := &userService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	req := &model.UserCreateRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Name:     "Test User",
		Password: "password123",
	}

	// 设置模拟期望
	mockUserRepo.On("GetUserByUsername", ctx, "testuser").Return(nil, nil)
	mockUserRepo.On("GetUserByEmail", ctx, "<EMAIL>").Return(nil, nil)
	mockUserRepo.On("CreateUser", ctx, mock.AnythingOfType("*model.User")).Return(nil)

	// 执行测试
	user, err := userService.CreateUser(ctx, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "testuser", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "Test User", user.Name)
	assert.Equal(t, model.UserStatusActive, user.Status)
	assert.Equal(t, model.IdentityProviderLocal, user.IdentityProvider)
	assert.NotEmpty(t, user.Password) // 密码应该被哈希

	// 验证模拟调用
	mockUserRepo.AssertExpectations(t)
}

func TestUserService_GetUserByID(t *testing.T) {
	// 准备测试数据
	mockUserRepo := &MockUserRepository{}
	mockRepo := &MockRepository{userRepo: mockUserRepo}
	
	userService := &userService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	userID := uint(1)
	expectedUser := &model.User{
		ID:       userID,
		Username: "testuser",
		Email:    "<EMAIL>",
		Name:     "Test User",
		Status:   model.UserStatusActive,
	}

	// 设置模拟期望
	mockUserRepo.On("GetUserByID", ctx, userID).Return(expectedUser, nil)

	// 执行测试
	user, err := userService.GetUserByID(ctx, userID)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, expectedUser.ID, user.ID)
	assert.Equal(t, expectedUser.Username, user.Username)
	assert.Equal(t, expectedUser.Email, user.Email)

	// 验证模拟调用
	mockUserRepo.AssertExpectations(t)
}

func TestUserService_UpdateUser(t *testing.T) {
	// 准备测试数据
	mockUserRepo := &MockUserRepository{}
	mockRepo := &MockRepository{userRepo: mockUserRepo}
	
	userService := &userService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	userID := uint(1)
	existingUser := &model.User{
		ID:       userID,
		Username: "testuser",
		Email:    "<EMAIL>",
		Name:     "Old Name",
		Status:   model.UserStatusActive,
	}

	req := &model.UserUpdateRequest{
		Email:  "<EMAIL>",
		Name:   "New Name",
		Status: model.UserStatusActive,
	}

	// 设置模拟期望
	mockUserRepo.On("GetUserByID", ctx, userID).Return(existingUser, nil)
	mockUserRepo.On("UpdateUser", ctx, mock.AnythingOfType("*model.User")).Return(nil)

	// 执行测试
	user, err := userService.UpdateUser(ctx, userID, req)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "New Name", user.Name)

	// 验证模拟调用
	mockUserRepo.AssertExpectations(t)
}

func TestUserService_CreateOrUpdateOIDCUser(t *testing.T) {
	// 准备测试数据
	mockUserRepo := &MockUserRepository{}
	mockRepo := &MockRepository{userRepo: mockUserRepo}
	
	userService := &userService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	userInfo := &model.OIDCUserInfo{
		Subject:           "oidc-subject-123",
		Email:             "<EMAIL>",
		Name:              "OIDC User",
		PreferredUsername: "oidcuser",
		Picture:           "https://example.com/avatar.jpg",
		FeishuOpenID:      "feishu-open-123",
		FeishuUnionID:     "feishu-union-123",
		FeishuUserID:      "feishu-user-123",
	}

	// 设置模拟期望 - 用户不存在，需要创建
	mockUserRepo.On("GetUserByOIDCSubject", ctx, "oidc-subject-123").Return(nil, nil)
	mockUserRepo.On("CreateUser", ctx, mock.AnythingOfType("*model.User")).Return(nil)

	// 执行测试
	user, err := userService.CreateOrUpdateOIDCUser(ctx, userInfo)

	// 验证结果
	assert.NoError(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, "oidcuser", user.Username)
	assert.Equal(t, "<EMAIL>", user.Email)
	assert.Equal(t, "OIDC User", user.Name)
	assert.Equal(t, model.IdentityProviderOIDC, user.IdentityProvider)
	assert.Equal(t, "oidc-subject-123", user.OIDCSubject)
	assert.Equal(t, "feishu-open-123", user.FeishuOpenID)

	// 验证模拟调用
	mockUserRepo.AssertExpectations(t)
}
