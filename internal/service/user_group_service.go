package service

import (
	"context"
	"errors"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// userGroupService 用户组服务实现
type userGroupService struct {
	repo   repository.Repository
	tracer trace.Tracer
}

// NewUserGroupService 创建用户组服务
func NewUserGroupService(
	repo repository.Repository,
	tracer trace.Tracer,
) UserGroupService {
	return &userGroupService{
		repo:   repo,
		tracer: tracer,
	}
}

// 注意：intSliceFromUintSlice函数已在rbac_service.go中定义

// CreateUserGroup 创建用户组
func (s *userGroupService) CreateUserGroup(ctx context.Context, req *model.UserGroupCreateRequest) (*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.CreateUserGroup")
	defer span.End()
	span.SetAttributes(attribute.String("group.name", req.Name))

	// 检查用户组名称是否已存在
	existingGroup, err := s.repo.UserGroup().GetUserGroupByName(ctx, req.Name)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.RecordError(err)
		return nil, err
	}
	if existingGroup != nil {
		return nil, errors.New("user group name already exists")
	}

	// 创建用户组对象 - 按照technical-design.md 4.3.2节设计
	group := &model.UserGroup{
		Name:        req.Name,
		Domain:      req.Domain,
		Description: req.Description,
		Type:        req.Type,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.repo.UserGroup().CreateUserGroup(ctx, group)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return group, nil
}

// GetUserGroupByID 根据ID获取用户组
func (s *userGroupService) GetUserGroupByID(ctx context.Context, id uint) (*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.GetUserGroupByID")
	defer span.End()
	span.SetAttributes(attribute.Int64("group.id", int64(id)))

	return s.repo.UserGroup().GetUserGroupByID(ctx, id)
}

// GetUserGroupByName 根据名称获取用户组
func (s *userGroupService) GetUserGroupByName(ctx context.Context, name string) (*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.GetUserGroupByName")
	defer span.End()
	span.SetAttributes(attribute.String("group.name", name))

	return s.repo.UserGroup().GetUserGroupByName(ctx, name)
}

// UpdateUserGroup 更新用户组
func (s *userGroupService) UpdateUserGroup(ctx context.Context, id uint, req *model.UserGroupUpdateRequest) (*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.UpdateUserGroup")
	defer span.End()
	span.SetAttributes(attribute.Int64("group.id", int64(id)))

	// 检查用户组是否存在
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}
	if group == nil {
		return nil, errors.New("user group not found")
	}

	// 更新字段 - 按照technical-design.md 4.3.2节设计
	if req.Domain != "" {
		group.Domain = req.Domain
	}
	if req.Description != "" {
		group.Description = req.Description
	}
	if req.Type != "" {
		group.Type = req.Type
	}
	group.UpdatedAt = time.Now()

	err = s.repo.UserGroup().UpdateUserGroup(ctx, group)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return group, nil
}

// DeleteUserGroup 删除用户组
func (s *userGroupService) DeleteUserGroup(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.DeleteUserGroup")
	defer span.End()
	span.SetAttributes(attribute.Int64("group.id", int64(id)))

	// 检查用户组是否存在
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if group == nil {
		return errors.New("user group not found")
	}

	return s.repo.UserGroup().DeleteUserGroup(ctx, id)
}

// ListUserGroups 获取用户组列表
func (s *userGroupService) ListUserGroups(ctx context.Context, page, pageSize int) ([]*model.UserGroup, int64, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.ListUserGroups")
	defer span.End()
	span.SetAttributes(
		attribute.Int("page", page),
		attribute.Int("pageSize", pageSize),
	)

	return s.repo.UserGroup().ListUserGroupsPaginated(ctx, page, pageSize)
}

// AddUsersToGroup 添加用户到用户组
func (s *userGroupService) AddUsersToGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.AddUsersToGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.Int("user.count", len(userIDs)),
	)

	// 检查用户组是否存在
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, groupID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if group == nil {
		return errors.New("user group not found")
	}

	return s.repo.UserGroup().AddUsersToGroup(ctx, groupID, userIDs)
}

// RemoveUsersFromGroup 从用户组移除用户
func (s *userGroupService) RemoveUsersFromGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.RemoveUsersFromGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("user.ids", intSliceFromUintSlice(userIDs)),
	)

	return s.repo.UserGroup().RemoveUsersFromGroup(ctx, groupID, userIDs)
}

// SetGroupMembers 设置用户组成员
func (s *userGroupService) SetGroupMembers(ctx context.Context, groupID uint, userIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.SetGroupMembers")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("user.ids", intSliceFromUintSlice(userIDs)),
	)

	return s.repo.UserGroup().SetGroupMembers(ctx, groupID, userIDs)
}

// GetGroupMembers 获取用户组成员
func (s *userGroupService) GetGroupMembers(ctx context.Context, groupID uint) ([]*model.User, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.GetGroupMembers")
	defer span.End()
	span.SetAttributes(attribute.Int64("group.id", int64(groupID)))

	return s.repo.UserGroup().GetGroupMembers(ctx, groupID)
}

// GetUserGroups 获取用户所属的用户组
func (s *userGroupService) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.GetUserGroups")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	return s.repo.UserGroup().GetUserGroups(ctx, userID)
}

// IsUserInGroup 检查用户是否属于用户组
func (s *userGroupService) IsUserInGroup(ctx context.Context, userID, groupID uint) (bool, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.IsUserInGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("group.id", int64(groupID)),
	)

	return s.repo.UserGroup().IsUserInGroup(ctx, userID, groupID)
}

// SyncKeycloakGroups 同步Keycloak用户组
func (s *userGroupService) SyncKeycloakGroups(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.SyncKeycloakGroups")
	defer span.End()

	// TODO: 实现Keycloak用户组同步逻辑
	// 1. 从Keycloak获取所有用户组
	// 2. 与本地用户组进行比较
	// 3. 创建或更新本地用户组
	return nil
}

// CreateOrUpdateKeycloakGroup 创建或更新Keycloak用户组 - 按照technical-design.md简化设计
func (s *userGroupService) CreateOrUpdateKeycloakGroup(ctx context.Context, externalID, name, displayName string) (*model.UserGroup, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.CreateOrUpdateKeycloakGroup")
	defer span.End()
	span.SetAttributes(
		attribute.String("group.name", name),
	)

	// 尝试通过名称查找用户组
	group, err := s.repo.UserGroup().GetUserGroupByName(ctx, name)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.RecordError(err)
		return nil, err
	}

	if group != nil {
		// 更新现有用户组
		if displayName != "" {
			group.Description = displayName
		}
		group.UpdatedAt = time.Now()

		err = s.repo.UserGroup().UpdateUserGroup(ctx, group)
		if err != nil {
			span.RecordError(err)
			return nil, err
		}
		return group, nil
	}

	// 创建新用户组
	group = &model.UserGroup{
		Name:        name,
		Description: displayName, // 使用displayName作为描述
		Type:        "keycloak",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.repo.UserGroup().CreateUserGroup(ctx, group)
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return group, nil
}

// GetUserGroupByExternalID 通过外部ID获取用户组 - 已废弃，使用KeycloakGroupMapping
func (s *userGroupService) GetUserGroupByExternalID(ctx context.Context, externalID string) (*model.UserGroup, error) {
    // 此方法已废弃，请使用KeycloakGroupMapping进行组映射
    return nil, errors.New("method deprecated, use KeycloakGroupMapping instead")
}

// SyncUserGroupMembership 同步用户组成员关系 - 简化版本，使用KeycloakGroupMapping
func (s *userGroupService) SyncUserGroupMembership(ctx context.Context, userID uint, keycloakGroups []string) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.SyncUserGroupMembership")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.StringSlice("keycloak.groups", keycloakGroups),
	)

	// TODO: 实现基于KeycloakGroupMapping的用户组同步逻辑
	// 1. 查询KeycloakGroupMapping表，找到keycloakGroups对应的本地用户组
	// 2. 清除用户的自动加入组关系（IsAutoJoin=true）
	// 3. 为用户添加新的组关系，标记为自动加入

	return nil // 暂时返回nil，避免编译错误
}

// AssignPermissionsToGroup 为用户组分配权限
func (s *userGroupService) AssignPermissionsToGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.AssignPermissionsToGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	// 检查用户组是否存在
	group, err := s.repo.UserGroup().GetUserGroupByID(ctx, groupID)
	if err != nil {
		span.RecordError(err)
		return err
	}
	if group == nil {
		return errors.New("user group not found")
	}

	return s.repo.UserGroup().AssignPermissionsToGroup(ctx, groupID, permissionIDs)
}

// RemovePermissionsFromGroup 从用户组移除权限
func (s *userGroupService) RemovePermissionsFromGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.RemovePermissionsFromGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("group.id", int64(groupID)),
		attribute.IntSlice("permission.ids", intSliceFromUintSlice(permissionIDs)),
	)

	return s.repo.UserGroup().RemovePermissionsFromGroup(ctx, groupID, permissionIDs)
}

// GetGroupPermissions 获取用户组权限
func (s *userGroupService) GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.GetGroupPermissions")
	defer span.End()
	span.SetAttributes(attribute.Int64("group.id", int64(groupID)))

	return s.repo.UserGroup().GetGroupPermissions(ctx, groupID)
}

// AddUserToGroup 添加用户到用户组
func (s *userGroupService) AddUserToGroup(ctx context.Context, userID, groupID uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.AddUserToGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("group.id", int64(groupID)),
	)

	return s.repo.UserGroup().AddUserToGroup(ctx, userID, groupID)
}

// RemoveUserFromGroup 从用户组移除用户
func (s *userGroupService) RemoveUserFromGroup(ctx context.Context, userID, groupID uint) error {
	ctx, span := s.tracer.Start(ctx, "UserGroupService.RemoveUserFromGroup")
	defer span.End()
	span.SetAttributes(
		attribute.Int64("user.id", int64(userID)),
		attribute.Int64("group.id", int64(groupID)),
	)

	return s.repo.UserGroup().RemoveUserFromGroup(ctx, userID, groupID)
}




