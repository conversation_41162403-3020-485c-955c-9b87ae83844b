package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// ApprovalService 审批服务接口
type ApprovalService interface {
	// CreateApprovalFlow 创建审批流程
	CreateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error

	// GetApprovalFlow 获取审批流程
	GetApprovalFlow(ctx context.Context, id uint) (*model.ApprovalFlow, error)

	// UpdateApprovalFlow 更新审批流程
	UpdateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error

	// ListApprovalFlows 获取所有审批流程
	ListApprovalFlows(ctx context.Context) ([]*model.ApprovalFlow, error)

	// DeleteApprovalFlow 删除审批流程
	DeleteApprovalFlow(ctx context.Context, id uint) error

	// CreateApprovalRequest 创建审批请求
	CreateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error

	// GetApprovalRequest 获取审批请求
	GetApprovalRequest(ctx context.Context, id uint) (*model.ApprovalRequest, error)

	// ApproveRequest 同意审批请求
	ApproveRequest(ctx context.Context, requestID uint, userID uint, comment string) error

	// RejectRequest 拒绝审批请求
	RejectRequest(ctx context.Context, requestID uint, userID uint, comment string) error

	// CancelRequest 取消审批请求
	CancelRequest(ctx context.Context, requestID uint, userID uint) error

	// ListApprovalRequests 获取审批请求列表
	ListApprovalRequests(ctx context.Context, userID uint, status string, page, size int) ([]*model.ApprovalRequest, int64, error)

	// ProcessFeishuApproval 处理飞书审批回调
	ProcessFeishuApproval(ctx context.Context, approvalID string, action string, userID string) error

	// SendApprovalNotification 发送审批通知
	SendApprovalNotification(ctx context.Context, requestID uint) error
}

// approvalService 审批服务实现
type approvalService struct {
	repo   repository.Repository
	tracer trace.Tracer
}

// NewApprovalService 创建新的审批服务
func NewApprovalService(repo repository.Repository, tracer trace.Tracer) ApprovalService {
	return &approvalService{
		repo:   repo,
		tracer: tracer,
	}
}

// CreateApprovalFlow 创建审批流程
func (s *approvalService) CreateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.CreateApprovalFlow")
	defer span.End()
	span.SetAttributes(attribute.String("flow.name", flow.Name))

	// TODO: 实现审批流程创建
	return fmt.Errorf("approval flow creation not implemented")
}

// GetApprovalFlow 获取审批流程
func (s *approvalService) GetApprovalFlow(ctx context.Context, id uint) (*model.ApprovalFlow, error) {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.GetApprovalFlow")
	defer span.End()
	span.SetAttributes(attribute.Int64("flow.id", int64(id)))

	// TODO: 实现审批流程获取
	flow, err := (*model.ApprovalFlow)(nil), fmt.Errorf("approval flow retrieval not implemented")
	if err != nil {
		span.RecordError(err)
		return nil, err
	}

	return flow, err
}

// UpdateApprovalFlow 更新审批流程
func (s *approvalService) UpdateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.UpdateApprovalFlow")
	defer span.End()
	span.SetAttributes(attribute.Int64("flow.id", int64(flow.ID)))

	// 检查流程是否存在
	_, err := s.repo.Approval().GetApprovalFlow(ctx, flow.ID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	return s.repo.Approval().UpdateApprovalFlow(ctx, flow)
}

// ListApprovalFlows 获取所有审批流程
func (s *approvalService) ListApprovalFlows(ctx context.Context) ([]*model.ApprovalFlow, error) {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.ListApprovalFlows")
	defer span.End()

	// TODO: 实现审批流程列表
	flows, err := ([]*model.ApprovalFlow)(nil), fmt.Errorf("approval flow listing not implemented")
	if err != nil {
		return nil, err
	}

	return flows, err
}

// DeleteApprovalFlow 删除审批流程
func (s *approvalService) DeleteApprovalFlow(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.DeleteApprovalFlow")
	defer span.End()
	span.SetAttributes(attribute.Int64("flow.id", int64(id)))

	// 检查流程是否存在
	_, err := s.repo.Approval().GetApprovalFlow(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}

	return s.repo.Approval().DeleteApprovalFlow(ctx, id)
}

// CreateApprovalRequest 创建审批请求
func (s *approvalService) CreateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.CreateApprovalRequest")
	defer span.End()
	span.SetAttributes(attribute.Int64("flow.id", int64(request.FlowID)))
	span.SetAttributes(attribute.String("request.title", request.Title))

	// 获取审批流程
	flow, err := s.repo.Approval().GetApprovalFlow(ctx, request.FlowID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 设置初始状态
	request.Status = "pending"
	request.CreatedAt = time.Now()
	request.UpdatedAt = time.Now()

	// 创建请求
	if err := s.repo.Approval().CreateApprovalRequest(ctx, request); err != nil {
		span.RecordError(err)
		return err
	}

	// 创建审批步骤
	if err := s.createApprovalSteps(ctx, request, flow); err != nil {
		span.RecordError(err)
		return err
	}

	// 发送审批通知
	if err := s.SendApprovalNotification(ctx, request.ID); err != nil {
		span.RecordError(err)
		// 不阻断流程，只记录日志
	}

	return nil
}

// createApprovalSteps 创建审批步骤
func (s *approvalService) createApprovalSteps(ctx context.Context, request *model.ApprovalRequest, flow *model.ApprovalFlow) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.createApprovalSteps")
	defer span.End()

	// 获取流程步骤
	steps := flow.ApprovalSteps
	if len(steps) == 0 {
		return errors.New("审批流程没有配置步骤")
	}

	// 排序步骤
	// TODO: 需要排序步骤，按照Order字段

	// 创建请求步骤
	for _, step := range steps {
		requestStep := &model.ApprovalRequestStep{
			RequestID:     request.ID,
			StepID:        step.ID,
			StepName:      step.Name,
			StepOrder:     step.Order,
			Status:        "pending",
			ApproverIDs:   step.ApproverIDs,
			ApproverNames: "", // 需要获取审批人名称
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// 设置超时时间
		if step.Timeout > 0 {
			timeoutAt := time.Now().Add(time.Duration(step.Timeout) * time.Hour)
			requestStep.TimeoutAt = &timeoutAt
		}

		// 保存步骤
		if err := s.repo.Approval().UpdateApprovalRequestStep(ctx, requestStep); err != nil {
			return err
		}
	}

	// 更新请求当前步骤
	request.CurrentStep = 0
	return s.repo.Approval().UpdateApprovalRequest(ctx, request)
}

// GetApprovalRequest 获取审批请求
func (s *approvalService) GetApprovalRequest(ctx context.Context, id uint) (*model.ApprovalRequest, error) {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.GetApprovalRequest")
	defer span.End()
	span.SetAttributes(attribute.Int64("request.id", int64(id)))

	return s.repo.Approval().GetApprovalRequest(ctx, id)
}

// ApproveRequest 同意审批请求
func (s *approvalService) ApproveRequest(ctx context.Context, requestID uint, userID uint, comment string) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.ApproveRequest")
	defer span.End()
	span.SetAttributes(attribute.Int64("request.id", int64(requestID)))
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// 获取审批请求
	request, err := s.repo.Approval().GetApprovalRequest(ctx, requestID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 检查请求状态
	if request.Status != "pending" {
		return errors.New("审批请求已经完成或已取消")
	}

	// 获取当前步骤
	if request.CurrentStep >= len(request.Steps) {
		return errors.New("审批步骤索引超出范围")
	}

	// 将结构体转为指针
	currentStepPtr := &request.Steps[request.CurrentStep]

	// 检查是否有权限审批
	if !s.canApprove(ctx, currentStepPtr, userID) {
		return errors.New("您没有权限审批此请求")
	}

	// 更新步骤状态
	currentStepPtr.Status = "approved"
	currentStepPtr.Approver = userID
	// TODO: 获取用户名称
	currentStepPtr.ApproverName = fmt.Sprintf("User%d", userID)
	currentStepPtr.Comment = comment
	currentStepPtr.ApprovedAt = timePtr(time.Now())

	if err := s.repo.Approval().UpdateApprovalRequestStep(ctx, currentStepPtr); err != nil {
		span.RecordError(err)
		return err
	}

	// 检查是否是最后一个步骤
	if request.CurrentStep == len(request.Steps)-1 {
		// 最后一个步骤，审批完成
		request.Status = "approved"
		request.Result = "审批通过"
		request.CompletedAt = timePtr(time.Now())
	} else {
		// 移到下一个步骤
		request.CurrentStep++
	}

	// 更新请求状态
	if err := s.repo.Approval().UpdateApprovalRequest(ctx, request); err != nil {
		span.RecordError(err)
		return err
	}

	// 如果审批完成，执行后续操作
	if request.Status == "approved" {
		if err := s.handleApprovedRequest(ctx, request); err != nil {
			span.RecordError(err)
			// 不阻断流程，只记录日志
		}
	} else {
		// 发送通知给下一步审批人
		if err := s.SendApprovalNotification(ctx, requestID); err != nil {
			span.RecordError(err)
			// 不阻断流程，只记录日志
		}
	}

	return nil
}

// RejectRequest 拒绝审批请求
func (s *approvalService) RejectRequest(ctx context.Context, requestID uint, userID uint, comment string) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.RejectRequest")
	defer span.End()
	span.SetAttributes(attribute.Int64("request.id", int64(requestID)))
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// 获取审批请求
	request, err := s.repo.Approval().GetApprovalRequest(ctx, requestID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 检查请求状态
	if request.Status != "pending" {
		return errors.New("审批请求已经完成或已取消")
	}

	// 获取当前步骤
	if request.CurrentStep >= len(request.Steps) {
		return errors.New("审批步骤索引超出范围")
	}

	// 将结构体转为指针
	currentStepPtr := &request.Steps[request.CurrentStep]

	// 检查是否有权限审批
	if !s.canApprove(ctx, currentStepPtr, userID) {
		return errors.New("您没有权限审批此请求")
	}

	// 更新步骤状态
	currentStepPtr.Status = "rejected"
	currentStepPtr.Approver = userID
	// TODO: 获取用户名称
	currentStepPtr.ApproverName = fmt.Sprintf("User%d", userID)
	currentStepPtr.Comment = comment
	currentStepPtr.ApprovedAt = timePtr(time.Now())

	if err := s.repo.Approval().UpdateApprovalRequestStep(ctx, currentStepPtr); err != nil {
		span.RecordError(err)
		return err
	}

	// 更新请求状态
	request.Status = "rejected"
	request.Result = comment
	request.CompletedAt = timePtr(time.Now())

	// 更新请求状态
	if err := s.repo.Approval().UpdateApprovalRequest(ctx, request); err != nil {
		span.RecordError(err)
		return err
	}

	// 通知申请人
	if err := s.notifyApplicant(ctx, request, "rejected", comment); err != nil {
		span.RecordError(err)
		// 不阻断流程，只记录日志
	}

	return nil
}

// CancelRequest 取消审批请求
func (s *approvalService) CancelRequest(ctx context.Context, requestID uint, userID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.CancelRequest")
	defer span.End()
	span.SetAttributes(attribute.Int64("request.id", int64(requestID)))
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))

	// 获取审批请求
	request, err := s.repo.Approval().GetApprovalRequest(ctx, requestID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 检查请求状态
	if request.Status != "pending" {
		return errors.New("审批请求已经完成或已取消")
	}

	// 检查是否是申请人或管理员
	if request.ApplicantID != userID {
		// TODO: 检查是否是管理员
	}

	// 更新请求状态
	request.Status = "canceled"
	request.CompletedAt = timePtr(time.Now())

	// 更新请求状态
	if err := s.repo.Approval().UpdateApprovalRequest(ctx, request); err != nil {
		span.RecordError(err)
		return err
	}

	return nil
}

// ListApprovalRequests 获取审批请求列表
func (s *approvalService) ListApprovalRequests(ctx context.Context, userID uint, status string, page, size int) ([]*model.ApprovalRequest, int64, error) {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.ListApprovalRequests")
	defer span.End()
	span.SetAttributes(attribute.Int64("user.id", int64(userID)))
	span.SetAttributes(attribute.String("status", status))
	span.SetAttributes(attribute.Int("page", page))
	span.SetAttributes(attribute.Int("size", size))

	// 获取所有审批请求，然后在服务层进行过滤和分页
	allRequests, _, err := s.repo.Approval().ListApprovalRequests(ctx, 1, 1000) // 获取大量数据用于过滤
	if err != nil {
		return nil, 0, err
	}

	// 过滤条件
	var filteredRequests []*model.ApprovalRequest
	for _, request := range allRequests {
		// 过滤用户ID（如果指定）
		if userID != 0 && request.ApplicantID != userID {
			continue
		}
		// 过滤状态（如果指定）
		if status != "" && request.Status != status {
			continue
		}
		filteredRequests = append(filteredRequests, request)
	}

	// 计算分页
	filteredTotal := int64(len(filteredRequests))
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	start := (page - 1) * size
	end := start + size
	if start >= len(filteredRequests) {
		return []*model.ApprovalRequest{}, filteredTotal, nil
	}
	if end > len(filteredRequests) {
		end = len(filteredRequests)
	}

	return filteredRequests[start:end], filteredTotal, nil
}

// ProcessFeishuApproval 处理飞书审批回调
func (s *approvalService) ProcessFeishuApproval(ctx context.Context, approvalID string, action string, userID string) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.ProcessFeishuApproval")
	defer span.End()
	span.SetAttributes(attribute.String("approval.id", approvalID))
	span.SetAttributes(attribute.String("action", action))
	span.SetAttributes(attribute.String("user.id", userID))

	// 将approvalID转换为请求ID
	requestID, err := strconv.ParseUint(approvalID, 10, 64)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 获取飞书用户
	// TODO: 通过飞书用户ID获取系统用户ID
	systemUserID := uint(1) // 默认为1

	// 根据action处理
	switch action {
	case "approve":
		return s.ApproveRequest(ctx, uint(requestID), systemUserID, "通过飞书审批通过")
	case "reject":
		return s.RejectRequest(ctx, uint(requestID), systemUserID, "通过飞书审批拒绝")
	default:
		return errors.New("不支持的操作类型")
	}
}

// SendApprovalNotification 发送审批通知
func (s *approvalService) SendApprovalNotification(ctx context.Context, requestID uint) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.SendApprovalNotification")
	defer span.End()
	span.SetAttributes(attribute.Int64("request.id", int64(requestID)))

	// 获取审批请求
	request, err := s.repo.Approval().GetApprovalRequest(ctx, requestID)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 检查请求状态
	if request.Status != "pending" {
		return nil // 已经完成的请求不需要发送通知
	}

	// 获取当前步骤
	if request.CurrentStep >= len(request.Steps) {
		return errors.New("审批步骤索引超出范围")
	}

	currentStep := request.Steps[request.CurrentStep]

	// 获取飞书配置并发送通知
	feishuConfigData, err := s.repo.SystemConfig().GetFeishuConfig(ctx)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 记录飞书配置信息
	span.SetAttributes(attribute.String("feishu.app_id", feishuConfigData.AppID))

	// 解析审批人ID
	var approverIDs []string
	if err := json.Unmarshal([]byte(currentStep.ApproverIDs), &approverIDs); err != nil {
		// 尝试以逗号分隔的格式解析
		approverIDs = splitAndTrim(currentStep.ApproverIDs)
	}

	// 记录审批人信息
	span.SetAttributes(attribute.StringSlice("approvers", approverIDs))

	// 发送审批消息
	title := fmt.Sprintf("审批请求: %s", request.Title)
	content := request.Content

	// 简单实现，实际应使用飞书API发送消息
	// 这里仅记录消息内容，不实际发送
	span.SetAttributes(attribute.String("message.title", title))
	span.SetAttributes(attribute.String("message.content", content))
	span.SetAttributes(attribute.String("message.id", fmt.Sprintf("%d", request.ID)))

	// 由于飞书服务没有实现SendApproval方法，我们在这里不实际调用
	// 返回nil表示成功，实际项目中应该实现真正的发送逻辑
	return nil
}

// handleApprovedRequest 处理已批准的请求
func (s *approvalService) handleApprovedRequest(ctx context.Context, request *model.ApprovalRequest) error {
	ctx, span := s.tracer.Start(ctx, "ApprovalService.handleApprovedRequest")
	defer span.End()

	// 根据请求类型执行相应操作
	switch request.ResourceType {
	case "cluster":
		// 处理集群资源
		return s.handleClusterResource(ctx, request)
	case "namespace":
		// 处理命名空间
		return s.handleNamespaceResource(ctx, request)
	case "deployment":
		// 处理部署
		return s.handleDeploymentResource(ctx, request)
	default:
		// 其他资源类型
		return nil
	}
}

// handleClusterResource 处理集群资源
func (s *approvalService) handleClusterResource(ctx context.Context, request *model.ApprovalRequest) error {
	// TODO: 实现具体处理逻辑
	return nil
}

// handleNamespaceResource 处理命名空间资源
func (s *approvalService) handleNamespaceResource(ctx context.Context, request *model.ApprovalRequest) error {
	// TODO: 实现具体处理逻辑
	return nil
}

// handleDeploymentResource 处理部署资源
func (s *approvalService) handleDeploymentResource(ctx context.Context, request *model.ApprovalRequest) error {
	// TODO: 实现具体处理逻辑
	return nil
}

// notifyApplicant 通知申请人
func (s *approvalService) notifyApplicant(ctx context.Context, request *model.ApprovalRequest, status string, comment string) error {
	// TODO: 实现通知逻辑
	return nil
}

// canApprove 检查用户是否有权限审批
func (s *approvalService) canApprove(ctx context.Context, step *model.ApprovalRequestStep, userID uint) bool {
	// TODO: 实现检查逻辑
	return true
}

// timePtr 创建时间指针
func timePtr(t time.Time) *time.Time {
	return &t
}

// splitAndTrim 分割并去除空白字符
func splitAndTrim(s string) []string {
	if s == "" {
		return []string{}
	}

	parts := strings.Split(s, ",")
	result := make([]string, 0, len(parts))

	for _, part := range parts {
		trimmed := strings.TrimSpace(part)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	return result
}
