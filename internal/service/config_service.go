package service

import (
	"context"
	"encoding/json"
	"fmt"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// KeycloakGroupMappingConfig 是Keycloak组映射配置
type KeycloakGroupMappingConfig struct {
	KeycloakGroup string `json:"keycloak_group"`
	PlatformGroup string `json:"platform_group"`
}

// ConfigService 配置服务接口
type ConfigService interface {
	// Keycloak组映射相关方法
	GetKeycloakGroupMappings(ctx context.Context) ([]KeycloakGroupMappingConfig, error)
	CreateKeycloakGroupMapping(ctx context.Context, keycloakGroup, platformGroup string) error
	DeleteKeycloakGroupMapping(ctx context.Context, keycloakGroup string) error
}

// ConfigServiceImpl 配置服务实现
type ConfigServiceImpl struct {
	log        *zap.Logger
	tracer     trace.Tracer
	configRepo repository.ConfigRepository
}

// NewConfigService 创建配置服务
func NewConfigService(
	log *zap.Logger,
	tracer trace.Tracer,
	configRepo repository.ConfigRepository,
) ConfigService {
	return &ConfigServiceImpl{
		log:        log,
		tracer:     tracer,
		configRepo: configRepo,
	}
}

// GetKeycloakGroupMappings 获取所有Keycloak组映射
func (s *ConfigServiceImpl) GetKeycloakGroupMappings(ctx context.Context) ([]KeycloakGroupMappingConfig, error) {
	ctx, span := s.tracer.Start(ctx, "ConfigService.GetKeycloakGroupMappings")
	defer span.End()

	configs, err := s.configRepo.GetByType(ctx, "keycloak_group_mapping")
	if err != nil {
		return nil, fmt.Errorf("获取Keycloak组映射失败: %w", err)
	}

	var mappings []KeycloakGroupMappingConfig
	for _, config := range configs {
		var mapping KeycloakGroupMappingConfig
		if err := json.Unmarshal([]byte(config.Value), &mapping); err != nil {
			s.log.Warn("解析组映射配置失败",
				zap.String("config_key", config.Key),
				zap.String("config_value", config.Value),
				zap.Error(err))
			continue
		}
		mappings = append(mappings, mapping)
	}

	return mappings, nil
}

// CreateKeycloakGroupMapping 创建Keycloak组映射
func (s *ConfigServiceImpl) CreateKeycloakGroupMapping(ctx context.Context, keycloakGroup, platformGroup string) error {
	ctx, span := s.tracer.Start(ctx, "ConfigService.CreateKeycloakGroupMapping")
	defer span.End()

	// 检查是否已存在相同的映射
	configKey := fmt.Sprintf("keycloak_group_mapping:%s", keycloakGroup)
	_, err := s.configRepo.GetByKey(ctx, configKey)
	if err == nil {
		return fmt.Errorf("已存在相同的Keycloak组映射: %s", keycloakGroup)
	} else if err != repository.ErrNotFound {
		return fmt.Errorf("检查Keycloak组映射失败: %w", err)
	}

	// 创建映射配置
	mapping := KeycloakGroupMappingConfig{
		KeycloakGroup: keycloakGroup,
		PlatformGroup: platformGroup,
	}

	data, err := json.Marshal(mapping)
	if err != nil {
		return fmt.Errorf("序列化映射配置失败: %w", err)
	}

	config := &model.Config{
		Type:  "keycloak_group_mapping",
		Key:   configKey,
		Value: string(data),
	}

	if err := s.configRepo.Create(ctx, config); err != nil {
		return fmt.Errorf("创建Keycloak组映射失败: %w", err)
	}

	s.log.Info("创建Keycloak组映射成功",
		zap.String("keycloak_group", keycloakGroup),
		zap.String("platform_group", platformGroup))

	return nil
}

// DeleteKeycloakGroupMapping 删除Keycloak组映射
func (s *ConfigServiceImpl) DeleteKeycloakGroupMapping(ctx context.Context, keycloakGroup string) error {
	ctx, span := s.tracer.Start(ctx, "ConfigService.DeleteKeycloakGroupMapping")
	defer span.End()

	// 检查映射是否存在
	configKey := fmt.Sprintf("keycloak_group_mapping:%s", keycloakGroup)
	config, err := s.configRepo.GetByKey(ctx, configKey)
	if err != nil {
		if err == repository.ErrNotFound {
			return fmt.Errorf("未找到指定的Keycloak组映射: %s", keycloakGroup)
		}
		return fmt.Errorf("获取Keycloak组映射失败: %w", err)
	}

	// 删除映射
	if err := s.configRepo.Delete(ctx, config.ID); err != nil {
		return fmt.Errorf("删除Keycloak组映射失败: %w", err)
	}

	s.log.Info("删除Keycloak组映射成功", zap.String("keycloak_group", keycloakGroup))
	return nil
}
