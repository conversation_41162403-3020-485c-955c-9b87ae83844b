package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.opentelemetry.io/otel"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// MockUserGroupRepository 用户组仓库模拟
type MockUserGroupRepository struct {
	mock.Mock
}

func (m *MockUserGroupRepository) CreateUserGroup(ctx context.Context, group *model.UserGroup) error {
	args := m.Called(ctx, group)
	return args.Error(0)
}

func (m *MockUserGroupRepository) GetUserGroupByID(ctx context.Context, id uint) (*model.UserGroup, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserGroup), args.Error(1)
}

func (m *MockUserGroupRepository) GetUserGroupByName(ctx context.Context, name string) (*model.UserGroup, error) {
	args := m.Called(ctx, name)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserGroup), args.Error(1)
}

func (m *MockUserGroupRepository) GetUserGroupByExternalID(ctx context.Context, externalID string) (*model.UserGroup, error) {
	args := m.Called(ctx, externalID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.UserGroup), args.Error(1)
}

func (m *MockUserGroupRepository) UpdateUserGroup(ctx context.Context, group *model.UserGroup) error {
	args := m.Called(ctx, group)
	return args.Error(0)
}

func (m *MockUserGroupRepository) DeleteUserGroup(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserGroupRepository) ListUserGroupsPaginated(ctx context.Context, page, pageSize int) ([]*model.UserGroup, int64, error) {
	args := m.Called(ctx, page, pageSize)
	return args.Get(0).([]*model.UserGroup), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserGroupRepository) AddUsersToGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	args := m.Called(ctx, groupID, userIDs)
	return args.Error(0)
}

func (m *MockUserGroupRepository) RemoveUsersFromGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	args := m.Called(ctx, groupID, userIDs)
	return args.Error(0)
}

func (m *MockUserGroupRepository) SetGroupMembers(ctx context.Context, groupID uint, userIDs []uint) error {
	args := m.Called(ctx, groupID, userIDs)
	return args.Error(0)
}

func (m *MockUserGroupRepository) GetGroupMembers(ctx context.Context, groupID uint) ([]*model.User, error) {
	args := m.Called(ctx, groupID)
	return args.Get(0).([]*model.User), args.Error(1)
}

func (m *MockUserGroupRepository) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*model.UserGroup), args.Error(1)
}

func (m *MockUserGroupRepository) IsUserInGroup(ctx context.Context, userID, groupID uint) (bool, error) {
	args := m.Called(ctx, userID, groupID)
	return args.Bool(0), args.Error(1)
}

func (m *MockUserGroupRepository) AssignPermissionsToGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	args := m.Called(ctx, groupID, permissionIDs)
	return args.Error(0)
}

func (m *MockUserGroupRepository) RemovePermissionsFromGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	args := m.Called(ctx, groupID, permissionIDs)
	return args.Error(0)
}

func (m *MockUserGroupRepository) GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	args := m.Called(ctx, groupID)
	return args.Get(0).([]*model.ResourcePermission), args.Error(1)
}

func (m *MockUserGroupRepository) GetKeycloakGroupMappings(ctx context.Context) ([]*model.KeycloakGroupMapping, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*model.KeycloakGroupMapping), args.Error(1)
}

func (m *MockUserGroupRepository) SaveKeycloakGroupMapping(ctx context.Context, mapping *model.KeycloakGroupMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *MockUserGroupRepository) DeleteKeycloakGroupMapping(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MockRepositoryForUserGroup 用户组测试的仓库模拟
type MockRepositoryForUserGroup struct {
	userGroupRepo *MockUserGroupRepository
}

func (m *MockRepositoryForUserGroup) User() repository.UserRepository {
	return nil
}

func (m *MockRepositoryForUserGroup) UserGroup() repository.UserGroupRepository {
	return m.userGroupRepo
}

func (m *MockRepositoryForUserGroup) Project() repository.ProjectRepository {
	return nil
}

func (m *MockRepositoryForUserGroup) Cluster() repository.ClusterRepository {
	return nil
}

func (m *MockRepositoryForUserGroup) Application() repository.ApplicationRepository {
	return nil
}

func (m *MockRepositoryForUserGroup) Permission() repository.PermissionRepository {
	return nil
}

func TestUserGroupService_CreateUserGroup(t *testing.T) {
	mockUserGroupRepo := &MockUserGroupRepository{}
	mockRepo := &MockRepositoryForUserGroup{userGroupRepo: mockUserGroupRepo}
	
	userGroupService := &userGroupService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	req := &model.UserGroupCreateRequest{
		Name:        "test-group",
		DisplayName: "Test Group",
		Description: "Test group description",
		ExternalID:  "keycloak-123",
	}

	mockUserGroupRepo.On("GetUserGroupByName", ctx, "test-group").Return(nil, nil)
	mockUserGroupRepo.On("CreateUserGroup", ctx, mock.AnythingOfType("*model.UserGroup")).Return(nil)

	group, err := userGroupService.CreateUserGroup(ctx, req)

	assert.NoError(t, err)
	assert.NotNil(t, group)
	assert.Equal(t, "test-group", group.Name)
	assert.Equal(t, "Test Group", group.DisplayName)
	assert.Equal(t, "Test group description", group.Description)
	assert.Equal(t, "keycloak-123", group.ExternalID)

	mockUserGroupRepo.AssertExpectations(t)
}

func TestUserGroupService_GetUserGroupByID(t *testing.T) {
	mockUserGroupRepo := &MockUserGroupRepository{}
	mockRepo := &MockRepositoryForUserGroup{userGroupRepo: mockUserGroupRepo}
	
	userGroupService := &userGroupService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	groupID := uint(1)
	expectedGroup := &model.UserGroup{
		ID:          groupID,
		Name:        "test-group",
		DisplayName: "Test Group",
		Description: "Test group description",
		CreatedAt:   time.Now(),
	}

	mockUserGroupRepo.On("GetUserGroupByID", ctx, groupID).Return(expectedGroup, nil)

	group, err := userGroupService.GetUserGroupByID(ctx, groupID)

	assert.NoError(t, err)
	assert.NotNil(t, group)
	assert.Equal(t, expectedGroup.ID, group.ID)
	assert.Equal(t, expectedGroup.Name, group.Name)
	assert.Equal(t, expectedGroup.DisplayName, group.DisplayName)

	mockUserGroupRepo.AssertExpectations(t)
}

func TestUserGroupService_AddUsersToGroup(t *testing.T) {
	mockUserGroupRepo := &MockUserGroupRepository{}
	mockRepo := &MockRepositoryForUserGroup{userGroupRepo: mockUserGroupRepo}
	
	userGroupService := &userGroupService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	groupID := uint(1)
	userIDs := []uint{1, 2, 3}
	existingGroup := &model.UserGroup{
		ID:   groupID,
		Name: "test-group",
	}

	mockUserGroupRepo.On("GetUserGroupByID", ctx, groupID).Return(existingGroup, nil)
	mockUserGroupRepo.On("AddUsersToGroup", ctx, groupID, userIDs).Return(nil)

	err := userGroupService.AddUsersToGroup(ctx, groupID, userIDs)

	assert.NoError(t, err)
	mockUserGroupRepo.AssertExpectations(t)
}

func TestUserGroupService_CreateOrUpdateKeycloakGroup(t *testing.T) {
	mockUserGroupRepo := &MockUserGroupRepository{}
	mockRepo := &MockRepositoryForUserGroup{userGroupRepo: mockUserGroupRepo}
	
	userGroupService := &userGroupService{
		repo:   mockRepo,
		tracer: otel.Tracer("test"),
	}

	ctx := context.Background()
	externalID := "keycloak-123"
	name := "keycloak-group"
	displayName := "Keycloak Group"

	mockUserGroupRepo.On("GetUserGroupByExternalID", ctx, externalID).Return(nil, nil)
	mockUserGroupRepo.On("CreateUserGroup", ctx, mock.AnythingOfType("*model.UserGroup")).Return(nil)

	group, err := userGroupService.CreateOrUpdateKeycloakGroup(ctx, externalID, name, displayName)

	assert.NoError(t, err)
	assert.NotNil(t, group)
	assert.Equal(t, name, group.Name)
	assert.Equal(t, displayName, group.DisplayName)
	assert.Equal(t, externalID, group.ExternalID)

	mockUserGroupRepo.AssertExpectations(t)
}
