package service

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/repository/mocks"
	"kubeops/pkg/k8s"
)

func TestApplicationSyncService_getProjectNamespaces(t *testing.T) {
	// 创建模拟仓库
	mockRepo := &mocks.Repository{}
	mockProjectRepo := &mocks.ProjectRepository{}
	mockRepo.On("Project").Return(mockProjectRepo)

	// 创建测试项目
	projects := []*model.Project{
		{
			ID:     1,
			Name:   "web-frontend",
			Status: model.ProjectStatusActive,
		},
		{
			ID:     2,
			Name:   "api-backend",
			Status: model.ProjectStatusActive,
		},
		{
			ID:     3,
			Name:   "data-service",
			Status: model.ProjectStatusInactive, // 非活跃项目
		},
	}

	mockProjectRepo.On("ListProjectsPaginated", mock.Anything, 1, 1000).Return(projects, int64(3), nil)

	// 创建同步服务
	syncService := &ApplicationSyncService{
		repo:   mockRepo,
		logger: zap.NewNop(),
		tracer: trace.NewNoopTracerProvider().Tracer("test"),
	}

	// 测试获取项目命名空间
	ctx := context.Background()
	namespaces, err := syncService.getProjectNamespaces(ctx)

	// 验证结果
	assert.NoError(t, err)
	assert.Len(t, namespaces, 2) // 只有活跃项目
	assert.Contains(t, namespaces, "web-frontend")
	assert.Contains(t, namespaces, "api-backend")
	assert.NotContains(t, namespaces, "data-service") // 非活跃项目不包含

	mockRepo.AssertExpectations(t)
	mockProjectRepo.AssertExpectations(t)
}

func TestApplicationSyncService_isNamespaceExcluded(t *testing.T) {
	syncService := &ApplicationSyncService{
		logger: zap.NewNop(),
	}

	excludedNamespaces := []string{"kube-system", "kube-public", "istio-system"}

	tests := []struct {
		namespace string
		excluded  bool
	}{
		{"web-frontend", false},
		{"api-backend", false},
		{"kube-system", true},
		{"kube-public", true},
		{"istio-system", true},
		{"monitoring", false},
	}

	for _, tt := range tests {
		t.Run(tt.namespace, func(t *testing.T) {
			result := syncService.isNamespaceExcluded(tt.namespace, excludedNamespaces)
			assert.Equal(t, tt.excluded, result)
		})
	}
}

func TestApplicationSyncService_syncWorkloadToApplication(t *testing.T) {
	// 创建模拟仓库
	mockRepo := &mocks.Repository{}
	mockProjectRepo := &mocks.ProjectRepository{}
	mockApplicationRepo := &mocks.ApplicationRepository{}
	
	mockRepo.On("Project").Return(mockProjectRepo)
	mockRepo.On("Application").Return(mockApplicationRepo)

	// 创建测试项目
	project := &model.Project{
		ID:     1,
		Name:   "web-frontend",
		Status: model.ProjectStatusActive,
	}

	// 创建工作负载事件
	event := &model.WorkloadEvent{
		ClusterID:       1,
		Namespace:       "web-frontend",
		WorkloadType:    "deployment",
		WorkloadName:    "nginx-deployment",
		EventType:       "ADDED",
		ResourceVersion: "12345",
		Labels: map[string]string{
			"app": "nginx",
		},
		Annotations: map[string]string{
			"deployment.kubernetes.io/revision": "1",
		},
		Timestamp: time.Now(),
	}

	// 设置模拟期望
	mockProjectRepo.On("GetProjectByName", mock.Anything, "web-frontend").Return(project, nil)
	mockApplicationRepo.On("GetApplicationByWorkload", mock.Anything, uint(1), "deployment", "nginx-deployment").Return(nil, assert.AnError) // 应用不存在
	mockApplicationRepo.On("CreateApplication", mock.Anything, mock.MatchedBy(func(app *model.Application) bool {
		return app.Name == "nginx-deployment" &&
			app.ProjectID == 1 &&
			app.WorkloadType == "deployment" &&
			app.Namespace == "web-frontend" &&
			app.Source == model.ApplicationSourceDiscovered &&
			app.SyncStatus == model.ApplicationSyncStatusPending
	})).Return(nil)

	// 模拟项目集群关联
	projectClusters := []*model.ProjectCluster{
		{
			ID:        1,
			ProjectID: 1,
			ClusterID: 1,
			Namespace: "web-frontend",
		},
	}
	mockProjectRepo.On("GetProjectClusters", mock.Anything, uint(1)).Return(projectClusters, nil)

	// 模拟应用集群部署记录
	mockApplicationRepo.On("GetApplicationCluster", mock.Anything, mock.AnythingOfType("uint"), uint(1)).Return(nil, assert.AnError) // 不存在
	mockApplicationRepo.On("CreateApplicationCluster", mock.Anything, mock.MatchedBy(func(ac *model.ApplicationCluster) bool {
		return ac.ProjectClusterID == 1 &&
			ac.Status == model.ApplicationClusterStatusRunning &&
			ac.ResourceVersion == "12345"
	})).Return(nil)

	// 创建同步服务
	syncService := &ApplicationSyncService{
		repo:   mockRepo,
		logger: zap.NewNop(),
		tracer: trace.NewNoopTracerProvider().Tracer("test"),
	}

	// 测试同步工作负载到应用
	ctx := context.Background()
	err := syncService.syncWorkloadToApplication(ctx, event)

	// 验证结果
	assert.NoError(t, err)

	mockRepo.AssertExpectations(t)
	mockProjectRepo.AssertExpectations(t)
	mockApplicationRepo.AssertExpectations(t)
}

func TestApplicationSyncService_handleWorkloadDeletion(t *testing.T) {
	// 创建模拟仓库
	mockRepo := &mocks.Repository{}
	mockProjectRepo := &mocks.ProjectRepository{}
	mockApplicationRepo := &mocks.ApplicationRepository{}
	
	mockRepo.On("Project").Return(mockProjectRepo)
	mockRepo.On("Application").Return(mockApplicationRepo)

	// 创建测试项目
	project := &model.Project{
		ID:     1,
		Name:   "web-frontend",
		Status: model.ProjectStatusActive,
	}

	// 创建测试应用
	app := &model.Application{
		ID:           1,
		Name:         "nginx-deployment",
		ProjectID:    1,
		WorkloadType: "deployment",
		WorkloadName: "nginx-deployment",
		Source:       model.ApplicationSourceDiscovered,
	}

	// 创建删除事件
	event := &model.WorkloadEvent{
		ClusterID:    1,
		Namespace:    "web-frontend",
		WorkloadType: "deployment",
		WorkloadName: "nginx-deployment",
		EventType:    "DELETED",
		Timestamp:    time.Now(),
	}

	// 设置模拟期望
	mockProjectRepo.On("GetProjectByName", mock.Anything, "web-frontend").Return(project, nil)
	mockApplicationRepo.On("GetApplicationByWorkload", mock.Anything, uint(1), "deployment", "nginx-deployment").Return(app, nil)

	// 模拟项目集群关联
	projectClusters := []*model.ProjectCluster{
		{
			ID:        1,
			ProjectID: 1,
			ClusterID: 1,
			Namespace: "web-frontend",
		},
	}
	mockProjectRepo.On("GetProjectClusters", mock.Anything, uint(1)).Return(projectClusters, nil)

	// 模拟删除应用集群部署记录
	mockApplicationRepo.On("DeleteApplicationCluster", mock.Anything, uint(1), uint(1)).Return(nil)

	// 模拟检查其他集群部署
	mockApplicationRepo.On("GetApplicationClusters", mock.Anything, uint(1)).Return([]*model.ApplicationCluster{}, nil) // 没有其他部署

	// 模拟删除应用（因为是自动发现的且没有其他部署）
	mockApplicationRepo.On("DeleteApplication", mock.Anything, uint(1)).Return(nil)

	// 创建同步服务
	syncService := &ApplicationSyncService{
		repo:   mockRepo,
		logger: zap.NewNop(),
		tracer: trace.NewNoopTracerProvider().Tracer("test"),
	}

	// 测试处理工作负载删除
	ctx := context.Background()
	err := syncService.handleWorkloadDeletion(ctx, event)

	// 验证结果
	assert.NoError(t, err)

	mockRepo.AssertExpectations(t)
	mockProjectRepo.AssertExpectations(t)
	mockApplicationRepo.AssertExpectations(t)
}
