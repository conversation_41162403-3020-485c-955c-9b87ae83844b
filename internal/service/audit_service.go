package service

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// AuditService 审计服务接口
type AuditService interface {
	// ListAuditLogs 获取审计日志列表
	ListAuditLogs(ctx context.Context, filter *model.AuditQueryFilter) ([]*model.AuditLog, int64, error)

	// ExportAuditLogs 导出审计日志
	ExportAuditLogs(ctx context.Context, filter *model.AuditQueryFilter, format string) ([]byte, error)

	// CleanupExpiredAuditLogs 清理过期审计日志
	CleanupExpiredAuditLogs(ctx context.Context) error

	// ListAuditArchives 获取审计归档列表
	ListAuditArchives(ctx context.Context, page, size int) ([]*model.AuditArchive, int64, error)

	// GetAuditArchive 获取审计归档详情
	GetAuditArchive(ctx context.Context, id uint) (*model.AuditArchive, error)

	// DeleteAuditArchive 删除审计归档
	DeleteAuditArchive(ctx context.Context, id uint) error

	// GetAuditArchiveConfig 获取审计归档配置
	GetAuditArchiveConfig(ctx context.Context) (*model.AuditArchiveConfig, error)

	// UpdateAuditArchiveConfig 更新审计归档配置
	UpdateAuditArchiveConfig(ctx context.Context, config *model.AuditArchiveConfig) error

	// ArchiveAuditLogs 归档审计日志
	ArchiveAuditLogs(ctx context.Context) error

	// UpdateOBSClient 更新OBS客户端
	UpdateOBSClient(obsClient interface{}) error

	// CleanupExpiredData 清理过期数据（与CleanupExpiredAuditLogs相同，保留兼容性）
	CleanupExpiredData(ctx context.Context) error

	// CreateQuarterlyArchive 创建季度归档
	CreateQuarterlyArchive(ctx context.Context, year int, quarter int) error
}

// auditService 审计服务实现
type auditService struct {
	repo   repository.Repository
	tracer trace.Tracer
}

// NewAuditService 创建审计服务
func NewAuditService(repo repository.Repository, tracer trace.Tracer) AuditService {
	return &auditService{
		repo:   repo,
		tracer: tracer,
	}
}

// ListAuditLogs 获取审计日志列表
func (s *auditService) ListAuditLogs(ctx context.Context, filter *model.AuditQueryFilter) ([]*model.AuditLog, int64, error) {
	ctx, span := s.tracer.Start(ctx, "AuditService.ListAuditLogs")
	defer span.End()

	return s.repo.Audit().ListAuditLogs(ctx, filter)
}

// ExportAuditLogs 导出审计日志
func (s *auditService) ExportAuditLogs(ctx context.Context, filter *model.AuditQueryFilter, format string) ([]byte, error) {
	ctx, span := s.tracer.Start(ctx, "AuditService.ExportAuditLogs")
	defer span.End()
	span.SetAttributes(attribute.String("format", format))

	return s.repo.Audit().ExportAuditLogs(ctx, filter, format)
}

// CleanupExpiredAuditLogs 清理过期审计日志
func (s *auditService) CleanupExpiredAuditLogs(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "AuditService.CleanupExpiredAuditLogs")
	defer span.End()

	// 获取配置
	config, err := s.repo.Audit().GetAuditArchiveConfig(ctx)
	if err != nil {
		span.RecordError(err)
		return err
	}

	return s.repo.Audit().CleanupExpiredAuditLogs(ctx, config.RetentionDays)
}

// ListAuditArchives 获取审计归档列表
func (s *auditService) ListAuditArchives(ctx context.Context, page, size int) ([]*model.AuditArchive, int64, error) {
	ctx, span := s.tracer.Start(ctx, "AuditService.ListAuditArchives")
	defer span.End()

	return s.repo.Audit().ListAuditArchives(ctx, page, size)
}

// GetAuditArchive 获取审计归档详情
func (s *auditService) GetAuditArchive(ctx context.Context, id uint) (*model.AuditArchive, error) {
	ctx, span := s.tracer.Start(ctx, "AuditService.GetAuditArchive")
	defer span.End()

	return s.repo.Audit().GetAuditArchive(ctx, id)
}

// DeleteAuditArchive 删除审计归档
func (s *auditService) DeleteAuditArchive(ctx context.Context, id uint) error {
	ctx, span := s.tracer.Start(ctx, "AuditService.DeleteAuditArchive")
	defer span.End()

	// 确认归档存在
	_, err := s.repo.Audit().GetAuditArchive(ctx, id)
	if err != nil {
		span.RecordError(err)
		return err
	}

	return s.repo.Audit().DeleteAuditArchive(ctx, id)
}

// GetAuditArchiveConfig 获取审计归档配置
func (s *auditService) GetAuditArchiveConfig(ctx context.Context) (*model.AuditArchiveConfig, error) {
	ctx, span := s.tracer.Start(ctx, "AuditService.GetAuditArchiveConfig")
	defer span.End()

	return s.repo.Audit().GetAuditArchiveConfig(ctx)
}

// UpdateAuditArchiveConfig 更新审计归档配置
func (s *auditService) UpdateAuditArchiveConfig(ctx context.Context, config *model.AuditArchiveConfig) error {
	ctx, span := s.tracer.Start(ctx, "AuditService.UpdateAuditArchiveConfig")
	defer span.End()

	return s.repo.Audit().UpdateAuditArchiveConfig(ctx, config)
}

// ArchiveAuditLogs 归档审计日志
func (s *auditService) ArchiveAuditLogs(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "AuditService.ArchiveAuditLogs")
	defer span.End()

	// 获取归档配置
	config, err := s.repo.Audit().GetAuditArchiveConfig(ctx)
	if err != nil {
		span.RecordError(err)
		return err
	}

	// 检查是否需要归档
	if config.ArchiveInterval == "0" {
		// 归档间隔为0表示不自动归档
		return nil
	}

	// 计算归档时间范围
	now := time.Now()
	var archiveBeforeTime time.Time

	switch config.ArchiveInterval {
	case "daily":
		archiveBeforeTime = now.AddDate(0, 0, -1)
	case "weekly":
		archiveBeforeTime = now.AddDate(0, 0, -7)
	case "monthly":
		archiveBeforeTime = now.AddDate(0, -1, 0)
	default:
		return fmt.Errorf("未知的归档间隔: %s", config.ArchiveInterval)
	}

	// 查询需要归档的日志
	filter := &model.AuditQueryFilter{
		EndTime: &archiveBeforeTime,
	}
	logs, _, err := s.repo.Audit().ListAuditLogs(ctx, filter)
	if err != nil {
		span.RecordError(err)
		return err
	}

	if len(logs) == 0 {
		// 没有需要归档的日志
		return nil
	}

	// 创建归档记录
	archive := &model.AuditArchive{
		ArchiveName:  fmt.Sprintf("Audit-%s", now.Format("20060102-150405")),
		Quarter:      fmt.Sprintf("%d-Q%d", now.Year(), (now.Month()-1)/3+1), // 当前季度
		StartTime:    logs[0].CreatedAt,
		EndTime:      archiveBeforeTime,
		RecordCount:  int64(len(logs)),
		FileSize:     0,
		OBSKey:       fmt.Sprintf("archives/%d/%s.csv", now.Year(), now.Format("20060102-150405")),
		Status:       "processing",
		ErrorMessage: "",
		CreatedBy:    0, // 系统自动归档
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// 保存归档记录
	if err := s.repo.Audit().CreateAuditArchive(ctx, archive); err != nil {
		span.RecordError(err)
		return err
	}

	// TODO: 实现归档文件生成逻辑

	// 更新归档状态
	archive.Status = "completed"
	archive.UpdatedAt = now
	if err := s.repo.Audit().UpdateAuditArchive(ctx, archive); err != nil {
		span.RecordError(err)
		return err
	}

	// 删除已归档的日志
	if err := s.repo.Audit().DeleteAuditLogs(ctx, archiveBeforeTime); err != nil {
		span.RecordError(err)
		return err
	}

	return nil
}

// UpdateOBSClient 更新OBS客户端
func (s *auditService) UpdateOBSClient(obsClient interface{}) error {
	// 这里只是一个兼容性方法，实际实现可能会在后续完善
	// 暂时不做任何操作，仅保持接口兼容性
	return nil
}

// CleanupExpiredData 清理过期数据（与CleanupExpiredAuditLogs相同，保留兼容性）
func (s *auditService) CleanupExpiredData(ctx context.Context) error {
	return s.CleanupExpiredAuditLogs(ctx)
}

// CreateQuarterlyArchive 创建季度归档
func (s *auditService) CreateQuarterlyArchive(ctx context.Context, year int, quarter int) error {
	ctx, span := s.tracer.Start(ctx, "AuditService.CreateQuarterlyArchive")
	defer span.End()

	span.SetAttributes(attribute.Int("year", year))
	span.SetAttributes(attribute.Int("quarter", quarter))

	// 计算季度的开始和结束时间
	startMonth := (quarter-1)*3 + 1
	startTime := time.Date(year, time.Month(startMonth), 1, 0, 0, 0, 0, time.Local)
	endTime := startTime.AddDate(0, 3, 0).Add(-time.Second)

	// 查询需要归档的日志
	filter := &model.AuditQueryFilter{
		StartTime: &startTime,
		EndTime:   &endTime,
	}
	_, count, err := s.repo.Audit().ListAuditLogs(ctx, filter)
	if err != nil {
		span.RecordError(err)
		return err
	}

	if count == 0 {
		// 没有需要归档的日志
		return nil
	}

	// 创建归档记录
	now := time.Now()
	archive := &model.AuditArchive{
		ArchiveName:  fmt.Sprintf("Audit-%d-Q%d", year, quarter),
		Quarter:      fmt.Sprintf("%d-Q%d", year, quarter),
		StartTime:    startTime,
		EndTime:      endTime,
		RecordCount:  count,
		FileSize:     0,
		OBSKey:       fmt.Sprintf("archives/%d/Q%d-%s.csv", year, quarter, now.Format("20060102-150405")),
		Status:       "processing",
		ErrorMessage: "",
		CreatedBy:    0, // 系统自动归档
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// 保存归档记录
	if err := s.repo.Audit().CreateAuditArchive(ctx, archive); err != nil {
		span.RecordError(err)
		return err
	}

	// 更新归档状态
	archive.Status = "completed"
	archive.UpdatedAt = now
	if err := s.repo.Audit().UpdateAuditArchive(ctx, archive); err != nil {
		span.RecordError(err)
		return err
	}

	return nil
}
