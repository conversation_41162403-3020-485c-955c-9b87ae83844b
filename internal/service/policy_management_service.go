package service

import (
	"context"
	"fmt"
	"strings"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// PolicyManagementService 策略管理服务
type PolicyManagementService struct {
	casbinService *CasbinService
	repo          repository.Repository
	logger        *zap.Logger
	tracer        trace.Tracer
}

// NewPolicyManagementService 创建策略管理服务
func NewPolicyManagementService(casbinService *CasbinService, repo repository.Repository, logger *zap.Logger, tracer trace.Tracer) *PolicyManagementService {
	return &PolicyManagementService{
		casbinService: casbinService,
		repo:          repo,
		logger:        logger,
		tracer:        tracer,
	}
}

// ImportPolicies 导入策略
func (s *PolicyManagementService) ImportPolicies(ctx context.Context, policies [][]string) error {
	ctx, span := s.tracer.Start(ctx, "PolicyManagementService.ImportPolicies")
	defer span.End()

	// 清空现有策略
	s.casbinService.enforcer.ClearPolicy()

	// 导入新策略
	for _, policy := range policies {
		if len(policy) < 2 {
			continue
		}

		ptype := policy[0]
		params := policy[1:]

		switch ptype {
		case "p":
			if len(params) >= 4 {
				// 转换为interface{}切片
				interfaceParams := make([]interface{}, len(params))
				for i, v := range params {
					interfaceParams[i] = v
				}
				_, err := s.casbinService.enforcer.AddPolicy(interfaceParams...)
				if err != nil {
					s.logger.Error("导入用户权限策略失败", zap.Strings("policy", params), zap.Error(err))
				}
			}
		case "p2":
			if len(params) >= 4 {
				// 转换为interface{}切片
				interfaceParams := make([]interface{}, len(params))
				for i, v := range params {
					interfaceParams[i] = v
				}
				_, err := s.casbinService.enforcer.AddNamedPolicy("p2", interfaceParams...)
				if err != nil {
					s.logger.Error("导入用户组权限策略失败", zap.Strings("policy", params), zap.Error(err))
				}
			}
		case "g":
			if len(params) >= 2 {
				// 转换为interface{}切片
				interfaceParams := make([]interface{}, len(params))
				for i, v := range params {
					interfaceParams[i] = v
				}
				_, err := s.casbinService.enforcer.AddGroupingPolicy(interfaceParams...)
				if err != nil {
					s.logger.Error("导入用户组成员关系失败", zap.Strings("policy", params), zap.Error(err))
				}
			}
		}
	}

	// 保存策略
	err := s.casbinService.enforcer.SavePolicy()
	if err != nil {
		return fmt.Errorf("保存导入的策略失败: %w", err)
	}

	s.logger.Info("策略导入成功", zap.Int("policy_count", len(policies)))
	return nil
}

// ValidatePolicy 验证策略语法
func (s *PolicyManagementService) ValidatePolicy(policyRule []string) error {
	if len(policyRule) < 2 {
		return fmt.Errorf("策略规则长度不足")
	}

	ptype := policyRule[0]
	params := policyRule[1:]

	switch ptype {
	case "p":
		if len(params) < 4 {
			return fmt.Errorf("用户权限策略需要至少4个参数: subject, resource_type, resource_path, action")
		}

		// 验证资源路径格式
		if !s.isValidResourcePath(params[2]) {
			return fmt.Errorf("无效的资源路径: %s", params[2])
		}

		// 验证操作格式
		if !s.isValidAction(params[3]) {
			return fmt.Errorf("无效的操作: %s", params[3])
		}

	case "p2":
		if len(params) < 4 {
			return fmt.Errorf("用户组权限策略需要至少4个参数: group, resource_type, resource_path, action")
		}

		// 验证资源路径格式
		if !s.isValidResourcePath(params[2]) {
			return fmt.Errorf("无效的资源路径: %s", params[2])
		}

		// 验证操作格式
		if !s.isValidAction(params[3]) {
			return fmt.Errorf("无效的操作: %s", params[3])
		}

	case "g":
		if len(params) < 2 {
			return fmt.Errorf("用户组成员关系需要2个参数: user, group")
		}

	default:
		return fmt.Errorf("未知的策略类型: %s", ptype)
	}

	return nil
}

// SyncPermissionPolicies 同步权限策略
func (s *PolicyManagementService) SyncPermissionPolicies(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "PolicyManagementService.SyncPermissionPolicies")
	defer span.End()

	// 1. 清空现有策略
	s.casbinService.enforcer.ClearPolicy()

	// 2. 同步用户组权限策略
	groups, _, err := s.repo.UserGroup().ListUserGroupsPaginated(ctx, 1, 1000)
	if err != nil {
		return fmt.Errorf("获取用户组列表失败: %w", err)
	}

	for _, group := range groups {
		permissions, err := s.repo.RBAC().GetGroupPermissions(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组权限失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		for _, perm := range permissions {
			// 添加用户组权限策略
			err = s.casbinService.AddGroupPolicy(group.Name, perm.ResourceType, perm.ResourcePath, perm.Actions)
			if err != nil {
				s.logger.Error("添加用户组权限策略失败",
					zap.String("group", group.Name),
					zap.String("resource_path", perm.ResourcePath),
					zap.Error(err))
			}
		}
	}

	// 3. 同步用户直接权限策略
	// TODO: 实现用户权限同步，需要添加ListAllUserPermissions方法
	s.logger.Info("跳过用户权限策略同步，需要实现ListAllUserPermissions方法")
	var userPermissions []*model.UserResourcePermission // 空切片，暂时跳过

	for _, userPerm := range userPermissions {
		permission, err := s.repo.RBAC().GetPermissionByID(ctx, userPerm.ResourcePermissionID)
		if err != nil {
			s.logger.Error("获取权限信息失败", zap.Uint("permission_id", userPerm.ResourcePermissionID), zap.Error(err))
			continue
		}

		err = s.casbinService.AddUserPolicy(userPerm.UserID, permission.ResourceType, permission.ResourcePath, permission.Actions)
		if err != nil {
			s.logger.Error("添加用户权限策略失败",
				zap.Uint("user_id", userPerm.UserID),
				zap.String("resource_path", permission.ResourcePath),
				zap.Error(err))
		}
	}

	// 4. 同步用户组成员关系
	// TODO: 实现用户组成员关系同步，需要添加ListAllUserGroups方法
	s.logger.Info("跳过用户组成员关系同步，需要实现ListAllUserGroups方法")

	// 暂时跳过这个循环
	for _, group := range groups {
		// 获取组成员并同步到casbin
		members, err := s.repo.UserGroup().GetGroupMembers(ctx, group.ID)
		if err != nil {
			s.logger.Error("获取用户组成员失败", zap.Uint("group_id", group.ID), zap.Error(err))
			continue
		}

		for _, member := range members {
			err = s.casbinService.AddUserToGroup(member.ID, group.Name)
			if err != nil {
				s.logger.Error("添加用户组成员关系失败",
					zap.Uint("user_id", member.ID),
					zap.String("username", member.Username),
					zap.String("group_name", group.Name),
					zap.Error(err))
			}
		}
	}

	// 5. 保存策略到数据库
	err = s.casbinService.enforcer.SavePolicy()
	if err != nil {
		return fmt.Errorf("保存策略失败: %w", err)
	}

	s.logger.Info("权限策略同步完成")
	return nil
}

// GetPolicyStatistics 获取策略统计信息
func (s *PolicyManagementService) GetPolicyStatistics(ctx context.Context) (map[string]int, error) {
	// ctx, span := s.tracer.Start(ctx, "PolicyManagementService.GetPolicyStatistics")
	// defer span.End()

	stats := make(map[string]int)

	// 用户直接权限策略数量
	userPolicies, err := s.casbinService.enforcer.GetPolicy()
	if err != nil {
		return nil, err
	}
	stats["user_policies"] = len(userPolicies)

	// 用户组权限策略数量
	groupPolicies, err := s.casbinService.enforcer.GetNamedPolicy("p2")
	if err != nil {
		return nil, err
	}
	stats["group_policies"] = len(groupPolicies)

	// 用户组成员关系数量
	groupings, err := s.casbinService.enforcer.GetGroupingPolicy()
	if err != nil {
		return nil, err
	}
	stats["user_group_relations"] = len(groupings)

	// 总策略数量
	stats["total_policies"] = stats["user_policies"] + stats["group_policies"] + stats["user_group_relations"]

	return stats, nil
}

// CleanupOrphanedPolicies 清理孤立的策略
func (s *PolicyManagementService) CleanupOrphanedPolicies(ctx context.Context) error {
	ctx, span := s.tracer.Start(ctx, "PolicyManagementService.CleanupOrphanedPolicies")
	defer span.End()

	// TODO: 实现清理孤立策略的逻辑
	// 1. 检查策略中引用的用户是否存在
	// 2. 检查策略中引用的用户组是否存在
	// 3. 检查策略中引用的权限是否存在
	// 4. 删除无效的策略

	s.logger.Info("孤立策略清理完成")
	return nil
}

// isValidResourcePath 验证资源路径格式
func (s *PolicyManagementService) isValidResourcePath(path string) bool {
	// 系统级资源
	if strings.HasPrefix(path, "system:") {
		return true
	}

	// 集群级资源
	if strings.HasPrefix(path, "cluster:") {
		parts := strings.Split(path, ":")
		return len(parts) >= 3
	}

	// 通配符
	if path == "*" {
		return true
	}

	return false
}

// isValidAction 验证操作格式
func (s *PolicyManagementService) isValidAction(action string) bool {
	validActions := []string{"*", "create", "read", "update", "delete", "list", "watch", "exec", "get"}

	// 通配符
	if action == "*" {
		return true
	}

	// 支持多个操作用逗号分隔
	actions := strings.Split(action, ",")
	for _, act := range actions {
		act = strings.TrimSpace(act)
		found := false
		for _, valid := range validActions {
			if act == valid {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	return true
}

// extractDomainFromPath 从权限路径中提取域信息
func (s *PolicyManagementService) extractDomainFromPath(resourcePath string) string {
	// 从资源路径中提取集群名称作为域
	if strings.HasPrefix(resourcePath, "cluster:") {
		parts := strings.Split(resourcePath, ":")
		if len(parts) >= 2 {
			return parts[1]
		}
	}

	// 系统级资源使用通配符域
	return "*"
}
