package service

import (
	"context"
	"sync"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/repository"
	"kubeops/pkg/lark"
	"kubeops/pkg/obs"
)

// IntegrationServiceImpl 集成服务实现
type IntegrationServiceImpl struct {
	repo         repository.Repository
	logger       *zap.Logger
	tracer       trace.Tracer
	feishuConfig *lark.FeishuConfig
	feishuClient interface{}
	obsConfig    *obs.OBSConfig
	obsClient    interface{}
	mu           sync.RWMutex // 保护服务实例的读写锁
}

// NewIntegrationService 创建集成服务
func NewIntegrationService(
	repo repository.Repository,
	logger *zap.Logger,
	tracer trace.Tracer,
) IntegrationService {
	svc := &IntegrationServiceImpl{
		repo:   repo,
		logger: logger,
		tracer: tracer,
	}

	// 尝试从数据库加载初始配置
	ctx := context.Background()

	// 加载飞书配置
	feishuConfig, err := repo.SystemConfig().GetFeishuConfig(ctx)
	if err == nil && feishuConfig != nil && feishuConfig.AppID != "" {
		larkConfig := &lark.FeishuConfig{
			AppID:     feishuConfig.AppID,
			AppSecret: feishuConfig.AppSecret,
		}
		svc.feishuConfig = larkConfig
		// 创建飞书客户端
		// svc.feishuClient = lark.NewFeishuClient(larkConfig)
		logger.Info("已从数据库初始化飞书配置")
	}

	// 加载OBS配置
	obsConfig, err := repo.SystemConfig().GetOBSConfig(ctx)
	if err == nil && obsConfig != nil && obsConfig.Enabled {
		config := &obs.OBSConfig{
			Enabled:       obsConfig.Enabled,
			Endpoint:      obsConfig.Endpoint,
			AccessKey:     obsConfig.AccessKey,
			SecretKey:     obsConfig.SecretKey,
			Bucket:        obsConfig.Bucket,
			Region:        obsConfig.Region,
			EncryptionKey: obsConfig.EncryptionKey,
		}

		svc.obsConfig = config
		obsClient, err := obs.NewOBSClient(config)
		if err == nil {
			svc.obsClient = obsClient
			logger.Info("已从数据库初始化OBS客户端",
				zap.String("endpoint", config.Endpoint),
				zap.String("bucket", config.Bucket))
		} else {
			logger.Error("初始化OBS客户端失败", zap.Error(err))
		}
	}

	return svc
}

// GetFeishuConfig 获取飞书配置
func (s *IntegrationServiceImpl) GetFeishuConfig() *lark.FeishuConfig {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.feishuConfig
}

// UpdateFeishuConfig 更新飞书配置
func (s *IntegrationServiceImpl) UpdateFeishuConfig(config *lark.FeishuConfig) error {
	if config == nil {
		return nil
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.feishuConfig = config
	// 更新飞书客户端
	// s.feishuClient = lark.NewFeishuClient(config)
	s.logger.Info("已更新飞书配置",
		zap.String("app_id", config.AppID))

	return nil
}

// GetFeishuClient 获取飞书客户端
func (s *IntegrationServiceImpl) GetFeishuClient() interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.feishuClient
}

// GetOBSConfig 获取OBS配置
func (s *IntegrationServiceImpl) GetOBSConfig() *obs.OBSConfig {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.obsConfig
}

// UpdateOBSConfig 更新OBS配置
func (s *IntegrationServiceImpl) UpdateOBSConfig(config *obs.OBSConfig) error {
	if config == nil {
		return nil
	}

	// 如果禁用了OBS，则清除客户端
	if !config.Enabled {
		s.mu.Lock()
		defer s.mu.Unlock()

		s.obsConfig = config
		s.obsClient = nil
		s.logger.Info("OBS客户端已禁用")
		return nil
	}

	obsClient, err := obs.NewOBSClient(config)
	if err != nil {
		s.logger.Error("创建OBS客户端失败", zap.Error(err))
		return err
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	s.obsConfig = config
	s.obsClient = obsClient
	s.logger.Info("OBS客户端已更新",
		zap.Bool("enabled", config.Enabled),
		zap.String("endpoint", config.Endpoint),
		zap.String("bucket", config.Bucket),
		zap.String("region", config.Region))

	return nil
}

// GetOBSClient 获取OBS客户端
func (s *IntegrationServiceImpl) GetOBSClient() interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.obsClient
}
