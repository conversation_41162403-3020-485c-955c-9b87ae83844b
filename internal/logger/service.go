package logger

import (
	"context"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// LoggerService 定义统一的日志服务接口
// 这个接口抽象了所有日志操作，确保整个应用使用一致的日志配置和格式
type LoggerService interface {
	// 基础日志方法
	Debug(msg string, fields ...zapcore.Field)
	Info(msg string, fields ...zapcore.Field)
	Warn(msg string, fields ...zapcore.Field)
	Error(msg string, fields ...zapcore.Field)
	Fatal(msg string, fields ...zapcore.Field)

	// 格式化日志方法（便利方法）
	Debugf(template string, args ...interface{})
	Infof(template string, args ...interface{})
	Warnf(template string, args ...interface{})
	Errorf(template string, args ...interface{})
	Fatalf(template string, args ...interface{})

	// 上下文感知的日志方法（支持OpenTelemetry追踪）
	DebugContext(ctx context.Context, msg string, fields ...zap.Field)
	InfoContext(ctx context.Context, msg string, fields ...zap.Field)
	WarnContext(ctx context.Context, msg string, fields ...zap.Field)
	ErrorContext(ctx context.Context, msg string, fields ...zap.Field)
	FatalContext(ctx context.Context, msg string, fields ...zap.Field)

	// 带字段的日志方法
	With(fields ...zapcore.Field) LoggerService

	// 实用方法
	GetLogger() *zap.Logger
	IsInitialized() bool
	Sync() error
}

// loggerService 实现LoggerService接口
type loggerService struct {
	// 使用全局logger实例，确保配置一致性
}

// NewLoggerService 创建新的日志服务实例
// 这个服务使用全局配置的logger，确保所有服务使用相同的日志配置
func NewLoggerService() LoggerService {
	return &loggerService{}
}

// Debug 记录Debug级别日志
func (l *loggerService) Debug(msg string, fields ...zapcore.Field) {
	Debug(msg, fields...)
}

// Info 记录Info级别日志
func (l *loggerService) Info(msg string, fields ...zapcore.Field) {
	Info(msg, fields...)
}

// Warn 记录Warn级别日志
func (l *loggerService) Warn(msg string, fields ...zapcore.Field) {
	Warn(msg, fields...)
}

// Error 记录Error级别日志
func (l *loggerService) Error(msg string, fields ...zapcore.Field) {
	Error(msg, fields...)
}

// Fatal 记录Fatal级别日志并退出程序
func (l *loggerService) Fatal(msg string, fields ...zapcore.Field) {
	Fatal(msg, fields...)
}

// Debugf 记录格式化Debug日志
func (l *loggerService) Debugf(template string, args ...interface{}) {
	Debugf(template, args...)
}

// Infof 记录格式化Info日志
func (l *loggerService) Infof(template string, args ...interface{}) {
	Infof(template, args...)
}

// Warnf 记录格式化Warn日志
func (l *loggerService) Warnf(template string, args ...interface{}) {
	Warnf(template, args...)
}

// Errorf 记录格式化Error日志
func (l *loggerService) Errorf(template string, args ...interface{}) {
	Errorf(template, args...)
}

// Fatalf 记录格式化Fatal日志并退出程序
func (l *loggerService) Fatalf(template string, args ...interface{}) {
	Fatalf(template, args...)
}

// DebugContext 记录带上下文的Debug日志
func (l *loggerService) DebugContext(ctx context.Context, msg string, fields ...zap.Field) {
	DebugContext(ctx, msg, fields...)
}

// InfoContext 记录带上下文的Info日志
func (l *loggerService) InfoContext(ctx context.Context, msg string, fields ...zap.Field) {
	InfoContext(ctx, msg, fields...)
}

// WarnContext 记录带上下文的Warn日志
func (l *loggerService) WarnContext(ctx context.Context, msg string, fields ...zap.Field) {
	WarnContext(ctx, msg, fields...)
}

// ErrorContext 记录带上下文的Error日志
func (l *loggerService) ErrorContext(ctx context.Context, msg string, fields ...zap.Field) {
	ErrorContext(ctx, msg, fields...)
}

// FatalContext 记录带上下文的Fatal日志并退出程序
func (l *loggerService) FatalContext(ctx context.Context, msg string, fields ...zap.Field) {
	FatalContext(ctx, msg, fields...)
}

// With 创建带有预设字段的日志服务
func (l *loggerService) With(fields ...zapcore.Field) LoggerService {
	// 创建一个新的logger服务实例，带有预设字段
	return &loggerServiceWithFields{
		fields: fields,
	}
}

// GetLogger 获取底层的zap.Logger实例
func (l *loggerService) GetLogger() *zap.Logger {
	return GetLogger()
}

// IsInitialized 检查日志系统是否已初始化
func (l *loggerService) IsInitialized() bool {
	return IsInitialized()
}

// Sync 同步日志缓冲区
func (l *loggerService) Sync() error {
	Sync()
	return nil
}

// loggerServiceWithFields 带有预设字段的日志服务
type loggerServiceWithFields struct {
	fields []zapcore.Field
}

// Debug 记录Debug级别日志（带预设字段）
func (l *loggerServiceWithFields) Debug(msg string, fields ...zapcore.Field) {
	allFields := append(l.fields, fields...)
	Debug(msg, allFields...)
}

// Info 记录Info级别日志（带预设字段）
func (l *loggerServiceWithFields) Info(msg string, fields ...zapcore.Field) {
	allFields := append(l.fields, fields...)
	Info(msg, allFields...)
}

// Warn 记录Warn级别日志（带预设字段）
func (l *loggerServiceWithFields) Warn(msg string, fields ...zapcore.Field) {
	allFields := append(l.fields, fields...)
	Warn(msg, allFields...)
}

// Error 记录Error级别日志（带预设字段）
func (l *loggerServiceWithFields) Error(msg string, fields ...zapcore.Field) {
	allFields := append(l.fields, fields...)
	Error(msg, allFields...)
}

// Fatal 记录Fatal级别日志并退出程序（带预设字段）
func (l *loggerServiceWithFields) Fatal(msg string, fields ...zapcore.Field) {
	allFields := append(l.fields, fields...)
	Fatal(msg, allFields...)
}

// Debugf 记录格式化Debug日志
func (l *loggerServiceWithFields) Debugf(template string, args ...interface{}) {
	Debugf(template, args...)
}

// Infof 记录格式化Info日志
func (l *loggerServiceWithFields) Infof(template string, args ...interface{}) {
	Infof(template, args...)
}

// Warnf 记录格式化Warn日志
func (l *loggerServiceWithFields) Warnf(template string, args ...interface{}) {
	Warnf(template, args...)
}

// Errorf 记录格式化Error日志
func (l *loggerServiceWithFields) Errorf(template string, args ...interface{}) {
	Errorf(template, args...)
}

// Fatalf 记录格式化Fatal日志并退出程序
func (l *loggerServiceWithFields) Fatalf(template string, args ...interface{}) {
	Fatalf(template, args...)
}

// DebugContext 记录带上下文的Debug日志（带预设字段）
func (l *loggerServiceWithFields) DebugContext(ctx context.Context, msg string, fields ...zap.Field) {
	allFields := append(l.fields, fields...)
	DebugContext(ctx, msg, allFields...)
}

// InfoContext 记录带上下文的Info日志（带预设字段）
func (l *loggerServiceWithFields) InfoContext(ctx context.Context, msg string, fields ...zap.Field) {
	allFields := append(l.fields, fields...)
	InfoContext(ctx, msg, allFields...)
}

// WarnContext 记录带上下文的Warn日志（带预设字段）
func (l *loggerServiceWithFields) WarnContext(ctx context.Context, msg string, fields ...zap.Field) {
	allFields := append(l.fields, fields...)
	WarnContext(ctx, msg, allFields...)
}

// ErrorContext 记录带上下文的Error日志（带预设字段）
func (l *loggerServiceWithFields) ErrorContext(ctx context.Context, msg string, fields ...zap.Field) {
	allFields := append(l.fields, fields...)
	ErrorContext(ctx, msg, allFields...)
}

// FatalContext 记录带上下文的Fatal日志并退出程序（带预设字段）
func (l *loggerServiceWithFields) FatalContext(ctx context.Context, msg string, fields ...zap.Field) {
	allFields := append(l.fields, fields...)
	FatalContext(ctx, msg, allFields...)
}

// With 创建带有更多预设字段的日志服务
func (l *loggerServiceWithFields) With(fields ...zapcore.Field) LoggerService {
	allFields := append(l.fields, fields...)
	return &loggerServiceWithFields{
		fields: allFields,
	}
}

// GetLogger 获取底层的zap.Logger实例
func (l *loggerServiceWithFields) GetLogger() *zap.Logger {
	return GetLogger()
}

// IsInitialized 检查日志系统是否已初始化
func (l *loggerServiceWithFields) IsInitialized() bool {
	return IsInitialized()
}

// Sync 同步日志缓冲区
func (l *loggerServiceWithFields) Sync() error {
	Sync()
	return nil
}
