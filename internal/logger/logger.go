package logger

import (
	"context"
	"fmt"
	"os"
	"sync"

	"kubeops/internal/config"

	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	logger *zap.Logger
	once   sync.Once
)

// InitLogger 初始化日志

func InitLogger(cfg *config.Config) *zap.Logger {
	once.Do(func() {
		// 定义日志级别
		var level zapcore.Level
		switch cfg.Log.Level {
		case "debug":
			level = zapcore.DebugLevel
		case "info":
			level = zapcore.InfoLevel
		case "warn":
			level = zapcore.WarnLevel
		case "error":
			level = zapcore.ErrorLevel
		default:
			level = zapcore.InfoLevel
		}

		// 定义日志输出位置
		var output zapcore.WriteSyncer
		if cfg.Log.Output == "stdout" {
			output = zapcore.AddSync(os.Stdout)
		} else {
			file, err := os.OpenFile(cfg.Log.FilePath, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
			if err != nil {
				// 如果无法打开日志文件，回退到标准输出并记录错误
				fmt.Fprintf(os.Stderr, "警告: 无法打开日志文件 %s: %v，回退到标准输出\n", cfg.Log.FilePath, err)
				output = zapcore.AddSync(os.Stdout)
			} else {
				output = zapcore.AddSync(file)
			}
		}

		// 配置Encoder
		var encoderConfig zapcore.EncoderConfig
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.TimeKey = "time"
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

		// 选择Encoder类型
		var encoder zapcore.Encoder
		if cfg.Log.Format == "json" {
			encoder = zapcore.NewJSONEncoder(encoderConfig)
		} else {
			encoder = zapcore.NewConsoleEncoder(encoderConfig)
		}

		// 创建Core
		core := zapcore.NewCore(encoder, output, level)

		// 创建Logger
		logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
		zap.ReplaceGlobals(logger)
	})

	return logger
}

// Debug logs a message at DebugLevel
func Debug(msg string, fields ...zapcore.Field) {
	if logger == nil {
		return
	}
	logger.Debug(msg, fields...)
}

// Info logs a message at InfoLevel
func Info(msg string, fields ...zapcore.Field) {
	if logger == nil {
		return
	}
	logger.Info(msg, fields...)
}

// Warn logs a message at WarnLevel
func Warn(msg string, fields ...zapcore.Field) {
	if logger == nil {
		return
	}
	logger.Warn(msg, fields...)
}

// Error logs a message at ErrorLevel
func Error(msg string, fields ...zapcore.Field) {
	if logger == nil {
		return
	}
	logger.Error(msg, fields...)
}

// Fatal logs a message at FatalLevel and then calls os.Exit(1)
func Fatal(msg string, fields ...zapcore.Field) {
	if logger == nil {
		return
	}
	logger.Fatal(msg, fields...)
}

// With creates a child logger with the field added to its context
func With(fields ...zapcore.Field) *zap.Logger {
	if logger == nil {
		return nil
	}
	return logger.With(fields...)
}

// Sync 刷新缓冲区日志
func Sync() {
	if logger != nil {
		_ = logger.Sync()
	}
}

// extractTraceInfo 从上下文中提取追踪信息
func extractTraceInfo(ctx context.Context) []zap.Field {
	if ctx == nil {
		return nil
	}

	span := trace.SpanFromContext(ctx)
	if !span.SpanContext().IsValid() {
		return nil
	}

	traceID := span.SpanContext().TraceID().String()
	spanID := span.SpanContext().SpanID().String()

	return []zap.Field{
		zap.String("trace_id", traceID),
		zap.String("span_id", spanID),
	}
}

// DebugContext 使用上下文记录Debug级别日志
func DebugContext(ctx context.Context, msg string, fields ...zap.Field) {
	if logger == nil {
		return
	}

	// 添加追踪信息
	traceFields := extractTraceInfo(ctx)
	if len(traceFields) > 0 {
		fields = append(fields, traceFields...)
	}

	logger.Debug(msg, fields...)
}

// InfoContext 使用上下文记录Info级别日志
func InfoContext(ctx context.Context, msg string, fields ...zap.Field) {
	if logger == nil {
		return
	}

	// 添加追踪信息
	traceFields := extractTraceInfo(ctx)
	if len(traceFields) > 0 {
		fields = append(fields, traceFields...)
	}

	logger.Info(msg, fields...)
}

// WarnContext 使用上下文记录Warn级别日志
func WarnContext(ctx context.Context, msg string, fields ...zap.Field) {
	if logger == nil {
		return
	}

	// 添加追踪信息
	traceFields := extractTraceInfo(ctx)
	if len(traceFields) > 0 {
		fields = append(fields, traceFields...)
	}

	logger.Warn(msg, fields...)
}

// ErrorContext 使用上下文记录Error级别日志
func ErrorContext(ctx context.Context, msg string, fields ...zap.Field) {
	if logger == nil {
		return
	}

	// 添加追踪信息
	traceFields := extractTraceInfo(ctx)
	if len(traceFields) > 0 {
		fields = append(fields, traceFields...)
	}

	logger.Error(msg, fields...)
}

// FatalContext 使用上下文记录Fatal级别日志
func FatalContext(ctx context.Context, msg string, fields ...zap.Field) {
	if logger == nil {
		return
	}

	// 添加追踪信息
	traceFields := extractTraceInfo(ctx)
	if len(traceFields) > 0 {
		fields = append(fields, traceFields...)
	}

	logger.Fatal(msg, fields...)
}

// GetLogger 获取当前的zap.Logger实例
func GetLogger() *zap.Logger {
	return logger
}

// IsInitialized 检查日志系统是否已初始化
func IsInitialized() bool {
	return logger != nil
}

// SetLevel 动态设置日志级别
func SetLevel(level zapcore.Level) {
	if logger == nil {
		return
	}
	// 注意：zap不支持动态修改级别，这里提供接口但实际需要重新初始化
	// 在生产环境中，建议通过配置文件或环境变量来控制日志级别
}

// Debugf 格式化Debug日志 - 便利方法
func Debugf(template string, args ...interface{}) {
	if logger == nil {
		return
	}
	logger.Sugar().Debugf(template, args...)
}

// Infof 格式化Info日志 - 便利方法
func Infof(template string, args ...interface{}) {
	if logger == nil {
		return
	}
	logger.Sugar().Infof(template, args...)
}

// Warnf 格式化Warn日志 - 便利方法
func Warnf(template string, args ...interface{}) {
	if logger == nil {
		return
	}
	logger.Sugar().Warnf(template, args...)
}

// Errorf 格式化Error日志 - 便利方法
func Errorf(template string, args ...interface{}) {
	if logger == nil {
		return
	}
	logger.Sugar().Errorf(template, args...)
}

// Fatalf 格式化Fatal日志 - 便利方法
func Fatalf(template string, args ...interface{}) {
	if logger == nil {
		return
	}
	logger.Sugar().Fatalf(template, args...)
}
