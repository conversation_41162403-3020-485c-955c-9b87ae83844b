package auth

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"

	"kubeops/internal/config"
	"kubeops/internal/redis"
)

// JWTBlacklistService JWT黑名单服务
type JWTBlacklistService struct {
	redisManager redis.RedisManager
	config       *config.Config
	logger       *zap.Logger
}

// NewJWTBlacklistService 创建JWT黑名单服务
func NewJWTBlacklistService(redisManager redis.RedisManager, cfg *config.Config) *JWTBlacklistService {
	return &JWTBlacklistService{
		redisManager: redisManager,
		config:       cfg,
		logger:       zap.L().Named("jwt.blacklist"),
	}
}

// BlacklistToken 将JWT加入黑名单
func (s *JWTBlacklistService) BlacklistToken(ctx context.Context, tokenString string, reason string) error {
	// 1. 解析JWT获取Claims
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.Auth.JWTSecret), nil
	})
	
	if err != nil {
		return fmt.Errorf("failed to parse token: %w", err)
	}
	
	claims, ok := token.Claims.(*JWTClaims)
	if !ok {
		return fmt.Errorf("invalid token claims")
	}
	
	// 2. 计算剩余有效期
	now := time.Now()
	if claims.ExpiresAt == nil {
		return fmt.Errorf("token has no expiration time")
	}
	
	expiresAt := claims.ExpiresAt.Time
	if expiresAt.Before(now) {
		return nil // 已过期的token无需加入黑名单
	}
	
	ttl := expiresAt.Sub(now)
	
	// 3. 构建黑名单键
	blacklistKey := s.buildBlacklistKey(claims.ID)
	
	// 4. 存储到Redis
	blacklistData := map[string]interface{}{
		"token_id":       claims.ID,
		"user_id":        claims.UserID,
		"token_type":     claims.TokenType,
		"reason":         reason,
		"blacklisted_at": now.Unix(),
		"expires_at":     claims.ExpiresAt.Unix(),
		"session_id":     claims.SessionID,
	}
	
	// 使用Hash存储黑名单数据
	for field, value := range blacklistData {
		if err := s.redisManager.HSet(ctx, blacklistKey, field, value); err != nil {
			return fmt.Errorf("failed to store blacklist data: %w", err)
		}
	}
	
	// 设置过期时间
	if err := s.redisManager.Expire(ctx, blacklistKey, ttl); err != nil {
		return fmt.Errorf("failed to set blacklist expiration: %w", err)
	}
	
	s.logger.Info("Token blacklisted",
		zap.String("token_id", claims.ID),
		zap.Uint("user_id", claims.UserID),
		zap.String("reason", reason),
		zap.Duration("ttl", ttl))
	
	return nil
}

// IsTokenBlacklisted 检查JWT是否在黑名单中
func (s *JWTBlacklistService) IsTokenBlacklisted(ctx context.Context, tokenID string) (bool, error) {
	blacklistKey := s.buildBlacklistKey(tokenID)
	exists, err := s.redisManager.Exists(ctx, blacklistKey)
	if err != nil {
		return false, fmt.Errorf("failed to check blacklist: %w", err)
	}
	
	return exists > 0, nil
}

// BlacklistUserTokens 将用户的所有token加入黑名单
func (s *JWTBlacklistService) BlacklistUserTokens(ctx context.Context, userID uint, reason string) error {
	// 1. 查找用户的所有活跃会话
	sessionPattern := fmt.Sprintf("auth:activity:user:%d:*", userID)
	sessions, err := s.redisManager.Keys(ctx, sessionPattern)
	if err != nil {
		return fmt.Errorf("failed to find user sessions: %w", err)
	}
	
	blacklistedCount := 0
	
	// 2. 获取会话中的token信息并加入黑名单
	for _, sessionKey := range sessions {
		tokenData, err := s.redisManager.HGetAll(ctx, sessionKey)
		if err != nil {
			s.logger.Warn("Failed to get session data", 
				zap.String("session_key", sessionKey), 
				zap.Error(err))
			continue
		}
		
		tokenID, exists := tokenData["token_id"]
		if !exists || tokenID == "" {
			continue
		}
		
		// 3. 将token加入黑名单
		blacklistKey := s.buildBlacklistKey(tokenID)
		
		// 检查是否已经在黑名单中
		alreadyBlacklisted, err := s.redisManager.Exists(ctx, blacklistKey)
		if err != nil {
			s.logger.Warn("Failed to check existing blacklist", 
				zap.String("token_id", tokenID), 
				zap.Error(err))
			continue
		}
		
		if alreadyBlacklisted > 0 {
			continue // 已经在黑名单中
		}
		
		blacklistData := map[string]interface{}{
			"token_id":       tokenID,
			"user_id":        userID,
			"reason":         reason,
			"blacklisted_at": time.Now().Unix(),
			"session_key":    sessionKey,
		}
		
		// 存储黑名单数据
		for field, value := range blacklistData {
			if err := s.redisManager.HSet(ctx, blacklistKey, field, value); err != nil {
				s.logger.Warn("Failed to store blacklist data", 
					zap.String("token_id", tokenID), 
					zap.Error(err))
				break
			}
		}
		
		// 设置默认过期时间（如果无法确定具体过期时间）
		defaultTTL := s.config.RedisModules.AuthSecurity.TTL.JWTBlacklist
		s.redisManager.Expire(ctx, blacklistKey, defaultTTL)
		
		blacklistedCount++
	}
	
	s.logger.Info("User tokens blacklisted",
		zap.Uint("user_id", userID),
		zap.String("reason", reason),
		zap.Int("count", blacklistedCount))
	
	return nil
}

// GetBlacklistInfo 获取黑名单信息
func (s *JWTBlacklistService) GetBlacklistInfo(ctx context.Context, tokenID string) (map[string]string, error) {
	blacklistKey := s.buildBlacklistKey(tokenID)
	return s.redisManager.HGetAll(ctx, blacklistKey)
}

// CleanupExpiredBlacklist 清理过期的黑名单记录
func (s *JWTBlacklistService) CleanupExpiredBlacklist(ctx context.Context) error {
	// Redis的过期机制会自动清理过期的键
	// 这里可以实现额外的清理逻辑，比如统计清理的数量
	
	pattern := s.buildBlacklistKey("*")
	cursor := uint64(0)
	cleanedCount := 0
	
	for {
		keys, nextCursor, err := s.redisManager.Scan(ctx, cursor, pattern, 100)
		if err != nil {
			return fmt.Errorf("failed to scan blacklist keys: %w", err)
		}
		
		for _, key := range keys {
			// 检查键是否存在（可能已经过期）
			exists, err := s.redisManager.Exists(ctx, key)
			if err != nil {
				continue
			}
			
			if exists == 0 {
				cleanedCount++
			}
		}
		
		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}
	
	if cleanedCount > 0 {
		s.logger.Info("Cleaned up expired blacklist entries", zap.Int("count", cleanedCount))
	}
	
	return nil
}

// GetBlacklistStats 获取黑名单统计信息
func (s *JWTBlacklistService) GetBlacklistStats(ctx context.Context) (*BlacklistStats, error) {
	pattern := s.buildBlacklistKey("*")
	cursor := uint64(0)
	stats := &BlacklistStats{
		TotalEntries: 0,
		ByReason:     make(map[string]int),
		ByTokenType:  make(map[string]int),
	}
	
	for {
		keys, nextCursor, err := s.redisManager.Scan(ctx, cursor, pattern, 100)
		if err != nil {
			return nil, fmt.Errorf("failed to scan blacklist keys: %w", err)
		}
		
		for _, key := range keys {
			// 获取黑名单信息
			info, err := s.redisManager.HGetAll(ctx, key)
			if err != nil {
				continue
			}
			
			stats.TotalEntries++
			
			if reason, exists := info["reason"]; exists {
				stats.ByReason[reason]++
			}
			
			if tokenType, exists := info["token_type"]; exists {
				stats.ByTokenType[tokenType]++
			}
		}
		
		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}
	
	return stats, nil
}

// buildBlacklistKey 构建黑名单键
func (s *JWTBlacklistService) buildBlacklistKey(tokenID string) string {
	prefix := s.config.RedisModules.AuthSecurity.Keys.BlacklistPrefix
	return fmt.Sprintf("%s%s", prefix, tokenID)
}

// BlacklistStats 黑名单统计信息
type BlacklistStats struct {
	TotalEntries int            `json:"total_entries"`
	ByReason     map[string]int `json:"by_reason"`
	ByTokenType  map[string]int `json:"by_token_type"`
}
