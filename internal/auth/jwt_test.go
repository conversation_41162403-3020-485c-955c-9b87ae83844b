package auth

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"kubeops/internal/config"
)

func createTestConfig() *config.Config {
	cfg := &config.Config{}
	
	// JWT配置
	cfg.Auth.JWTSecret = "test-jwt-secret-key-at-least-32-characters"
	cfg.Auth.JWTAlgorithm = "HS256"
	cfg.Auth.JWTExpire = 24 * time.Hour
	
	// Token配置
	cfg.Auth.Tokens.AccessToken.ExpiresIn = 1 * time.Hour
	cfg.Auth.Tokens.AccessToken.Issuer = "test"
	cfg.Auth.Tokens.AccessToken.Audience = []string{"test-api"}
	
	cfg.Auth.Tokens.RefreshToken.ExpiresIn = 168 * time.Hour
	cfg.Auth.Tokens.RefreshToken.Issuer = "test"
	cfg.Auth.Tokens.RefreshToken.Audience = []string{"test-auth"}
	
	// 刷新策略
	cfg.Auth.Refresh.RefreshWindow = 6 * time.Hour
	cfg.Auth.Refresh.MaxRefreshCount = 10
	cfg.Auth.Refresh.RateLimit.Enabled = false
	
	return cfg
}

func createTestUserInfo() *UserInfo {
	return &UserInfo{
		UserID:      1,
		Username:    "testuser",
		Email:       "<EMAIL>",
		DisplayName: "Test User",
		Roles:       []string{"user", "admin"},
		Groups:      []string{"developers"},
		Permissions: []string{"read", "write"},
	}
}

func createTestDeviceInfo() *DeviceInfo {
	return &DeviceInfo{
		DeviceID:   "test-device-123",
		DeviceType: "web",
		UserAgent:  "Mozilla/5.0 Test Browser",
		IPAddress:  "127.0.0.1",
	}
}

func TestJWTClaims(t *testing.T) {
	cfg := createTestConfig()
	userInfo := createTestUserInfo()
	deviceInfo := createTestDeviceInfo()
	
	// 创建模拟的Redis管理器（这里简化处理）
	// 在实际测试中，应该使用mock或者测试用的Redis实例
	
	// 测试JWT Claims的基本结构
	now := time.Now()
	claims := &JWTClaims{
		UserID:       userInfo.UserID,
		Username:     userInfo.Username,
		Email:        userInfo.Email,
		DisplayName:  userInfo.DisplayName,
		Roles:        userInfo.Roles,
		Groups:       userInfo.Groups,
		Permissions:  userInfo.Permissions,
		SessionID:    "test-session-123",
		LoginTime:    now.Unix(),
		LastActivity: now.Unix(),
		DeviceID:     deviceInfo.DeviceID,
		DeviceType:   deviceInfo.DeviceType,
		UserAgent:    deviceInfo.UserAgent,
		IPAddress:    deviceInfo.IPAddress,
		TokenType:    "access_token",
		TokenVersion: 1,
	}
	
	// 验证Claims结构
	assert.Equal(t, uint(1), claims.UserID)
	assert.Equal(t, "testuser", claims.Username)
	assert.Equal(t, "<EMAIL>", claims.Email)
	assert.Equal(t, []string{"user", "admin"}, claims.Roles)
	assert.Equal(t, "access_token", claims.TokenType)
}

func TestGenerateTokenID(t *testing.T) {
	// 测试Token ID生成
	id1 := generateTokenID()
	id2 := generateTokenID()
	
	assert.NotEmpty(t, id1)
	assert.NotEmpty(t, id2)
	assert.NotEqual(t, id1, id2) // 应该生成不同的ID
	
	// UUID格式验证（简单检查）
	assert.Len(t, id1, 36) // UUID标准长度
	assert.Contains(t, id1, "-")
}

func TestGenerateSessionID(t *testing.T) {
	// 测试Session ID生成
	id1 := generateSessionID()
	id2 := generateSessionID()
	
	assert.NotEmpty(t, id1)
	assert.NotEmpty(t, id2)
	assert.NotEqual(t, id1, id2) // 应该生成不同的ID
	
	// 十六进制字符串验证
	assert.Len(t, id1, 32) // 16字节的十六进制表示
}

func TestTokenPairStructure(t *testing.T) {
	tokenPair := &TokenPair{
		AccessToken:  "test-access-token",
		RefreshToken: "test-refresh-token",
		TokenType:    "Bearer",
		ExpiresIn:    3600,
	}
	
	assert.Equal(t, "test-access-token", tokenPair.AccessToken)
	assert.Equal(t, "test-refresh-token", tokenPair.RefreshToken)
	assert.Equal(t, "Bearer", tokenPair.TokenType)
	assert.Equal(t, int64(3600), tokenPair.ExpiresIn)
}

func TestUserInfoStructure(t *testing.T) {
	userInfo := createTestUserInfo()
	
	assert.Equal(t, uint(1), userInfo.UserID)
	assert.Equal(t, "testuser", userInfo.Username)
	assert.Equal(t, "<EMAIL>", userInfo.Email)
	assert.Equal(t, "Test User", userInfo.DisplayName)
	assert.Contains(t, userInfo.Roles, "user")
	assert.Contains(t, userInfo.Roles, "admin")
	assert.Contains(t, userInfo.Groups, "developers")
	assert.Contains(t, userInfo.Permissions, "read")
	assert.Contains(t, userInfo.Permissions, "write")
}

func TestDeviceInfoStructure(t *testing.T) {
	deviceInfo := createTestDeviceInfo()
	
	assert.Equal(t, "test-device-123", deviceInfo.DeviceID)
	assert.Equal(t, "web", deviceInfo.DeviceType)
	assert.Equal(t, "Mozilla/5.0 Test Browser", deviceInfo.UserAgent)
	assert.Equal(t, "127.0.0.1", deviceInfo.IPAddress)
}

// 注意：以下测试需要实际的Redis实例，在CI/CD环境中可能需要跳过
func TestJWTServiceIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}
	
	// 这里需要实际的Redis管理器实现
	// 在实际测试中，应该使用测试用的Redis实例或者mock
	t.Skip("JWT Service integration test requires Redis manager implementation")
}

func TestJWTConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		setupConfig func() *config.Config
		expectValid bool
	}{
		{
			name: "valid config",
			setupConfig: func() *config.Config {
				return createTestConfig()
			},
			expectValid: true,
		},
		{
			name: "empty jwt secret",
			setupConfig: func() *config.Config {
				cfg := createTestConfig()
				cfg.Auth.JWTSecret = ""
				return cfg
			},
			expectValid: false,
		},
		{
			name: "short jwt secret",
			setupConfig: func() *config.Config {
				cfg := createTestConfig()
				cfg.Auth.JWTSecret = "short"
				return cfg
			},
			expectValid: false,
		},
		{
			name: "zero expire time",
			setupConfig: func() *config.Config {
				cfg := createTestConfig()
				cfg.Auth.JWTExpire = 0
				return cfg
			},
			expectValid: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := tt.setupConfig()
			validator := &JWTConfigValidator{}
			err := validator.Validate(cfg)
			
			if tt.expectValid {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
			}
		})
	}
}
