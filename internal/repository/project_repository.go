package repository

import (
	"context"
	"fmt"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// projectRepository 项目仓储实现
type projectRepository struct {
	db *gorm.DB
}

// NewProjectRepository 创建项目仓储
func NewProjectRepository(db *gorm.DB) ProjectRepository {
	return &projectRepository{
		db: db,
	}
}

// ListProjectsPaginated 分页列出项目
func (r *projectRepository) ListProjectsPaginated(ctx context.Context, page, pageSize int) ([]*model.Project, int64, error) {
	var projects []*model.Project
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&model.Project{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页获取项目列表
	offset := (page - 1) * pageSize
	err := r.db.WithContext(ctx).Offset(offset).Limit(pageSize).Find(&projects).Error
	if err != nil {
		return nil, 0, err
	}

	return projects, total, nil
}

// ListProjectsByCluster 根据集群ID列出项目
func (r *projectRepository) ListProjectsByCluster(ctx context.Context, clusterID uint) ([]*model.Project, error) {
	var projects []*model.Project
	err := r.db.WithContext(ctx).
		Where("cluster_id = ?", clusterID).
		Preload("Cluster").
		Find(&projects).Error
	if err != nil {
		return nil, err
	}
	return projects, nil
}

// GetProjectByID 通过ID获取项目
func (r *projectRepository) GetProjectByID(ctx context.Context, id uint) (*model.Project, error) {
	var project model.Project
	err := r.db.WithContext(ctx).First(&project, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &project, nil
}

// GetProjectByName 通过名称获取项目
func (r *projectRepository) GetProjectByName(ctx context.Context, name string) (*model.Project, error) {
	var project model.Project
	err := r.db.WithContext(ctx).Where("name = ?", name).First(&project).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &project, nil
}

// CreateProject 创建项目
func (r *projectRepository) CreateProject(ctx context.Context, project *model.Project) error {
	return r.db.WithContext(ctx).Create(project).Error
}

// UpdateProject 更新项目
func (r *projectRepository) UpdateProject(ctx context.Context, project *model.Project) error {
	return r.db.WithContext(ctx).Save(project).Error
}

// DeleteProject 删除项目
func (r *projectRepository) DeleteProject(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Project{}, id).Error
}

// AddProjectMember 添加项目成员
func (r *projectRepository) AddProjectMember(ctx context.Context, projectID, userID uint, role string) error {
	member := &model.ProjectMember{
		ProjectID: projectID,
		UserID:    userID,
		Role:      role,
	}
	return r.db.WithContext(ctx).Create(member).Error
}

// RemoveProjectMember 移除项目成员
func (r *projectRepository) RemoveProjectMember(ctx context.Context, projectID, userID uint) error {
	return r.db.WithContext(ctx).
		Where("project_id = ? AND user_id = ?", projectID, userID).
		Delete(&model.ProjectMember{}).Error
}

// UpdateProjectMemberRole 更新项目成员角色
func (r *projectRepository) UpdateProjectMemberRole(ctx context.Context, projectID, userID uint, role string) error {
	return r.db.WithContext(ctx).
		Model(&model.ProjectMember{}).
		Where("project_id = ? AND user_id = ?", projectID, userID).
		Update("role", role).Error
}

// GetProjectMembers 获取项目成员
func (r *projectRepository) GetProjectMembers(ctx context.Context, projectID uint) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).
		Table("users").
		Joins("JOIN project_members ON users.id = project_members.user_id").
		Where("project_members.project_id = ?", projectID).
		Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// GetUserProjects 获取用户的项目列表
func (r *projectRepository) GetUserProjects(ctx context.Context, userID uint) ([]*model.Project, error) {
	var projects []*model.Project
	err := r.db.WithContext(ctx).
		Table("projects").
		Joins("JOIN project_members ON projects.id = project_members.project_id").
		Where("project_members.user_id = ?", userID).
		Preload("Cluster").
		Find(&projects).Error
	if err != nil {
		return nil, err
	}
	return projects, nil
}

// IsUserInProject 检查用户是否在项目中
func (r *projectRepository) IsUserInProject(ctx context.Context, userID, projectID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.ProjectMember{}).
		Where("user_id = ? AND project_id = ?", userID, projectID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// GetUserProjectRole 获取用户在项目中的角色
func (r *projectRepository) GetUserProjectRole(ctx context.Context, userID, projectID uint) (string, error) {
	var member model.ProjectMember
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND project_id = ?", userID, projectID).
		First(&member).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", fmt.Errorf("用户不在项目中")
		}
		return "", err
	}
	return member.Role, nil
}

// CreateProjectCluster 创建项目集群关联
func (r *projectRepository) CreateProjectCluster(ctx context.Context, projectCluster *model.ProjectCluster) error {
	return r.db.WithContext(ctx).Create(projectCluster).Error
}

// GetProjectClusters 获取项目的集群关联
func (r *projectRepository) GetProjectClusters(ctx context.Context, projectID uint) ([]*model.ProjectCluster, error) {
	var projectClusters []*model.ProjectCluster
	query := r.db.WithContext(ctx)
	if projectID > 0 {
		query = query.Where("project_id = ?", projectID)
	}
	err := query.Find(&projectClusters).Error
	return projectClusters, err
}

// UpdateProjectCluster 更新项目集群关联
func (r *projectRepository) UpdateProjectCluster(ctx context.Context, projectCluster *model.ProjectCluster) error {
	return r.db.WithContext(ctx).Save(projectCluster).Error
}

// GetProjectCluster 获取项目集群关联
func (r *projectRepository) GetProjectCluster(ctx context.Context, projectID, clusterID uint) (*model.ProjectCluster, error) {
	var projectCluster model.ProjectCluster
	err := r.db.WithContext(ctx).Where("project_id = ? AND cluster_id = ?", projectID, clusterID).First(&projectCluster).Error
	if err != nil {
		return nil, err
	}
	return &projectCluster, nil
}

// GetProjectMemberRole 获取项目成员角色
func (r *projectRepository) GetProjectMemberRole(ctx context.Context, projectID, userID uint) (string, error) {
	var member model.ProjectMember
	err := r.db.WithContext(ctx).Where("project_id = ? AND user_id = ?", projectID, userID).First(&member).Error
	if err != nil {
		return "", err
	}
	return member.Role, nil
}

// IsProjectMember 检查用户是否为项目成员
func (r *projectRepository) IsProjectMember(ctx context.Context, projectID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.ProjectMember{}).Where("project_id = ? AND user_id = ?", projectID, userID).Count(&count).Error
	return count > 0, err
}

// DeleteProjectCluster 删除项目集群关联
func (r *projectRepository) DeleteProjectCluster(ctx context.Context, projectID, clusterID uint) error {
	return r.db.WithContext(ctx).Where("project_id = ? AND cluster_id = ?", projectID, clusterID).Delete(&model.ProjectCluster{}).Error
}
