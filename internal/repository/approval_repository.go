package repository

import (
	"context"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// approvalRepository 审批仓储实现
type approvalRepository struct {
	db *gorm.DB
}

// NewApprovalRepository 创建审批仓储
func NewApprovalRepository(db *gorm.DB) ApprovalRepository {
	return &approvalRepository{
		db: db,
	}
}

// CreateApprovalFlow 创建审批流程
func (r *approvalRepository) CreateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error {
	return r.db.WithContext(ctx).Create(flow).Error
}

// GetApprovalFlow 获取审批流程
func (r *approvalRepository) GetApprovalFlow(ctx context.Context, id uint) (*model.ApprovalFlow, error) {
	var flow model.ApprovalFlow
	err := r.db.WithContext(ctx).First(&flow, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &flow, nil
}

// UpdateApprovalFlow 更新审批流程
func (r *approvalRepository) UpdateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error {
	return r.db.WithContext(ctx).Save(flow).Error
}

// ListApprovalFlows 获取所有审批流程
func (r *approvalRepository) ListApprovalFlows(ctx context.Context) ([]*model.ApprovalFlow, error) {
	var flows []*model.ApprovalFlow
	err := r.db.WithContext(ctx).Find(&flows).Error
	return flows, err
}

// DeleteApprovalFlow 删除审批流程
func (r *approvalRepository) DeleteApprovalFlow(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ApprovalFlow{}, id).Error
}

// CreateApprovalRequest 创建审批请求
func (r *approvalRepository) CreateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error {
	return r.db.WithContext(ctx).Create(request).Error
}

// GetApprovalRequest 获取审批请求
func (r *approvalRepository) GetApprovalRequest(ctx context.Context, id uint) (*model.ApprovalRequest, error) {
	var request model.ApprovalRequest
	err := r.db.WithContext(ctx).First(&request, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &request, nil
}

// UpdateApprovalRequest 更新审批请求
func (r *approvalRepository) UpdateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error {
	return r.db.WithContext(ctx).Save(request).Error
}

// ListApprovalRequests 获取审批请求列表
func (r *approvalRepository) ListApprovalRequests(ctx context.Context, page, pageSize int) ([]*model.ApprovalRequest, int64, error) {
	var requests []*model.ApprovalRequest
	var total int64

	err := r.db.WithContext(ctx).Model(&model.ApprovalRequest{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).Offset(offset).Limit(pageSize).Find(&requests).Error
	if err != nil {
		return nil, 0, err
	}

	return requests, total, nil
}

// GetPendingApprovals 获取待处理的审批请求
func (r *approvalRepository) GetPendingApprovals(ctx context.Context, userID uint) ([]*model.ApprovalRequest, error) {
	var requests []*model.ApprovalRequest
	err := r.db.WithContext(ctx).
		Where("status = ? AND approver_id = ?", "pending", userID).
		Find(&requests).Error
	if err != nil {
		return nil, err
	}
	return requests, nil
}

// GetUserApprovals 获取用户的审批请求
func (r *approvalRepository) GetUserApprovals(ctx context.Context, userID uint) ([]*model.ApprovalRequest, error) {
	var requests []*model.ApprovalRequest
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Find(&requests).Error
	if err != nil {
		return nil, err
	}
	return requests, nil
}

// UpdateApprovalRequestStep 更新审批步骤
func (r *approvalRepository) UpdateApprovalRequestStep(ctx context.Context, step *model.ApprovalRequestStep) error {
	return r.db.WithContext(ctx).Save(step).Error
}

// DeleteApprovalRequest 删除审批请求
func (r *approvalRepository) DeleteApprovalRequest(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.ApprovalRequest{}, id).Error
}
