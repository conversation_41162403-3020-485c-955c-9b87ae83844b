package repository

import (
	"context"

	"gorm.io/gorm"

	"kubeops/internal/model"
)

// applicationRepository 应用仓储实现
type applicationRepository struct {
	db *gorm.DB
}

// NewApplicationRepository 创建应用仓储实例
func NewApplicationRepository(db *gorm.DB) ApplicationRepository {
	return &applicationRepository{
		db: db,
	}
}

// CreateApplication 创建应用
func (r *applicationRepository) CreateApplication(ctx context.Context, application *model.Application) error {
	return r.db.WithContext(ctx).Create(application).Error
}

// GetApplicationByID 根据ID获取应用
func (r *applicationRepository) GetApplicationByID(ctx context.Context, id uint) (*model.Application, error) {
	var application model.Application
	err := r.db.WithContext(ctx).First(&application, id).Error
	if err != nil {
		return nil, err
	}
	return &application, nil
}

// GetApplicationByName 根据名称获取应用
func (r *applicationRepository) GetApplicationByName(ctx context.Context, projectID uint, name string) (*model.Application, error) {
	var application model.Application
	err := r.db.WithContext(ctx).Where("project_id = ? AND name = ?", projectID, name).First(&application).Error
	if err != nil {
		return nil, err
	}
	return &application, nil
}

// UpdateApplication 更新应用
func (r *applicationRepository) UpdateApplication(ctx context.Context, application *model.Application) error {
	return r.db.WithContext(ctx).Save(application).Error
}

// DeleteApplication 删除应用
func (r *applicationRepository) DeleteApplication(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Application{}, id).Error
}

// ListApplicationsPaginated 分页获取应用列表
func (r *applicationRepository) ListApplicationsPaginated(ctx context.Context, page, pageSize int, projectID uint) ([]*model.Application, int64, error) {
	var applications []*model.Application
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Application{})
	if projectID > 0 {
		query = query.Where("project_id = ?", projectID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&applications).Error
	if err != nil {
		return nil, 0, err
	}

	return applications, total, nil
}

// GetApplicationByWorkload 根据工作负载获取应用
func (r *applicationRepository) GetApplicationByWorkload(ctx context.Context, projectID uint, workloadType, workloadName string) (*model.Application, error) {
	var application model.Application
	err := r.db.WithContext(ctx).Where("project_id = ? AND workload_type = ? AND workload_name = ?", projectID, workloadType, workloadName).First(&application).Error
	if err != nil {
		return nil, err
	}
	return &application, nil
}

// BatchCreateApplications 批量创建应用
func (r *applicationRepository) BatchCreateApplications(ctx context.Context, applications []*model.Application) error {
	return r.db.WithContext(ctx).CreateInBatches(applications, 100).Error
}

// UpdateApplicationSyncStatus 更新应用同步状态
func (r *applicationRepository) UpdateApplicationSyncStatus(ctx context.Context, id uint, status model.ApplicationSyncStatus) error {
	return r.db.WithContext(ctx).Model(&model.Application{}).Where("id = ?", id).Update("sync_status", status).Error
}

// GetIncompleteApplications 获取待完善的应用
func (r *applicationRepository) GetIncompleteApplications(ctx context.Context, projectID uint) ([]*model.Application, error) {
	var applications []*model.Application
	query := r.db.WithContext(ctx).Where("sync_status = ?", model.ApplicationSyncStatusPending)
	if projectID > 0 {
		query = query.Where("project_id = ?", projectID)
	}
	err := query.Find(&applications).Error
	return applications, err
}

// CreateApplicationCluster 创建应用集群部署记录
func (r *applicationRepository) CreateApplicationCluster(ctx context.Context, applicationCluster *model.ApplicationCluster) error {
	return r.db.WithContext(ctx).Create(applicationCluster).Error
}

// GetApplicationCluster 获取应用集群部署记录
func (r *applicationRepository) GetApplicationCluster(ctx context.Context, applicationID, projectClusterID uint) (*model.ApplicationCluster, error) {
	var applicationCluster model.ApplicationCluster
	err := r.db.WithContext(ctx).Where("application_id = ? AND project_cluster_id = ?", applicationID, projectClusterID).First(&applicationCluster).Error
	if err != nil {
		return nil, err
	}
	return &applicationCluster, nil
}

// GetApplicationClusters 获取应用的集群部署记录
func (r *applicationRepository) GetApplicationClusters(ctx context.Context, applicationID uint) ([]*model.ApplicationCluster, error) {
	var applicationClusters []*model.ApplicationCluster
	err := r.db.WithContext(ctx).Where("application_id = ?", applicationID).Find(&applicationClusters).Error
	return applicationClusters, err
}

// GetClusterApplications 获取集群的应用部署记录
func (r *applicationRepository) GetClusterApplications(ctx context.Context, projectClusterID uint) ([]*model.ApplicationCluster, error) {
	var applicationClusters []*model.ApplicationCluster
	err := r.db.WithContext(ctx).Where("project_cluster_id = ?", projectClusterID).Find(&applicationClusters).Error
	return applicationClusters, err
}

// UpdateApplicationCluster 更新应用集群部署记录
func (r *applicationRepository) UpdateApplicationCluster(ctx context.Context, applicationCluster *model.ApplicationCluster) error {
	return r.db.WithContext(ctx).Save(applicationCluster).Error
}

// DeleteApplicationCluster 删除应用集群部署记录
func (r *applicationRepository) DeleteApplicationCluster(ctx context.Context, applicationID, projectClusterID uint) error {
	return r.db.WithContext(ctx).Where("application_id = ? AND project_cluster_id = ?", applicationID, projectClusterID).Delete(&model.ApplicationCluster{}).Error
}

// AddApplicationMember 添加应用成员
func (r *applicationRepository) AddApplicationMember(ctx context.Context, applicationID, userID uint, role string) error {
	member := &model.ApplicationMember{
		ApplicationID: applicationID,
		UserID:        userID,
		Role:          role,
	}
	return r.db.WithContext(ctx).Create(member).Error
}

// RemoveApplicationMember 移除应用成员
func (r *applicationRepository) RemoveApplicationMember(ctx context.Context, applicationID, userID uint) error {
	return r.db.WithContext(ctx).Where("application_id = ? AND user_id = ?", applicationID, userID).Delete(&model.ApplicationMember{}).Error
}

// UpdateApplicationMemberRole 更新应用成员角色
func (r *applicationRepository) UpdateApplicationMemberRole(ctx context.Context, applicationID, userID uint, role string) error {
	return r.db.WithContext(ctx).Model(&model.ApplicationMember{}).Where("application_id = ? AND user_id = ?", applicationID, userID).Update("role", role).Error
}

// GetApplicationMembers 获取应用成员
func (r *applicationRepository) GetApplicationMembers(ctx context.Context, applicationID uint) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).
		Table("users").
		Joins("JOIN application_members ON users.id = application_members.user_id").
		Where("application_members.application_id = ?", applicationID).
		Find(&users).Error
	return users, err
}

// GetUserApplications 获取用户的应用
func (r *applicationRepository) GetUserApplications(ctx context.Context, userID uint) ([]*model.Application, error) {
	var applications []*model.Application
	err := r.db.WithContext(ctx).
		Table("applications").
		Joins("JOIN application_members ON applications.id = application_members.application_id").
		Where("application_members.user_id = ?", userID).
		Find(&applications).Error
	return applications, err
}

// IsApplicationMember 检查用户是否为应用成员
func (r *applicationRepository) IsApplicationMember(ctx context.Context, applicationID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.ApplicationMember{}).Where("application_id = ? AND user_id = ?", applicationID, userID).Count(&count).Error
	return count > 0, err
}

// GetApplicationCountByProject 获取项目的应用数量
func (r *applicationRepository) GetApplicationCountByProject(ctx context.Context, projectID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.Application{}).Where("project_id = ?", projectID).Count(&count).Error
	return count, err
}

// GetApplicationCountByStatus 获取指定状态的应用数量
func (r *applicationRepository) GetApplicationCountByStatus(ctx context.Context, projectID uint, status model.ApplicationStatus) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.Application{}).Where("status = ?", status)
	if projectID > 0 {
		query = query.Where("project_id = ?", projectID)
	}
	err := query.Count(&count).Error
	return count, err
}

// GetApplicationsByOwner 获取用户拥有的应用
func (r *applicationRepository) GetApplicationsByOwner(ctx context.Context, ownerID uint) ([]*model.Application, error) {
	var applications []*model.Application
	err := r.db.WithContext(ctx).Where("owner_id = ?", ownerID).Find(&applications).Error
	return applications, err
}
