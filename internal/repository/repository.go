package repository

import (
	"errors"

	"github.com/casbin/casbin/v2"
	"gorm.io/gorm"
)

// ErrNotFound 表示未找到记录
var ErrNotFound = errors.New("record not found")

// ErrNotImplemented 表示方法未实现
var ErrNotImplemented = errors.New("method not implemented")



// repository 仓储层实现
type repository struct {
	db               *gorm.DB
	userRepo         UserRepository
	auditRepo        AuditRepository
	clusterRepo      ClusterRepository
	projectRepo      ProjectRepository
	applicationRepo  ApplicationRepository
	systemConfigRepo SystemConfigRepository
	userGroupRepo    UserGroupRepository
	rbacRepo         RBACRepository
	approvalRepo     ApprovalRepository
}

// NewRepository 创建仓储层实例
func NewRepository(db *gorm.DB) Repository {
	repo := &repository{
		db: db,
	}

	// 初始化各子仓储
	repo.userRepo = NewUserRepository(db)
	repo.auditRepo = NewAuditRepository(db)
	repo.clusterRepo = NewClusterRepository(db)
	repo.projectRepo = NewProjectRepository(db)
	repo.applicationRepo = NewApplicationRepository(db)
	repo.systemConfigRepo = NewSystemConfigRepository(db)
	repo.userGroupRepo = NewUserGroupRepository(db)
	repo.rbacRepo = NewRBACRepository(db, nil) // 初始化时没有enforcer
	repo.approvalRepo = NewApprovalRepository(db)

	return repo
}

// UpdateRBACRepository 更新RBAC仓库，添加enforcer
func (r *repository) UpdateRBACRepository(enforcer *casbin.SyncedEnforcer) {
	if enforcer != nil {
		r.rbacRepo = NewRBACRepository(r.db, enforcer)
	}
}

// GetDB 获取数据库连接
func (r *repository) GetDB() (*gorm.DB, error) {
	return r.db, nil
}

// Close 关闭数据库连接
func (r *repository) Close() error {
	sqlDB, err := r.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// User 获取用户仓储接口
func (r *repository) User() UserRepository {
	return r.userRepo
}

// Audit 获取审计仓储接口
func (r *repository) Audit() AuditRepository {
	return r.auditRepo
}

// Cluster 获取集群仓储接口
func (r *repository) Cluster() ClusterRepository {
	return r.clusterRepo
}

// Project 获取项目仓储接口
func (r *repository) Project() ProjectRepository {
	return r.projectRepo
}

// Application 获取应用仓储接口
func (r *repository) Application() ApplicationRepository {
	return r.applicationRepo
}

// SystemConfig 获取系统配置仓储接口
func (r *repository) SystemConfig() SystemConfigRepository {
	return r.systemConfigRepo
}

// UserGroup 获取用户组仓储接口
func (r *repository) UserGroup() UserGroupRepository {
	return r.userGroupRepo
}

// RBAC 获取RBAC仓储接口
func (r *repository) RBAC() RBACRepository {
	return r.rbacRepo
}

// Approval 获取审批仓储接口
func (r *repository) Approval() ApprovalRepository {
	return r.approvalRepo
}
