package repository

import (
	"context"
	"encoding/json"
	"time"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// 实现审计相关的仓储方法

// ListAuditLogs 查询审计日志列表
func (r *repository) ListAuditLogs(ctx context.Context, filter *model.AuditQueryFilter) ([]*model.AuditLog, int64, error) {
	db := r.db.WithContext(ctx)
	var logs []*model.AuditLog
	var total int64

	// 构建查询条件
	query := db.Model(&model.AuditLog{})

	// 应用过滤条件
	if filter != nil {
		if filter.UserID != nil && *filter.UserID > 0 {
			query = query.Where("user_id = ?", *filter.UserID)
		}
		if filter.Username != "" {
			query = query.Where("username LIKE ?", "%"+filter.Username+"%")
		}
		if filter.Action != "" {
			query = query.Where("action = ?", filter.Action)
		}
		if filter.ResourceType != "" {
			query = query.Where("resource_type = ?", filter.ResourceType)
		}
		if filter.Result != "" {
			query = query.Where("result = ?", filter.Result)
		}
		if filter.StartTime != nil {
			query = query.Where("created_at >= ?", filter.StartTime)
		}
		if filter.EndTime != nil {
			query = query.Where("created_at <= ?", filter.EndTime)
		}
		if filter.IP != "" {
			query = query.Where("ip LIKE ?", "%"+filter.IP+"%")
		}
		if filter.Department != "" {
			query = query.Where("department LIKE ?", "%"+filter.Department+"%")
		}
		if filter.UserType != "" {
			query = query.Where("user_type = ?", filter.UserType)
		}
		if filter.Role != "" {
			query = query.Where("role LIKE ?", "%"+filter.Role+"%")
		}
		if filter.ResourceID != "" {
			query = query.Where("resource_id LIKE ?", "%"+filter.ResourceID+"%")
		}
		if filter.TraceID != "" {
			query = query.Where("trace_id = ?", filter.TraceID)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页和排序
	if filter != nil && filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 默认按时间降序排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// ExportAuditLogs 导出审计日志
func (r *repository) ExportAuditLogs(ctx context.Context, filter *model.AuditQueryFilter, format string) ([]byte, error) {
	// 查询符合条件的审计日志
	logs, _, err := r.ListAuditLogs(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 实际项目中应该根据format将logs转换为相应格式的数据
	// 这里简单实现JSON格式导出
	if format == "json" {
		return json.Marshal(logs)
	}

	// 其他格式（如CSV）可以在这里实现
	// 暂时返回空数据
	return []byte{}, nil
}

// ListAuditArchives 获取审计归档列表
func (r *repository) ListAuditArchives(ctx context.Context, page, size int) ([]*model.AuditArchive, int64, error) {
	db := r.db.WithContext(ctx)
	var archives []*model.AuditArchive
	var total int64

	// 获取总数
	if err := db.Model(&model.AuditArchive{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页和排序
	query := db.Model(&model.AuditArchive{})
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		query = query.Offset(offset).Limit(size)
	}

	// 默认按创建时间降序排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&archives).Error; err != nil {
		return nil, 0, err
	}

	return archives, total, nil
}

// GetAuditArchive 获取审计归档详情
func (r *repository) GetAuditArchive(ctx context.Context, id uint) (*model.AuditArchive, error) {
	var archive model.AuditArchive
	err := r.db.WithContext(ctx).First(&archive, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &archive, nil
}

// CreateAuditArchive 创建审计归档
func (r *repository) CreateAuditArchive(ctx context.Context, archive *model.AuditArchive) error {
	return r.db.WithContext(ctx).Create(archive).Error
}

// UpdateAuditArchive 更新审计归档
func (r *repository) UpdateAuditArchive(ctx context.Context, archive *model.AuditArchive) error {
	return r.db.WithContext(ctx).Save(archive).Error
}

// DeleteAuditArchive 删除审计归档
func (r *repository) DeleteAuditArchive(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.AuditArchive{}, id).Error
}

// GetAuditArchiveConfig 获取审计归档配置
func (r *repository) GetAuditArchiveConfig(ctx context.Context) (*model.AuditArchiveConfig, error) {
	var config model.AuditArchiveConfig
	err := r.db.WithContext(ctx).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认配置
			return &model.AuditArchiveConfig{
				RetentionDays:     90,
				ArchiveInterval:   "quarterly",
				EncryptionEnabled: false,
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
			}, nil
		}
		return nil, err
	}
	return &config, nil
}

// UpdateAuditArchiveConfig 更新审计归档配置
func (r *repository) UpdateAuditArchiveConfig(ctx context.Context, config *model.AuditArchiveConfig) error {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.AuditArchiveConfig{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		// 创建新配置
		return r.db.WithContext(ctx).Create(config).Error
	}

	// 更新现有配置
	return r.db.WithContext(ctx).Model(&model.AuditArchiveConfig{}).Updates(config).Error
}

// DeleteAuditLogs 删除审计日志
func (r *repository) DeleteAuditLogs(ctx context.Context, before time.Time) error {
	return r.db.WithContext(ctx).Where("created_at < ?", before).Delete(&model.AuditLog{}).Error
}

// CleanupExpiredAuditLogs 清理过期的审计日志
func (r *repository) CleanupExpiredAuditLogs(ctx context.Context, retentionDays int) error {
	if retentionDays <= 0 {
		// 如果配置了0或负数，表示不清理
		return nil
	}

	// 计算过期时间点
	expireTime := time.Now().AddDate(0, 0, -retentionDays)

	// 执行删除操作
	return r.DeleteAuditLogs(ctx, expireTime)
}
