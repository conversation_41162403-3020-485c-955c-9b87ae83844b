package repository

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"kubeops/internal/model"
)

// userGroupRepository 用户组仓储实现
type userGroupRepository struct {
	db *gorm.DB
}

// NewUserGroupRepository 创建用户组仓储
func NewUserGroupRepository(db *gorm.DB) UserGroupRepository {
	return &userGroupRepository{
		db: db,
	}
}

// CreateUserGroup 创建用户组
func (r *userGroupRepository) CreateUserGroup(ctx context.Context, group *model.UserGroup) error {
	return r.db.WithContext(ctx).Create(group).Error
}

// GetUserGroupByID 获取用户组
func (r *userGroupRepository) GetUserGroupByID(ctx context.Context, id uint) (*model.UserGroup, error) {
	var group model.UserGroup
	err := r.db.WithContext(ctx).First(&group, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &group, nil
}

// GetUserGroupByName 根据名称获取用户组
func (r *userGroupRepository) GetUserGroupByName(ctx context.Context, name string) (*model.UserGroup, error) {
	var group model.UserGroup
	err := r.db.WithContext(ctx).Where("name = ?", name).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &group, nil
}

// UpdateUserGroup 更新用户组
func (r *userGroupRepository) UpdateUserGroup(ctx context.Context, group *model.UserGroup) error {
	return r.db.WithContext(ctx).Save(group).Error
}

// DeleteUserGroup 删除用户组
func (r *userGroupRepository) DeleteUserGroup(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.UserGroup{}, id).Error
}

// ListUserGroups 获取用户组列表
func (r *userGroupRepository) ListUserGroups(ctx context.Context) ([]*model.UserGroup, int64, error) {
	var groups []*model.UserGroup
	var total int64
	err := r.db.WithContext(ctx).Model(&model.UserGroup{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	err = r.db.WithContext(ctx).Find(&groups).Error
	if err != nil {
		return nil, 0, err
	}
	return groups, total, nil
}

// AddUserToGroup 添加用户到用户组
func (r *userGroupRepository) AddUserToGroup(ctx context.Context, userID uint, groupID uint) error {
	return r.db.WithContext(ctx).Exec("INSERT INTO user_group_members (user_id, group_id) VALUES (?, ?)", userID, groupID).Error
}

// RemoveUserFromGroup 从用户组中移除用户
func (r *userGroupRepository) RemoveUserFromGroup(ctx context.Context, userID uint, groupID uint) error {
	return r.db.WithContext(ctx).Exec("DELETE FROM user_group_members WHERE user_id = ? AND group_id = ?", userID, groupID).Error
}

// GetUserGroups 获取用户所属的用户组
func (r *userGroupRepository) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	var groups []*model.UserGroup
	err := r.db.WithContext(ctx).
		Joins("JOIN user_group_members ON user_groups.id = user_group_members.group_id").
		Where("user_group_members.user_id = ?", userID).
		Find(&groups).Error
	if err != nil {
		return nil, err
	}
	return groups, nil
}

// CountUserGroups 统计用户组数量
func (r *userGroupRepository) CountUserGroups(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.UserGroup{}).Count(&count).Error
	return count, err
}









// GetGroupUsers 获取用户组的用户
func (r *userGroupRepository) GetGroupUsers(ctx context.Context, groupID uint) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).
		Table("users").
		Joins("JOIN user_group_members ON users.id = user_group_members.user_id").
		Where("user_group_members.group_id = ?", groupID).
		Find(&users).Error
	return users, err
}

// SetGroupUsers 设置用户组的用户
func (r *userGroupRepository) SetGroupUsers(ctx context.Context, groupID uint, userIDs []uint) error {
	// 先删除现有成员
	if err := r.db.WithContext(ctx).Where("group_id = ?", groupID).Delete(&model.UserGroupMember{}).Error; err != nil {
		return err
	}

	// 批量添加新成员
	var members []*model.UserGroupMember
	for _, userID := range userIDs {
		members = append(members, &model.UserGroupMember{
			UserID:  userID,
			GroupID: groupID,
		})
	}

	if len(members) > 0 {
		return r.db.WithContext(ctx).CreateInBatches(members, 100).Error
	}

	return nil
}



// GetUserGroup 获取用户组（别名方法）
func (r *userGroupRepository) GetUserGroup(ctx context.Context, id uint) (*model.UserGroup, error) {
	return r.GetUserGroupByID(ctx, id)
}





// IsUserInGroup 检查用户是否在用户组中
func (r *userGroupRepository) IsUserInGroup(ctx context.Context, userID, groupID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.UserGroupMember{}).
		Where("user_id = ? AND group_id = ?", userID, groupID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}



// GetUserGroupByExternalID 根据外部ID获取用户组
func (r *userGroupRepository) GetUserGroupByExternalID(ctx context.Context, externalID string) (*model.UserGroup, error) {
	var group model.UserGroup
	err := r.db.WithContext(ctx).Where("external_id = ?", externalID).First(&group).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// ListUserGroupsPaginated 分页获取用户组列表
func (r *userGroupRepository) ListUserGroupsPaginated(ctx context.Context, page, pageSize int) ([]*model.UserGroup, int64, error) {
	var groups []*model.UserGroup
	var total int64

	// 计算总数
	err := r.db.WithContext(ctx).Model(&model.UserGroup{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err = r.db.WithContext(ctx).
		Offset(offset).
		Limit(pageSize).
		Find(&groups).Error
	if err != nil {
		return nil, 0, err
	}

	return groups, total, nil
}

// AddUsersToGroup 批量添加用户到用户组
func (r *userGroupRepository) AddUsersToGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, userID := range userIDs {
			// 检查是否已存在
			var count int64
			err := tx.WithContext(ctx).
				Model(&model.UserGroupMember{}).
				Where("user_id = ? AND group_id = ?", userID, groupID).
				Count(&count).Error
			if err != nil {
				return err
			}

			// 如果不存在则添加
			if count == 0 {
				err = tx.WithContext(ctx).
					Exec("INSERT INTO user_group_members (user_id, group_id) VALUES (?, ?)", userID, groupID).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// RemoveUsersFromGroup 批量从用户组移除用户
func (r *userGroupRepository) RemoveUsersFromGroup(ctx context.Context, groupID uint, userIDs []uint) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Exec("DELETE FROM user_group_members WHERE group_id = ? AND user_id IN (?)", groupID, userIDs).Error
}

// SetGroupMembers 设置用户组成员
func (r *userGroupRepository) SetGroupMembers(ctx context.Context, groupID uint, userIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除原有关联
		if err := tx.WithContext(ctx).Exec("DELETE FROM user_group_members WHERE group_id = ?", groupID).Error; err != nil {
			return err
		}

		// 添加新关联
		for _, userID := range userIDs {
			if err := tx.WithContext(ctx).Exec("INSERT INTO user_group_members (user_id, group_id) VALUES (?, ?)", userID, groupID).Error; err != nil {
				return err
			}
		}

		return nil
	})
}

// GetGroupMembers 获取用户组成员（别名方法）
func (r *userGroupRepository) GetGroupMembers(ctx context.Context, groupID uint) ([]*model.User, error) {
	return r.GetGroupUsers(ctx, groupID)
}

// GetKeycloakGroupMappings 获取所有Keycloak组映射
func (r *userGroupRepository) GetKeycloakGroupMappings(ctx context.Context) ([]*model.KeycloakGroupMapping, error) {
	var mappings []*model.KeycloakGroupMapping
	err := r.db.WithContext(ctx).Find(&mappings).Error
	if err != nil {
		return nil, err
	}
	return mappings, nil
}

// SaveKeycloakGroupMapping 保存Keycloak组映射
func (r *userGroupRepository) SaveKeycloakGroupMapping(ctx context.Context, mapping *model.KeycloakGroupMapping) error {
	// 使用Upsert逻辑
	return r.db.WithContext(ctx).
		Where("keycloak_group = ?", mapping.KeycloakGroup).
		Assign(model.KeycloakGroupMapping{
			UserGroupID: mapping.UserGroupID,
			Description: mapping.Description,
		}).
		FirstOrCreate(mapping).Error
}

// DeleteKeycloakGroupMapping 删除Keycloak组映射
func (r *userGroupRepository) DeleteKeycloakGroupMapping(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Delete(&model.KeycloakGroupMapping{}, id).Error
}

// GetKeycloakGroupMapping 获取指定Keycloak组的映射
func (r *userGroupRepository) GetKeycloakGroupMapping(ctx context.Context, keycloakGroup string) (*model.KeycloakGroupMapping, error) {
	var mapping model.KeycloakGroupMapping
	err := r.db.WithContext(ctx).
		Where("keycloak_group = ?", keycloakGroup).
		First(&mapping).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &mapping, nil
}

// GetGroupResourcePermissions 获取用户组资源权限
func (r *userGroupRepository) GetGroupResourcePermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	var permissions []*model.ResourcePermission
	err := r.db.WithContext(ctx).
		Joins("JOIN user_group_resource_permissions ON resource_permissions.id = user_group_resource_permissions.resource_permission_id").
		Where("user_group_resource_permissions.group_id = ?", groupID).
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

// AssignPermissionsToGroup 为用户组分配权限
func (r *userGroupRepository) AssignPermissionsToGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, permissionID := range permissionIDs {
			// 检查是否已存在
			var count int64
			err := tx.WithContext(ctx).
				Model(&model.GroupResourcePermission{}).
				Where("group_id = ? AND resource_permission_id = ?", groupID, permissionID).
				Count(&count).Error
			if err != nil {
				return err
			}

			// 如果不存在则添加
			if count == 0 {
				err = tx.WithContext(ctx).
					Exec("INSERT INTO group_resource_permissions (group_id, resource_permission_id) VALUES (?, ?)", groupID, permissionID).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// RemovePermissionsFromGroup 从用户组移除权限
func (r *userGroupRepository) RemovePermissionsFromGroup(ctx context.Context, groupID uint, permissionIDs []uint) error {
	if len(permissionIDs) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).
		Exec("DELETE FROM group_resource_permissions WHERE group_id = ? AND resource_permission_id IN (?)", groupID, permissionIDs).Error
}

// GetGroupPermissions 获取用户组权限
func (r *userGroupRepository) GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error) {
	var permissions []*model.ResourcePermission
	err := r.db.WithContext(ctx).
		Joins("JOIN group_resource_permissions ON resource_permissions.id = group_resource_permissions.resource_permission_id").
		Where("group_resource_permissions.group_id = ?", groupID).
		Find(&permissions).Error
	if err != nil {
		return nil, err
	}
	return permissions, nil
}

// SetGroupResourcePermissions 设置用户组资源权限
func (r *userGroupRepository) SetGroupResourcePermissions(ctx context.Context, groupID uint, resourcePermissionIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除原有关联
		if err := tx.WithContext(ctx).Exec("DELETE FROM group_resource_permissions WHERE group_id = ?", groupID).Error; err != nil {
			return err
		}

		// 添加新关联
		for _, permID := range resourcePermissionIDs {
			if err := tx.WithContext(ctx).Exec(
				"INSERT INTO group_resource_permissions (group_id, resource_permission_id) VALUES (?, ?)",
				groupID, permID).Error; err != nil {
				return err
			}
		}

		return nil
	})
}
