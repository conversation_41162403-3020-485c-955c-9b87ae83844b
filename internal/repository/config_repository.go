package repository

import (
	"context"

	"gorm.io/gorm"

	"kubeops/internal/model"
)

// ConfigRepository 配置仓储接口
type ConfigRepository interface {
	// 基本CRUD操作
	Create(ctx context.Context, config *model.Config) error
	GetByID(ctx context.Context, id uint) (*model.Config, error)
	GetByKey(ctx context.Context, key string) (*model.Config, error)
	GetByType(ctx context.Context, configType string) ([]*model.Config, error)
	Update(ctx context.Context, config *model.Config) error
	Delete(ctx context.Context, id uint) error
}

// configRepository 配置仓储实现
type configRepository struct {
	db *gorm.DB
}

// NewConfigRepository 创建配置仓储
func NewConfigRepository(db *gorm.DB) ConfigRepository {
	return &configRepository{
		db: db,
	}
}

// Create 创建配置
func (r *configRepository) Create(ctx context.Context, config *model.Config) error {
	return r.db.WithContext(ctx).Create(config).Error
}

// GetByID 根据ID获取配置
func (r *configRepository) GetByID(ctx context.Context, id uint) (*model.Config, error) {
	var config model.Config
	err := r.db.WithContext(ctx).First(&config, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &config, nil
}

// GetByKey 根据Key获取配置
func (r *configRepository) GetByKey(ctx context.Context, key string) (*model.Config, error) {
	var config model.Config
	err := r.db.WithContext(ctx).Where("key = ?", key).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &config, nil
}

// GetByType 根据类型获取配置
func (r *configRepository) GetByType(ctx context.Context, configType string) ([]*model.Config, error) {
	var configs []*model.Config
	err := r.db.WithContext(ctx).Where("type = ?", configType).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}

// Update 更新配置
func (r *configRepository) Update(ctx context.Context, config *model.Config) error {
	return r.db.WithContext(ctx).Save(config).Error
}

// Delete 删除配置
func (r *configRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Config{}, id).Error
}
