package repository

import (
	"context"
	"time"

	"kubeops/internal/model"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	// 基础用户操作
	ListUsers(ctx context.Context) ([]*model.User, error)
	ListUsersPaginated(ctx context.Context, page, size int) ([]*model.User, int64, error)
	GetUserByID(ctx context.Context, id uint) (*model.User, error)
	GetUserByUsername(ctx context.Context, username string) (*model.User, error)
	GetUserByEmail(ctx context.Context, email string) (*model.User, error)
	GetUserByOIDCSubject(ctx context.Context, subject string) (*model.User, error)
	CreateUser(ctx context.Context, user *model.User) error
	UpdateUser(ctx context.Context, user *model.User) error
	DeleteUser(ctx context.Context, id uint) error
	CountUsers(ctx context.Context) (int64, error)

	// 用户状态管理
	UpdateUserStatus(ctx context.Context, userID uint, status model.UserStatus) error
	UpdateLastLogin(ctx context.Context, userID uint, lastLogin *time.Time) error

	// 飞书集成
	GetUserByFeishuID(ctx context.Context, feishuOpenID, feishuUnionID string) (*model.User, error)

	// 用户组关联
	GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error)
	AddUserToGroups(ctx context.Context, userID uint, groupIDs []uint) error
	RemoveUserFromGroups(ctx context.Context, userID uint, groupIDs []uint) error
	SyncUserGroups(ctx context.Context, userID uint, groupIDs []uint) error
}

// AuditRepository 审计仓储接口
type AuditRepository interface {
	// 审计相关
	CreateAuditLog(ctx context.Context, log *model.AuditLog) error
	ListAuditLogs(ctx context.Context, filter *model.AuditQueryFilter) ([]*model.AuditLog, int64, error)
	GetAuditLog(ctx context.Context, id uint) (*model.AuditLog, error)
	ArchiveAuditLogs(ctx context.Context, before string, status string) (int64, error)
	ExportAuditLogs(ctx context.Context, filter *model.AuditQueryFilter, format string) ([]byte, error)
	DeleteAuditLogs(ctx context.Context, before time.Time) error
	CleanupExpiredAuditLogs(ctx context.Context, retentionDays int) error
	ListAuditArchives(ctx context.Context, page, size int) ([]*model.AuditArchive, int64, error)
	GetAuditArchive(ctx context.Context, id uint) (*model.AuditArchive, error)
	CreateAuditArchive(ctx context.Context, archive *model.AuditArchive) error
	UpdateAuditArchive(ctx context.Context, archive *model.AuditArchive) error
	DeleteAuditArchive(ctx context.Context, id uint) error
	GetAuditArchiveConfig(ctx context.Context) (*model.AuditArchiveConfig, error)
	UpdateAuditArchiveConfig(ctx context.Context, config *model.AuditArchiveConfig) error
}

// ClusterRepository 集群仓储接口
type ClusterRepository interface {
	// 基础CRUD操作
	CreateCluster(ctx context.Context, cluster *model.Cluster) error
	GetClusterByID(ctx context.Context, id uint) (*model.Cluster, error)
	GetClusterByName(ctx context.Context, name string) (*model.Cluster, error)
	UpdateCluster(ctx context.Context, cluster *model.Cluster) error
	DeleteCluster(ctx context.Context, id uint) error
	ListClustersPaginated(ctx context.Context, page, pageSize int) ([]*model.Cluster, int64, error)

	// 集群状态管理
	UpdateClusterStatus(ctx context.Context, clusterID uint, status model.ClusterStatus) error
	GetActiveClusterIDs(ctx context.Context) ([]uint, error)

	// 集群配置管理
	UpdateClusterConfig(ctx context.Context, clusterID uint, config map[string]interface{}) error
	GetClusterConfig(ctx context.Context, clusterID uint) (map[string]interface{}, error)
}

// ProjectRepository 项目仓储接口
type ProjectRepository interface {
	// 基础CRUD操作
	CreateProject(ctx context.Context, project *model.Project) error
	GetProjectByID(ctx context.Context, id uint) (*model.Project, error)
	GetProjectByName(ctx context.Context, name string) (*model.Project, error)
	UpdateProject(ctx context.Context, project *model.Project) error
	DeleteProject(ctx context.Context, id uint) error
	ListProjectsPaginated(ctx context.Context, page, pageSize int) ([]*model.Project, int64, error)

	// 多集群部署支持
	CreateProjectCluster(ctx context.Context, projectCluster *model.ProjectCluster) error
	GetProjectCluster(ctx context.Context, projectID, clusterID uint) (*model.ProjectCluster, error)
	GetProjectClusters(ctx context.Context, projectID uint) ([]*model.ProjectCluster, error)
	UpdateProjectCluster(ctx context.Context, projectCluster *model.ProjectCluster) error
	DeleteProjectCluster(ctx context.Context, projectID, clusterID uint) error

	// 项目成员关系管理（仅用于界面展示）
	AddProjectMember(ctx context.Context, projectID, userID uint, role string) error
	RemoveProjectMember(ctx context.Context, projectID, userID uint) error
	UpdateProjectMemberRole(ctx context.Context, projectID, userID uint, role string) error
	GetProjectMembers(ctx context.Context, projectID uint) ([]*model.User, error)
	GetUserProjects(ctx context.Context, userID uint) ([]*model.Project, error)
	IsProjectMember(ctx context.Context, projectID, userID uint) (bool, error)
	GetProjectMemberRole(ctx context.Context, projectID, userID uint) (string, error)
}

// SystemConfigRepository 系统配置仓储接口
type SystemConfigRepository interface {
	// 系统配置相关
	GetSystemConfig(ctx context.Context, key string) (*model.SystemConfig, error)
	SetSystemConfig(ctx context.Context, key string, value string) error
	DeleteSystemConfig(ctx context.Context, key string) error
	ListSystemConfigs(ctx context.Context) ([]*model.SystemConfig, error)
	UpdateSystemConfig(ctx context.Context, config *model.SystemConfig) error

	// 飞书配置
	GetFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error)
	SetFeishuConfig(ctx context.Context, config *model.SystemFeishuConfig) error

	// OIDC配置
	GetOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error)
	SetOIDCConfig(ctx context.Context, config *model.SystemOIDCConfig) error

	// OBS配置
	GetOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error)
	SetOBSConfig(ctx context.Context, config *model.SystemOBSConfig) error

	// 审计配置
	GetAuditConfig(ctx context.Context) (*model.SystemAuditConfig, error)
	SetAuditConfig(ctx context.Context, config *model.SystemAuditConfig) error

	// 基本配置
	GetBasicConfig(ctx context.Context) (*model.SystemBasicConfig, error)
	SetBasicConfig(ctx context.Context, config *model.SystemBasicConfig) error
}

// UserGroupRepository 用户组仓储接口
type UserGroupRepository interface {
	// 基础CRUD操作
	CreateUserGroup(ctx context.Context, group *model.UserGroup) error
	GetUserGroupByID(ctx context.Context, id uint) (*model.UserGroup, error)
	GetUserGroupByName(ctx context.Context, name string) (*model.UserGroup, error)
	GetUserGroupByExternalID(ctx context.Context, externalID string) (*model.UserGroup, error)
	UpdateUserGroup(ctx context.Context, group *model.UserGroup) error
	DeleteUserGroup(ctx context.Context, id uint) error
	ListUserGroupsPaginated(ctx context.Context, page, pageSize int) ([]*model.UserGroup, int64, error)

	// 成员关系管理
	AddUsersToGroup(ctx context.Context, groupID uint, userIDs []uint) error
	AddUserToGroup(ctx context.Context, userID, groupID uint) error
	RemoveUserFromGroup(ctx context.Context, userID, groupID uint) error
	RemoveUsersFromGroup(ctx context.Context, groupID uint, userIDs []uint) error
	SetGroupMembers(ctx context.Context, groupID uint, userIDs []uint) error
	SetGroupUsers(ctx context.Context, groupID uint, userIDs []uint) error
	GetGroupMembers(ctx context.Context, groupID uint) ([]*model.User, error)
	GetGroupUsers(ctx context.Context, groupID uint) ([]*model.User, error)
	GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error)
	IsUserInGroup(ctx context.Context, userID, groupID uint) (bool, error)

	// 权限关联
	AssignPermissionsToGroup(ctx context.Context, groupID uint, permissionIDs []uint) error
	RemovePermissionsFromGroup(ctx context.Context, groupID uint, permissionIDs []uint) error
	GetGroupPermissions(ctx context.Context, groupID uint) ([]*model.ResourcePermission, error)

	// Keycloak映射相关
	GetKeycloakGroupMappings(ctx context.Context) ([]*model.KeycloakGroupMapping, error)
	SaveKeycloakGroupMapping(ctx context.Context, mapping *model.KeycloakGroupMapping) error
	DeleteKeycloakGroupMapping(ctx context.Context, id uint) error
}

// RBACRepository RBAC统一仓储接口
// 包含权限管理、用户组权限、用户权限、RBAC模型等所有权限相关功能
// 注意：这个接口定义在 rbac_repository.go 中，这里只是为了保持接口的可见性

// ApplicationRepository 应用仓储接口
type ApplicationRepository interface {
	// 基础CRUD操作
	CreateApplication(ctx context.Context, application *model.Application) error
	GetApplicationByID(ctx context.Context, id uint) (*model.Application, error)
	GetApplicationByName(ctx context.Context, projectID uint, name string) (*model.Application, error)
	UpdateApplication(ctx context.Context, application *model.Application) error
	DeleteApplication(ctx context.Context, id uint) error
	ListApplicationsPaginated(ctx context.Context, page, pageSize int, projectID uint) ([]*model.Application, int64, error)

	// 应用发现和同步
	GetApplicationByWorkload(ctx context.Context, projectID uint, workloadType, workloadName string) (*model.Application, error)
	BatchCreateApplications(ctx context.Context, applications []*model.Application) error
	UpdateApplicationSyncStatus(ctx context.Context, id uint, status model.ApplicationSyncStatus) error
	GetIncompleteApplications(ctx context.Context, projectID uint) ([]*model.Application, error)

	// 多集群部署管理
	CreateApplicationCluster(ctx context.Context, applicationCluster *model.ApplicationCluster) error
	GetApplicationCluster(ctx context.Context, applicationID, projectClusterID uint) (*model.ApplicationCluster, error)
	GetApplicationClusters(ctx context.Context, applicationID uint) ([]*model.ApplicationCluster, error)
	GetClusterApplications(ctx context.Context, projectClusterID uint) ([]*model.ApplicationCluster, error)
	UpdateApplicationCluster(ctx context.Context, applicationCluster *model.ApplicationCluster) error
	DeleteApplicationCluster(ctx context.Context, applicationID, projectClusterID uint) error

	// 应用成员管理
	AddApplicationMember(ctx context.Context, applicationID, userID uint, role string) error
	RemoveApplicationMember(ctx context.Context, applicationID, userID uint) error
	UpdateApplicationMemberRole(ctx context.Context, applicationID, userID uint, role string) error
	GetApplicationMembers(ctx context.Context, applicationID uint) ([]*model.User, error)
	GetUserApplications(ctx context.Context, userID uint) ([]*model.Application, error)
	IsApplicationMember(ctx context.Context, applicationID, userID uint) (bool, error)

	// 应用统计
	GetApplicationCountByProject(ctx context.Context, projectID uint) (int64, error)
	GetApplicationCountByStatus(ctx context.Context, projectID uint, status model.ApplicationStatus) (int64, error)
	GetApplicationsByOwner(ctx context.Context, ownerID uint) ([]*model.Application, error)
}

// Repository 仓储接口聚合
type Repository interface {
	User() UserRepository
	Audit() AuditRepository
	Cluster() ClusterRepository
	Project() ProjectRepository
	Application() ApplicationRepository
	SystemConfig() SystemConfigRepository
	UserGroup() UserGroupRepository
	RBAC() RBACRepository
	Approval() ApprovalRepository
}

// ApprovalRepository 审批仓储接口（临时存根）
type ApprovalRepository interface {
	CreateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error
	GetApprovalFlow(ctx context.Context, id uint) (*model.ApprovalFlow, error)
	UpdateApprovalFlow(ctx context.Context, flow *model.ApprovalFlow) error
	ListApprovalFlows(ctx context.Context) ([]*model.ApprovalFlow, error)
	DeleteApprovalFlow(ctx context.Context, id uint) error
	CreateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error
	GetApprovalRequest(ctx context.Context, id uint) (*model.ApprovalRequest, error)
	UpdateApprovalRequest(ctx context.Context, request *model.ApprovalRequest) error
	ListApprovalRequests(ctx context.Context, page, pageSize int) ([]*model.ApprovalRequest, int64, error)
	DeleteApprovalRequest(ctx context.Context, id uint) error
	UpdateApprovalRequestStep(ctx context.Context, step *model.ApprovalRequestStep) error
}
