package repository

import (
	"context"
	"encoding/json"
	"time"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// auditRepository 审计仓储实现
type auditRepository struct {
	db *gorm.DB
}

// NewAuditRepository 创建审计仓储
func NewAuditRepository(db *gorm.DB) AuditRepository {
	return &auditRepository{
		db: db,
	}
}

// CreateAuditLog 创建审计日志
func (r *auditRepository) CreateAuditLog(ctx context.Context, log *model.AuditLog) error {
	return r.db.WithContext(ctx).Create(log).Error
}

// ListAuditLogs 查询审计日志列表
func (r *auditRepository) ListAuditLogs(ctx context.Context, filter *model.AuditQueryFilter) ([]*model.AuditLog, int64, error) {
	db := r.db.WithContext(ctx)
	var logs []*model.AuditLog
	var total int64

	// 构建查询条件
	query := db.Model(&model.AuditLog{})

	if filter != nil {
		if filter.UserID != nil {
			query = query.Where("user_id = ?", *filter.UserID)
		}
		if filter.Username != "" {
			query = query.Where("username LIKE ?", "%"+filter.Username+"%")
		}
		if filter.UserType != "" {
			query = query.Where("user_type = ?", filter.UserType)
		}
		if filter.Department != "" {
			query = query.Where("department LIKE ?", "%"+filter.Department+"%")
		}
		if filter.Role != "" {
			query = query.Where("role = ?", filter.Role)
		}
		if filter.Action != "" {
			query = query.Where("action = ?", filter.Action)
		}
		if filter.ResourceType != "" {
			query = query.Where("resource_type = ?", filter.ResourceType)
		}
		if filter.ResourceID != "" {
			query = query.Where("resource_id LIKE ?", "%"+filter.ResourceID+"%")
		}
		if filter.Result != "" {
			query = query.Where("result = ?", filter.Result)
		}
		if filter.IP != "" {
			query = query.Where("ip = ?", filter.IP)
		}
		if filter.TraceID != "" {
			query = query.Where("trace_id = ?", filter.TraceID)
		}
		if filter.StartTime != nil {
			query = query.Where("created_at >= ?", *filter.StartTime)
		}
		if filter.EndTime != nil {
			query = query.Where("created_at <= ?", *filter.EndTime)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页
	if filter != nil && filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 默认按时间降序排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetAuditLog 获取单个审计日志
func (r *auditRepository) GetAuditLog(ctx context.Context, id uint) (*model.AuditLog, error) {
	var log model.AuditLog
	err := r.db.WithContext(ctx).First(&log, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &log, nil
}

// ArchiveAuditLogs 归档审计日志
func (r *auditRepository) ArchiveAuditLogs(ctx context.Context, before string, status string) (int64, error) {
	// 解析时间
	beforeTime, err := time.Parse("2006-01-02", before)
	if err != nil {
		return 0, err
	}

	// 创建归档记录
	archive := &model.AuditArchive{
		ArchiveName: "audit_archive_" + before,
		Quarter:     before, // 简化处理，实际应该根据时间计算季度
		StartTime:   beforeTime.AddDate(0, -3, 0), // 假设归档3个月的数据
		EndTime:     beforeTime,
		Status:      status,
		RecordCount: 0,
		CreatedBy:   1, // 默认系统用户ID
	}

	// 开始事务
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}

	// 统计需要归档的日志数量
	var count int64
	if err := tx.Model(&model.AuditLog{}).Where("created_at < ?", beforeTime).Count(&count).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 如果没有需要归档的日志，直接返回
	if count == 0 {
		tx.Rollback()
		return 0, nil
	}

	// 设置归档记录的日志数量
	archive.RecordCount = count

	// 创建归档记录
	if err := tx.Create(archive).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 删除已归档的日志
	if err := tx.Where("created_at < ?", beforeTime).Delete(&model.AuditLog{}).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return count, nil
}

// ExportAuditLogs 导出审计日志
func (r *auditRepository) ExportAuditLogs(ctx context.Context, filter *model.AuditQueryFilter, format string) ([]byte, error) {
	// 查询符合条件的审计日志
	logs, _, err := r.ListAuditLogs(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 根据format将logs转换为相应格式的数据
	if format == "json" {
		return json.Marshal(logs)
	}

	// 其他格式（如CSV）可以在这里实现
	// 暂时返回空数据
	return []byte{}, nil
}

// DeleteAuditLogs 删除审计日志
func (r *auditRepository) DeleteAuditLogs(ctx context.Context, before time.Time) error {
	return r.db.WithContext(ctx).Where("created_at < ?", before).Delete(&model.AuditLog{}).Error
}

// CleanupExpiredAuditLogs 清理过期审计日志
func (r *auditRepository) CleanupExpiredAuditLogs(ctx context.Context, retentionDays int) error {
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays)
	return r.DeleteAuditLogs(ctx, cutoffTime)
}

// ListAuditArchives 获取审计归档列表
func (r *auditRepository) ListAuditArchives(ctx context.Context, page, size int) ([]*model.AuditArchive, int64, error) {
	db := r.db.WithContext(ctx)
	var archives []*model.AuditArchive
	var total int64

	// 获取总数
	if err := db.Model(&model.AuditArchive{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页和排序
	query := db.Model(&model.AuditArchive{})
	if page > 0 && size > 0 {
		offset := (page - 1) * size
		query = query.Offset(offset).Limit(size)
	}

	// 默认按创建时间降序排序
	query = query.Order("created_at DESC")

	// 执行查询
	if err := query.Find(&archives).Error; err != nil {
		return nil, 0, err
	}

	return archives, total, nil
}

// GetAuditArchive 获取审计归档详情
func (r *auditRepository) GetAuditArchive(ctx context.Context, id uint) (*model.AuditArchive, error) {
	var archive model.AuditArchive
	err := r.db.WithContext(ctx).First(&archive, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &archive, nil
}

// CreateAuditArchive 创建审计归档
func (r *auditRepository) CreateAuditArchive(ctx context.Context, archive *model.AuditArchive) error {
	return r.db.WithContext(ctx).Create(archive).Error
}

// UpdateAuditArchive 更新审计归档
func (r *auditRepository) UpdateAuditArchive(ctx context.Context, archive *model.AuditArchive) error {
	return r.db.WithContext(ctx).Save(archive).Error
}

// DeleteAuditArchive 删除审计归档
func (r *auditRepository) DeleteAuditArchive(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.AuditArchive{}, id).Error
}

// GetAuditArchiveConfig 获取审计归档配置
func (r *auditRepository) GetAuditArchiveConfig(ctx context.Context) (*model.AuditArchiveConfig, error) {
	var config model.AuditArchiveConfig
	err := r.db.WithContext(ctx).First(&config).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 返回默认配置
			return &model.AuditArchiveConfig{
				RetentionDays:     90,
				ArchiveInterval:   "quarterly",
				EncryptionEnabled: false,
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
			}, nil
		}
		return nil, err
	}
	return &config, nil
}

// UpdateAuditArchiveConfig 更新审计归档配置
func (r *auditRepository) UpdateAuditArchiveConfig(ctx context.Context, config *model.AuditArchiveConfig) error {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.AuditArchiveConfig{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		// 创建新配置
		return r.db.WithContext(ctx).Create(config).Error
	}

	// 更新现有配置
	return r.db.WithContext(ctx).Model(&model.AuditArchiveConfig{}).Updates(config).Error
}
