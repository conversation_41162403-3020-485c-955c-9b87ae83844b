package repository

import (
	"context"
	"errors"
	"strconv"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// systemConfigRepository 系统配置仓库实现
type systemConfigRepository struct {
	db *gorm.DB
}

// NewSystemConfigRepository 创建系统配置仓库
func NewSystemConfigRepository(db *gorm.DB) SystemConfigRepository {
	return &systemConfigRepository{
		db: db,
	}
}

// GetSystemConfig 获取系统配置
func (r *systemConfigRepository) GetSystemConfig(ctx context.Context, key string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	// 尝试获取第一条记录
	err := r.db.WithContext(ctx).First(&config).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果不存在，返回默认配置
			return r.getDefaultConfig(), nil
		}
		return nil, err
	}
	return &config, nil
}

// UpdateSystemConfig 更新系统配置
func (r *systemConfigRepository) UpdateSystemConfig(ctx context.Context, config *model.SystemConfig) error {
	if config == nil {
		return errors.New("配置不能为空")
	}

	// 检查是否已存在配置
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.SystemConfig{}).Count(&count).Error; err != nil {
		return err
	}

	if count == 0 {
		// 不存在，创建新配置
		return r.db.WithContext(ctx).Create(config).Error
	} else {
		// 已存在，更新配置（使用ID=1）
		config.ID = 1
		return r.db.WithContext(ctx).Save(config).Error
	}
}

// GetFeishuConfig 获取飞书配置
func (r *systemConfigRepository) GetFeishuConfig(ctx context.Context) (*model.SystemFeishuConfig, error) {
	config, err := r.GetSystemConfig(ctx, "feishu")
	if err != nil {
		return nil, err
	}
	return config.GetFeishuConfig(), nil
}

// SetFeishuConfig 设置飞书配置
func (r *systemConfigRepository) SetFeishuConfig(ctx context.Context, feishuConfig *model.SystemFeishuConfig) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, "feishu")
	if err != nil {
		return err
	}

	// 更新飞书配置部分
	config.UpdateFromFeishuConfig(feishuConfig)

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// GetOIDCConfig 获取OIDC配置
func (r *systemConfigRepository) GetOIDCConfig(ctx context.Context) (*model.SystemOIDCConfig, error) {
	config, err := r.GetSystemConfig(ctx, "oidc")
	if err != nil {
		return nil, err
	}
	return config.GetOIDCConfig(), nil
}

// SetOIDCConfig 设置OIDC配置
func (r *systemConfigRepository) SetOIDCConfig(ctx context.Context, oidcConfig *model.SystemOIDCConfig) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, "oidc")
	if err != nil {
		return err
	}

	// 更新OIDC配置部分
	config.UpdateFromOIDCConfig(oidcConfig)

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// GetOBSConfig 获取OBS配置
func (r *systemConfigRepository) GetOBSConfig(ctx context.Context) (*model.SystemOBSConfig, error) {
	config, err := r.GetSystemConfig(ctx, "obs")
	if err != nil {
		return nil, err
	}
	return config.GetOBSConfig(), nil
}

// SetOBSConfig 设置OBS配置
func (r *systemConfigRepository) SetOBSConfig(ctx context.Context, obsConfig *model.SystemOBSConfig) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, "obs")
	if err != nil {
		return err
	}

	// 更新OBS配置部分
	config.UpdateFromOBSConfig(obsConfig)

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// GetAuditConfig 获取审计配置
func (r *systemConfigRepository) GetAuditConfig(ctx context.Context) (*model.SystemAuditConfig, error) {
	config, err := r.GetSystemConfig(ctx, "audit")
	if err != nil {
		return nil, err
	}
	return config.GetAuditConfig(), nil
}

// SetAuditConfig 设置审计配置
func (r *systemConfigRepository) SetAuditConfig(ctx context.Context, auditConfig *model.SystemAuditConfig) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, "audit")
	if err != nil {
		return err
	}

	// 更新审计配置部分
	config.UpdateFromAuditConfig(auditConfig)

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// GetBasicConfig 获取基本系统配置
func (r *systemConfigRepository) GetBasicConfig(ctx context.Context) (*model.SystemBasicConfig, error) {
	config, err := r.GetSystemConfig(ctx, "basic")
	if err != nil {
		return nil, err
	}
	return config.GetBasicConfig(), nil
}

// SetBasicConfig 设置基本系统配置
func (r *systemConfigRepository) SetBasicConfig(ctx context.Context, basicConfig *model.SystemBasicConfig) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, "basic")
	if err != nil {
		return err
	}

	// 更新基本系统配置部分
	config.UpdateFromBasicConfig(basicConfig)

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// SetSystemConfig 设置系统配置
func (r *systemConfigRepository) SetSystemConfig(ctx context.Context, key string, value string) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, key)
	if err != nil {
		return err
	}

	// 根据key设置对应的值
	switch key {
	case "system_name":
		config.SystemName = value
	case "system_version":
		config.SystemVersion = value
	case "system_debug_mode":
		config.SystemDebugMode = value == "true"
	// 可以根据需要添加更多配置项
	default:
		return errors.New("不支持的配置项: " + key)
	}

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// ListSystemConfigs 获取所有系统配置
func (r *systemConfigRepository) ListSystemConfigs(ctx context.Context) ([]*model.SystemConfig, error) {
	var configs []*model.SystemConfig
	err := r.db.WithContext(ctx).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}

// DeleteSystemConfig 删除系统配置
func (r *systemConfigRepository) DeleteSystemConfig(ctx context.Context, key string) error {
	// 获取当前配置
	config, err := r.GetSystemConfig(ctx, key)
	if err != nil {
		return err
	}

	// 根据key设置对应的值为默认值
	switch key {
	case "system_name":
		config.SystemName = "KubeOps"
	case "system_version":
		config.SystemVersion = "1.0.0"
	case "system_debug_mode":
		config.SystemDebugMode = false
	case "feishu_app_id":
		config.FeishuAppID = ""
	case "feishu_app_secret":
		config.FeishuAppSecret = ""
	case "obs_enabled":
		config.OBSEnabled = false
	case "obs_endpoint":
		config.OBSEndpoint = ""
	case "obs_access_key":
		config.OBSAccessKey = ""
	case "obs_secret_key":
		config.OBSSecretKey = ""
	case "obs_bucket":
		config.OBSBucket = ""
	case "obs_region":
		config.OBSRegion = ""
	case "obs_encryption_key":
		config.OBSEncryptionKey = ""
	case "audit_retention_days":
		config.AuditRetentionDays = 90
	case "audit_archive_interval":
		config.AuditArchiveInterval = "0"
	default:
		return errors.New("不支持的配置项: " + key)
	}

	// 保存更新后的配置
	return r.UpdateSystemConfig(ctx, config)
}

// getDefaultConfig 获取默认系统配置
func (r *systemConfigRepository) getDefaultConfig() *model.SystemConfig {
	return &model.SystemConfig{
		// OIDC配置默认值
		OIDCEnabled:      false,
		OIDCIssuerURL:    "",
		OIDCClientID:     "",
		OIDCClientSecret: "",
		OIDCRedirectURI:  "",
		OIDCScopes:       "openid,profile,email",
		OIDCGroupsClaim:  "groups",
		OIDCRolesClaim:   "roles",

		// 飞书配置默认值
		FeishuAppID:     "",
		FeishuAppSecret: "",

		// OBS配置默认值
		OBSEnabled:       false,
		OBSEndpoint:      "https://obs.cn-north-4.myhuaweicloud.com",
		OBSAccessKey:     "",
		OBSSecretKey:     "",
		OBSBucket:        "kubeops-audit-logs",
		OBSRegion:        "cn-north-4",
		OBSEncryptionKey: "",

		// 审计配置默认值
		AuditRetentionDays:   90,
		AuditArchiveInterval: "quarterly",

		// 系统配置默认值
		SystemName:         "KubeOps",
		SystemContactEmail: "",
		SystemVersion:      "1.0.0",
		SystemDebugMode:    false,
	}
}

// Helper functions for type conversion
func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

func intToString(i int) string {
	return strconv.Itoa(i)
}

func stringToInt(s string, defaultVal int) int {
	val, err := strconv.Atoi(s)
	if err != nil {
		return defaultVal
	}
	return val
}
