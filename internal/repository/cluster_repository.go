package repository

import (
	"context"

	"kubeops/internal/model"

	"gorm.io/gorm"
)

// clusterRepository 集群仓储实现
type clusterRepository struct {
	db *gorm.DB
}

// NewClusterRepository 创建集群仓储
func NewClusterRepository(db *gorm.DB) ClusterRepository {
	return &clusterRepository{
		db: db,
	}
}

// GetClusterByID 通过ID获取集群
func (r *clusterRepository) GetClusterByID(ctx context.Context, id uint) (*model.Cluster, error) {
	var cluster model.Cluster
	err := r.db.WithContext(ctx).First(&cluster, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &cluster, nil
}

// GetClusterByName 通过名称获取集群
func (r *clusterRepository) GetClusterByName(ctx context.Context, name string) (*model.Cluster, error) {
	var cluster model.Cluster
	err := r.db.WithContext(ctx).Where("name = ?", name).First(&cluster).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}
	return &cluster, nil
}

// CreateCluster 创建集群
func (r *clusterRepository) CreateCluster(ctx context.Context, cluster *model.Cluster) error {
	return r.db.WithContext(ctx).Create(cluster).Error
}

// UpdateCluster 更新集群
func (r *clusterRepository) UpdateCluster(ctx context.Context, cluster *model.Cluster) error {
	return r.db.WithContext(ctx).Save(cluster).Error
}

// DeleteCluster 删除集群
func (r *clusterRepository) DeleteCluster(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Cluster{}, id).Error
}

// ListClustersPaginated 分页获取集群列表
func (r *clusterRepository) ListClustersPaginated(ctx context.Context, page, pageSize int) ([]*model.Cluster, int64, error) {
	var clusters []*model.Cluster
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Cluster{})

	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	offset := (page - 1) * pageSize
	err = query.Offset(offset).Limit(pageSize).Find(&clusters).Error
	if err != nil {
		return nil, 0, err
	}

	return clusters, total, nil
}

// UpdateClusterStatus 更新集群状态
func (r *clusterRepository) UpdateClusterStatus(ctx context.Context, clusterID uint, status model.ClusterStatus) error {
	return r.db.WithContext(ctx).Model(&model.Cluster{}).Where("id = ?", clusterID).Update("status", status).Error
}

// GetActiveClusterIDs 获取活跃集群ID列表
func (r *clusterRepository) GetActiveClusterIDs(ctx context.Context) ([]uint, error) {
	var clusterIDs []uint
	err := r.db.WithContext(ctx).Model(&model.Cluster{}).Where("status = ?", model.ClusterStatusActive).Pluck("id", &clusterIDs).Error
	return clusterIDs, err
}

// UpdateClusterConfig 更新集群配置
func (r *clusterRepository) UpdateClusterConfig(ctx context.Context, clusterID uint, config map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.Cluster{}).Where("id = ?", clusterID).Update("config", config).Error
}

// GetClusterConfig 获取集群配置
func (r *clusterRepository) GetClusterConfig(ctx context.Context, clusterID uint) (map[string]interface{}, error) {
	var cluster model.Cluster
	err := r.db.WithContext(ctx).Select("config").First(&cluster, clusterID).Error
	if err != nil {
		return nil, err
	}
	return cluster.Config, nil
}
