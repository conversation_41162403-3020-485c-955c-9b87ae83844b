package repository

import (
	"context"
	"time"

	"gorm.io/gorm"

	"kubeops/internal/model"
)

// userRepository 用户仓储实现
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓储实例
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{
		db: db,
	}
}

// CreateUser 创建用户
func (r *userRepository) CreateUser(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Create(user).Error
}

// GetUserByID 根据ID获取用户
func (r *userRepository) GetUserByID(ctx context.Context, id uint) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).First(&user, id).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (r *userRepository) GetUserByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("username = ?", username).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByEmail 根据邮箱获取用户
func (r *userRepository) GetUserByEmail(ctx context.Context, email string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("email = ?", email).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用户
func (r *userRepository) UpdateUser(ctx context.Context, user *model.User) error {
	return r.db.WithContext(ctx).Save(user).Error
}

// DeleteUser 删除用户
func (r *userRepository) DeleteUser(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.User{}, id).Error
}

// ListUsersPaginated 分页获取用户列表
func (r *userRepository) ListUsersPaginated(ctx context.Context, page, pageSize int) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	// 获取总数
	if err := r.db.WithContext(ctx).Model(&model.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := r.db.WithContext(ctx).Offset(offset).Limit(pageSize).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// UpdateUserStatus 更新用户状态
func (r *userRepository) UpdateUserStatus(ctx context.Context, userID uint, status model.UserStatus) error {
	return r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Update("status", status).Error
}

// GetUsersByStatus 根据状态获取用户
func (r *userRepository) GetUsersByStatus(ctx context.Context, status model.UserStatus) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).Where("status = ?", status).Find(&users).Error
	return users, err
}

// UpdateLastLogin 更新用户最后登录时间
func (r *userRepository) UpdateLastLogin(ctx context.Context, userID uint, lastLogin *time.Time) error {
	if lastLogin == nil {
		now := time.Now()
		lastLogin = &now
	}
	return r.db.WithContext(ctx).Model(&model.User{}).Where("id = ?", userID).Update("last_login_at", lastLogin).Error
}

// GetUserByExternalID 根据外部ID获取用户
func (r *userRepository) GetUserByExternalID(ctx context.Context, externalID string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("external_id = ?", externalID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// BatchCreateUsers 批量创建用户
func (r *userRepository) BatchCreateUsers(ctx context.Context, users []*model.User) error {
	return r.db.WithContext(ctx).CreateInBatches(users, 100).Error
}

// SearchUsers 搜索用户
func (r *userRepository) SearchUsers(ctx context.Context, keyword string, page, pageSize int) ([]*model.User, int64, error) {
	var users []*model.User
	var total int64

	query := r.db.WithContext(ctx).Model(&model.User{})
	if keyword != "" {
		query = query.Where("username LIKE ? OR email LIKE ? OR display_name LIKE ?", "%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&users).Error
	if err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// AddUserToGroups 将用户添加到用户组
func (r *userRepository) AddUserToGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	// 先删除用户现有的组关联
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&model.UserGroupMember{}).Error; err != nil {
		return err
	}

	// 批量添加新的组关联
	var members []*model.UserGroupMember
	for _, groupID := range groupIDs {
		members = append(members, &model.UserGroupMember{
			UserID:  userID,
			GroupID: groupID,
		})
	}

	if len(members) > 0 {
		return r.db.WithContext(ctx).CreateInBatches(members, 100).Error
	}

	return nil
}

// GetUserGroups 获取用户的用户组
func (r *userRepository) GetUserGroups(ctx context.Context, userID uint) ([]*model.UserGroup, error) {
	var groups []*model.UserGroup
	err := r.db.WithContext(ctx).
		Table("user_groups").
		Joins("JOIN user_group_members ON user_groups.id = user_group_members.group_id").
		Where("user_group_members.user_id = ?", userID).
		Find(&groups).Error
	return groups, err
}

// RemoveUserFromGroups 从用户组中移除用户
func (r *userRepository) RemoveUserFromGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	return r.db.WithContext(ctx).Where("user_id = ? AND group_id IN ?", userID, groupIDs).Delete(&model.UserGroupMember{}).Error
}

// GetUserByOIDCSubject 根据OIDC Subject获取用户
func (r *userRepository) GetUserByOIDCSubject(ctx context.Context, subject string) (*model.User, error) {
	var user model.User
	err := r.db.WithContext(ctx).Where("oidc_subject = ?", subject).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// ListUsers 获取用户列表
func (r *userRepository) ListUsers(ctx context.Context) ([]*model.User, error) {
	var users []*model.User
	err := r.db.WithContext(ctx).Find(&users).Error
	return users, err
}

// SyncUserGroups 同步用户组
func (r *userRepository) SyncUserGroups(ctx context.Context, userID uint, groupIDs []uint) error {
	// 先删除用户现有的组关联
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Delete(&model.UserGroupMember{}).Error; err != nil {
		return err
	}

	// 批量添加新的组关联
	var members []*model.UserGroupMember
	for _, groupID := range groupIDs {
		members = append(members, &model.UserGroupMember{
			UserID:  userID,
			GroupID: groupID,
		})
	}

	if len(members) > 0 {
		return r.db.WithContext(ctx).CreateInBatches(members, 100).Error
	}

	return nil
}

// GetUserByFeishuID 根据飞书ID获取用户
func (r *userRepository) GetUserByFeishuID(ctx context.Context, feishuOpenID, feishuUnionID string) (*model.User, error) {
	var user model.User
	query := r.db.WithContext(ctx)

	if feishuOpenID != "" {
		query = query.Where("feishu_open_id = ?", feishuOpenID)
	} else if feishuUnionID != "" {
		query = query.Where("feishu_union_id = ?", feishuUnionID)
	} else {
		return nil, gorm.ErrRecordNotFound
	}

	err := query.First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// CountUsers 统计用户数量
func (r *userRepository) CountUsers(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&model.User{}).Count(&count).Error
	return count, err
}
