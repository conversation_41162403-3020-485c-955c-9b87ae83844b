package response

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/trace"
)

// 状态码常量
const (
	CodeSuccess            = 20000 // 成功状态码
	CodeInvalidParams      = 40000 // 参数错误
	CodeUnauthorized       = 40100 // 未授权
	CodeForbidden          = 40300 // 禁止访问
	CodeNotFound           = 40400 // 资源不存在
	CodeInternalError      = 50000 // 内部错误
	CodeServiceUnavailable = 50300 // 服务不可用
)

// 状态码对应的消息
var codeMessages = map[int]string{
	CodeSuccess:            "成功",
	CodeInvalidParams:      "参数错误",
	CodeUnauthorized:       "未授权或登录失效",
	CodeForbidden:          "禁止访问",
	CodeNotFound:           "资源不存在",
	CodeInternalError:      "系统内部错误",
	CodeServiceUnavailable: "服务不可用",
}

// Response 统一API响应结构
type Response struct {
	Code      int         `json:"code"`      // 业务状态码
	Message   string      `json:"message"`   // 状态码说明
	Data      interface{} `json:"data"`      // 业务数据
	TraceID   string      `json:"trace_id"`  // 追踪ID
	Timestamp int64       `json:"timestamp"` // 响应时间戳
}

// ResponseOption 自定义响应选项的函数类型
type ResponseOption func(*Response)

// WithTraceID 设置响应的TraceID
func WithTraceID(ctx *gin.Context) ResponseOption {
	return func(r *Response) {
		if span := trace.SpanFromContext(ctx.Request.Context()); span != nil {
			r.TraceID = span.SpanContext().TraceID().String()
		}
	}
}

// WithMessage 自定义响应消息
func WithMessage(message string) ResponseOption {
	return func(r *Response) {
		r.Message = message
	}
}

// buildResponse 构建响应对象
func buildResponse(code int, data interface{}, opts ...ResponseOption) *Response {
	// 创建基础响应
	resp := &Response{
		Code:      code,
		Message:   codeMessages[code],
		Data:      data,
		Timestamp: time.Now().Unix(),
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(resp)
	}

	return resp
}

// Success 成功响应
func Success(c *gin.Context, data interface{}, opts ...ResponseOption) {
	// 自动添加追踪ID
	opts = append(opts, WithTraceID(c))
	c.JSON(http.StatusOK, buildResponse(CodeSuccess, data, opts...))
}

// Fail 失败响应
func Fail(c *gin.Context, code int, data interface{}, opts ...ResponseOption) {
	// 自动添加追踪ID
	opts = append(opts, WithTraceID(c))

	// 如果未提供消息，使用默认消息
	messageProvided := false
	for _, opt := range opts {
		tempResp := &Response{}
		opt(tempResp)
		if tempResp.Message != "" {
			messageProvided = true
			break
		}
	}

	if !messageProvided {
		if msg, exists := codeMessages[code]; exists {
			opts = append(opts, WithMessage(msg))
		} else {
			opts = append(opts, WithMessage("未知错误"))
		}
	}

	// 根据业务状态码选择HTTP状态码
	var httpStatus int
	switch {
	case code >= 40000 && code < 40100:
		httpStatus = http.StatusBadRequest
	case code >= 40100 && code < 40200:
		httpStatus = http.StatusUnauthorized
	case code >= 40300 && code < 40400:
		httpStatus = http.StatusForbidden
	case code >= 40400 && code < 40500:
		httpStatus = http.StatusNotFound
	case code >= 50000:
		httpStatus = http.StatusInternalServerError
	default:
		httpStatus = http.StatusOK
	}

	c.JSON(httpStatus, buildResponse(code, data, opts...))
}

// BadRequest 参数错误响应
func BadRequest(c *gin.Context, message string, data ...interface{}) {
	var responseData interface{}
	if len(data) > 0 {
		responseData = data[0]
	}
	Fail(c, CodeInvalidParams, responseData, WithMessage(message))
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context, message string) {
	Fail(c, CodeUnauthorized, nil, WithMessage(message))
}

// Forbidden 禁止访问响应
func Forbidden(c *gin.Context, message string) {
	Fail(c, CodeForbidden, nil, WithMessage(message))
}

// NotFound 资源不存在响应
func NotFound(c *gin.Context, message string) {
	Fail(c, CodeNotFound, nil, WithMessage(message))
}

// ServerError 服务器错误响应
func ServerError(c *gin.Context, err error) {
	message := "系统内部错误"
	if err != nil {
		message = fmt.Sprintf("系统内部错误: %s", err.Error())
	}
	Fail(c, CodeInternalError, nil, WithMessage(message))
}

// ServiceUnavailable 服务不可用响应
func ServiceUnavailable(c *gin.Context, message string) {
	Fail(c, CodeServiceUnavailable, nil, WithMessage(message))
}
