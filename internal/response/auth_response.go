package response

import (
	"kubeops/internal/service"
)

// AuthResponse 认证相关的响应结构

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	TokenType    string             `json:"token_type"`
	ExpiresIn    int64              `json:"expires_in"`
	User         *service.UserInfo  `json:"user"`
	SessionID    string             `json:"session_id"`
	LoginTime    int64              `json:"login_time"`
}

// RefreshTokenResponse 刷新Token响应
type RefreshTokenResponse struct {
	AccessToken  string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	TokenType    string             `json:"token_type"`
	ExpiresIn    int64              `json:"expires_in"`
	User         *service.UserInfo  `json:"user"`
	SessionID    string             `json:"session_id"`
}

// UserInfoResponse 用户信息响应
type UserInfoResponse struct {
	UserID      uint     `json:"user_id"`
	Username    string   `json:"username"`
	Email       string   `json:"email"`
	DisplayName string   `json:"display_name"`
	Avatar      string   `json:"avatar,omitempty"`
	Roles       []string `json:"roles"`
	Groups      []string `json:"groups"`
	Permissions []string `json:"permissions"`
	Status      string   `json:"status"`
	LastLogin   *int64   `json:"last_login,omitempty"`
	SessionInfo *SessionInfo `json:"session_info,omitempty"`
}

// SessionInfo 会话信息
type SessionInfo struct {
	SessionID    string `json:"session_id"`
	LoginTime    int64  `json:"login_time"`
	LastActivity int64  `json:"last_activity"`
	DeviceType   string `json:"device_type"`
	IPAddress    string `json:"ip_address"`
	UserAgent    string `json:"user_agent,omitempty"`
}

// SessionListResponse 会话列表响应
type SessionListResponse struct {
	Sessions    []*service.UserSession `json:"sessions"`
	TotalCount  int                    `json:"total_count"`
	CurrentPage int                    `json:"current_page,omitempty"`
	PageSize    int                    `json:"page_size,omitempty"`
}

// LogoutResponse 登出响应
type LogoutResponse struct {
	Message   string `json:"message"`
	LogoutAll bool   `json:"logout_all,omitempty"`
}

// TokenValidationResponse Token验证响应
type TokenValidationResponse struct {
	Valid       bool                  `json:"valid"`
	User        *service.UserInfo     `json:"user,omitempty"`
	Claims      *service.UserClaims   `json:"claims,omitempty"`
	ExpiresAt   int64                 `json:"expires_at,omitempty"`
	TokenType   string                `json:"token_type,omitempty"`
}

// SecurityEventResponse 安全事件响应
type SecurityEventResponse struct {
	EventType   string `json:"event_type"`   // login_success, login_failed, logout, token_refresh
	Username    string `json:"username"`
	IPAddress   string `json:"ip_address"`
	UserAgent   string `json:"user_agent,omitempty"`
	Timestamp   int64  `json:"timestamp"`
	Success     bool   `json:"success"`
	Reason      string `json:"reason,omitempty"`
	SessionID   string `json:"session_id,omitempty"`
}

// AccountLockResponse 账户锁定响应
type AccountLockResponse struct {
	Username     string `json:"username"`
	IsLocked     bool   `json:"is_locked"`
	LockTime     int64  `json:"lock_time,omitempty"`
	UnlockTime   int64  `json:"unlock_time,omitempty"`
	AttemptCount int    `json:"attempt_count"`
	MaxAttempts  int    `json:"max_attempts"`
	LastAttempt  int64  `json:"last_attempt,omitempty"`
}

// 转换函数

// ConvertTokenPairToLoginResponse 转换TokenPair为登录响应
func ConvertTokenPairToLoginResponse(tokenPair *service.TokenPairResponse) *LoginResponse {
	if tokenPair == nil {
		return nil
	}
	
	return &LoginResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		TokenType:    tokenPair.TokenType,
		ExpiresIn:    tokenPair.ExpiresIn,
		User:         tokenPair.User,
		SessionID:    tokenPair.SessionID,
		LoginTime:    tokenPair.LoginTime,
	}
}

// ConvertTokenPairToRefreshResponse 转换TokenPair为刷新响应
func ConvertTokenPairToRefreshResponse(tokenPair *service.TokenPairResponse) *RefreshTokenResponse {
	if tokenPair == nil {
		return nil
	}
	
	return &RefreshTokenResponse{
		AccessToken:  tokenPair.AccessToken,
		RefreshToken: tokenPair.RefreshToken,
		TokenType:    tokenPair.TokenType,
		ExpiresIn:    tokenPair.ExpiresIn,
		User:         tokenPair.User,
		SessionID:    tokenPair.SessionID,
	}
}

// ConvertUserInfoToResponse 转换用户信息为响应
func ConvertUserInfoToResponse(userInfo *service.UserInfo, sessionInfo *SessionInfo) *UserInfoResponse {
	if userInfo == nil {
		return nil
	}
	
	return &UserInfoResponse{
		UserID:      userInfo.UserID,
		Username:    userInfo.Username,
		Email:       userInfo.Email,
		DisplayName: userInfo.DisplayName,
		Avatar:      userInfo.Avatar,
		Roles:       userInfo.Roles,
		Groups:      userInfo.Groups,
		Permissions: userInfo.Permissions,
		Status:      userInfo.Status,
		LastLogin:   userInfo.LastLogin,
		SessionInfo: sessionInfo,
	}
}

// ConvertSessionsToResponse 转换会话列表为响应
func ConvertSessionsToResponse(sessions []*service.UserSession, currentPage, pageSize int) *SessionListResponse {
	return &SessionListResponse{
		Sessions:    sessions,
		TotalCount:  len(sessions),
		CurrentPage: currentPage,
		PageSize:    pageSize,
	}
}

// ConvertUserClaimsToValidationResponse 转换用户Claims为验证响应
func ConvertUserClaimsToValidationResponse(claims *service.UserClaims, userInfo *service.UserInfo) *TokenValidationResponse {
	if claims == nil {
		return &TokenValidationResponse{
			Valid: false,
		}
	}
	
	return &TokenValidationResponse{
		Valid:     true,
		User:      userInfo,
		Claims:    claims,
		ExpiresAt: claims.ExpiresAt,
		TokenType: claims.TokenType,
	}
}

// 错误响应

// AuthErrorResponse 认证错误响应
type AuthErrorResponse struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// 常见的认证错误代码
const (
	ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"
	ErrCodeAccountLocked      = "ACCOUNT_LOCKED"
	ErrCodeAccountDisabled    = "ACCOUNT_DISABLED"
	ErrCodeTokenExpired       = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid       = "TOKEN_INVALID"
	ErrCodeTokenRevoked       = "TOKEN_REVOKED"
	ErrCodeRefreshFailed      = "REFRESH_FAILED"
	ErrCodeSessionNotFound    = "SESSION_NOT_FOUND"
	ErrCodeTooManyAttempts    = "TOO_MANY_ATTEMPTS"
	ErrCodeOIDCRequired       = "OIDC_REQUIRED"
)

// 错误消息映射
var AuthErrorMessages = map[string]string{
	ErrCodeInvalidCredentials: "用户名或密码错误",
	ErrCodeAccountLocked:      "账户已被锁定",
	ErrCodeAccountDisabled:    "账户已被禁用",
	ErrCodeTokenExpired:       "Token已过期",
	ErrCodeTokenInvalid:       "Token无效",
	ErrCodeTokenRevoked:       "Token已被撤销",
	ErrCodeRefreshFailed:      "Token刷新失败",
	ErrCodeSessionNotFound:    "会话不存在",
	ErrCodeTooManyAttempts:    "登录尝试次数过多",
	ErrCodeOIDCRequired:       "请使用OIDC登录",
}

// NewAuthErrorResponse 创建认证错误响应
func NewAuthErrorResponse(code, details string) *AuthErrorResponse {
	message, exists := AuthErrorMessages[code]
	if !exists {
		message = "认证失败"
	}
	
	return &AuthErrorResponse{
		Code:    code,
		Message: message,
		Details: details,
	}
}
