package middleware

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"kubeops/internal/auth"
	"kubeops/internal/config"
	"kubeops/internal/response"
)

const (
	// TokenCookieName Cookie中存储JWT令牌的名称
	TokenCookieName = "kubeops_token"
)

// JWTMiddleware JWT认证中间件 - 重构版本
type JWTMiddleware struct {
	jwtService auth.JWTService
	config     *config.Config
	logger     *zap.Logger
}

// NewJWTMiddleware 创建JWT中间件
func NewJWTMiddleware(jwtService auth.JWTService, cfg *config.Config) *JWTMiddleware {
	return &JWTMiddleware{
		jwtService: jwtService,
		config:     cfg,
		logger:     zap.L().Named("jwt.middleware"),
	}
}

// AuthRequired JWT认证中间件
func (m *JWTMiddleware) AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取Token
		token, err := m.extractToken(c)
		if err != nil {
			m.logger.Debug("Failed to extract token", zap.Error(err))
			response.Unauthorized(c, "Missing or invalid authorization header")
			c.Abort()
			return
		}

		// 验证Token
		claims, err := m.jwtService.VerifyAccessToken(c.Request.Context(), token)
		if err != nil {
			m.logger.Debug("Token verification failed",
				zap.String("token", token[:min(len(token), 20)]+"..."),
				zap.Error(err))
			response.Unauthorized(c, "Invalid or expired token")
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		m.setUserContext(c, claims)

		// 更新最后活跃时间
		go m.updateLastActivity(c.Request.Context(), claims)

		c.Next()
	}
}

// OptionalAuth 可选认证中间件
func (m *JWTMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试提取Token
		token, err := m.extractToken(c)
		if err != nil {
			// 没有Token时继续执行，但不设置用户上下文
			c.Next()
			return
		}

		// 验证Token
		claims, err := m.jwtService.VerifyAccessToken(c.Request.Context(), token)
		if err != nil {
			// Token无效时继续执行，但不设置用户上下文
			m.logger.Debug("Optional auth token verification failed", zap.Error(err))
			c.Next()
			return
		}

		// 将用户信息存储到上下文
		m.setUserContext(c, claims)

		// 更新最后活跃时间
		go m.updateLastActivity(c.Request.Context(), claims)

		c.Next()
	}
}

// RequireRoles 角色验证中间件
func (m *JWTMiddleware) RequireRoles(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userClaims, exists := m.getUserFromContext(c)
		if !exists {
			response.Unauthorized(c, "Authentication required")
			c.Abort()
			return
		}

		// 检查角色
		if !m.hasAnyRole(userClaims.Roles, roles) {
			m.logger.Warn("Access denied - insufficient roles",
				zap.Uint("user_id", userClaims.UserID),
				zap.Strings("user_roles", userClaims.Roles),
				zap.Strings("required_roles", roles))

			response.Forbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermissions 权限验证中间件
func (m *JWTMiddleware) RequirePermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		userClaims, exists := m.getUserFromContext(c)
		if !exists {
			response.Unauthorized(c, "Authentication required")
			c.Abort()
			return
		}

		// 检查权限
		if !m.hasAllPermissions(userClaims.Permissions, permissions) {
			m.logger.Warn("Access denied - insufficient permissions",
				zap.Uint("user_id", userClaims.UserID),
				zap.Strings("user_permissions", userClaims.Permissions),
				zap.Strings("required_permissions", permissions))

			response.Forbidden(c, "Insufficient permissions")
			c.Abort()
			return
		}

		c.Next()
	}
}

// RefreshTokenMiddleware 刷新Token中间件
func (m *JWTMiddleware) RefreshTokenMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 提取Refresh Token
		token, err := m.extractToken(c)
		if err != nil {
			response.Unauthorized(c, "Missing or invalid authorization header")
			c.Abort()
			return
		}

		// 验证Refresh Token
		claims, err := m.jwtService.VerifyRefreshToken(c.Request.Context(), token)
		if err != nil {
			response.Unauthorized(c, "Invalid or expired refresh token")
			c.Abort()
			return
		}

		// 将claims存储到上下文，供后续处理使用
		c.Set("refresh_claims", claims)
		c.Next()
	}
}

// extractToken 从请求中提取Token
func (m *JWTMiddleware) extractToken(c *gin.Context) (string, error) {
	// 1. 从Authorization header提取
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		// Bearer token格式
		if strings.HasPrefix(authHeader, "Bearer ") {
			return strings.TrimPrefix(authHeader, "Bearer "), nil
		}
		// 直接token格式
		return authHeader, nil
	}

	// 2. 从查询参数提取
	if token := c.Query("token"); token != "" {
		return token, nil
	}

	// 3. 从Cookie提取
	if token, err := c.Cookie("access_token"); err == nil && token != "" {
		return token, nil
	}

	// 4. 兼容旧版本Cookie名称
	if token, err := c.Cookie(TokenCookieName); err == nil && token != "" {
		return token, nil
	}

	return "", fmt.Errorf("no token found")
}

// setUserContext 设置用户上下文
func (m *JWTMiddleware) setUserContext(c *gin.Context, claims *auth.JWTClaims) {
	c.Set("user_id", claims.UserID)
	c.Set("username", claims.Username)
	c.Set("user_claims", claims)
	c.Set("session_id", claims.SessionID)
	c.Set("roles", claims.Roles)
	c.Set("permissions", claims.Permissions)
}

// getUserFromContext 从上下文获取用户信息
func (m *JWTMiddleware) getUserFromContext(c *gin.Context) (*auth.JWTClaims, bool) {
	if claims, exists := c.Get("user_claims"); exists {
		if userClaims, ok := claims.(*auth.JWTClaims); ok {
			return userClaims, true
		}
	}
	return nil, false
}

// hasAnyRole 检查是否拥有任一角色
func (m *JWTMiddleware) hasAnyRole(userRoles, requiredRoles []string) bool {
	roleSet := make(map[string]bool)
	for _, role := range userRoles {
		roleSet[role] = true
	}

	for _, requiredRole := range requiredRoles {
		if roleSet[requiredRole] {
			return true
		}
	}

	return false
}

// hasAllPermissions 检查是否拥有所有权限
func (m *JWTMiddleware) hasAllPermissions(userPermissions, requiredPermissions []string) bool {
	permissionSet := make(map[string]bool)
	for _, permission := range userPermissions {
		permissionSet[permission] = true
	}

	for _, requiredPermission := range requiredPermissions {
		if !permissionSet[requiredPermission] {
			return false
		}
	}

	return true
}

// updateLastActivity 更新最后活跃时间
func (m *JWTMiddleware) updateLastActivity(ctx context.Context, claims *auth.JWTClaims) {
	// 这里需要访问Redis管理器，暂时简化处理
	// 在实际实现中，应该通过依赖注入获取Redis管理器
	m.logger.Debug("Updating user activity",
		zap.Uint("user_id", claims.UserID),
		zap.String("session_id", claims.SessionID))
}

// shouldRefreshToken 检查令牌是否需要刷新
// 旧版本的Token刷新函数，已废弃
/*
// 如果令牌剩余有效期不足总有效期的30%，则刷新
func shouldRefreshToken(claims *service.Claims) bool {
	if claims.ExpiresAt == nil {
		return false
	}

	// 计算剩余有效期
	remaining := time.Until(claims.ExpiresAt.Time)
	// 如果剩余时间不足30%，则刷新令牌
	threshold := time.Duration(float64(claims.ExpiresAt.Time.Sub(claims.IssuedAt.Time)) * 0.3)

	return remaining < threshold
}

// refreshToken 刷新JWT令牌
func refreshToken(c *gin.Context, jwtService service.JWTService, claims *service.Claims) {
	// 生成新令牌
	tokenInfo, err := jwtService.GenerateToken(claims.UserID, claims.Username)
	if err != nil {
		logger.WarnContext(c.Request.Context(), "Failed to refresh token", zap.Error(err))
		return
	}

	// 设置新的Cookie
	c.SetCookie(
		TokenCookieName,
		tokenInfo.Token,
		int(time.Until(tokenInfo.ExpiresAt).Seconds()),
		"/",
		"",
		true, // 仅HTTPS
		true, // HTTP-only
	)

	// 在响应头中也返回新令牌
	c.Header("X-Refresh-Token", tokenInfo.Token)
}

// GetCurrentUser 获取当前用户信息的辅助函数
func GetCurrentUser(c *gin.Context) (*auth.JWTClaims, bool) {
	if claims, exists := c.Get("user_claims"); exists {
		if userClaims, ok := claims.(*auth.JWTClaims); ok {
			return userClaims, true
		}
	}
	return nil, false
}

// GetCurrentUserID 获取当前用户ID的辅助函数
func GetCurrentUserID(c *gin.Context) (uint, bool) {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(uint); ok {
			return id, true
		}
	}
	return 0, false
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
*/
