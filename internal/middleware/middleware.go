package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ErrorHandler 全局错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 执行请求处理
		c.Next()

		// 如果有错误，处理并返回统一的错误响应
		if len(c.Errors) > 0 {
			// 获取第一个错误
			err := c.Errors.Last().Err

			// 获取 trace ID（如果存在）
			traceID := c.Writer.Header().Get("X-Trace-ID")

			// 根据错误类型确定状态码
			statusCode := http.StatusInternalServerError
			if c.Writer.Status() >= 400 {
				statusCode = c.Writer.Status()
			}

			// 构建错误响应
			response := gin.H{
				"code":     statusCode,
				"message":  err.Error(),
				"trace_id": traceID,
			}

			// 响应
			c.<PERSON>(statusCode, response)
			c.Abort()
		}
	}
}

// GetTraceID 从上下文中获取 trace ID
func GetTraceID(c *gin.Context) string {
	if c == nil || c.Writer == nil {
		return ""
	}
	return c.Writer.Header().Get("X-Trace-ID")
}

// GetSpanID 从上下文中获取 span ID
func GetSpanID(c *gin.Context) string {
	if c == nil || c.Writer == nil {
		return ""
	}
	return c.Writer.Header().Get("X-Span-ID")
}

// WithTraceID 为错误添加 trace ID 信息
func WithTraceID(c *gin.Context, err error) error {
	if err == nil || c == nil || c.Writer == nil {
		return err
	}

	traceID := GetTraceID(c)
	if traceID == "" {
		return err
	}

	return fmt.Errorf("%w [trace_id=%s]", err, traceID)
}

// RequestBodyCapture 捕获请求体以供审计日志使用
func RequestBodyCapture() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只处理POST、PUT、PATCH请求
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			// 读取请求体
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err != nil {
				zap.L().Error("Failed to read request body", zap.Error(err))
				c.Next()
				return
			}

			// 恢复请求体，以便后续处理器能够继续读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// 尝试解析JSON并存储到上下文中
			var bodyMap map[string]interface{}
			if err := json.Unmarshal(bodyBytes, &bodyMap); err == nil {
				c.Set("requestBody", bodyMap)
			} else {
				// 如果不是JSON格式，则存储原始内容
				c.Set("requestBody", string(bodyBytes))
			}
		}
		c.Next()
	}
}

// CORS 处理跨域请求中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")

		// 如果是OPTIONS请求，返回204状态码
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
