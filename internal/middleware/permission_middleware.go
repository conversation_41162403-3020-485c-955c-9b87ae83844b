package middleware

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"kubeops/internal/service"
)

// 错误码常量
const (
	CodeUnauthorized = 40100
	CodeForbidden    = 40300
	CodeServerError  = 50000
)

// PermissionMiddleware 权限检查中间件 - 按照technical-design.md实现
type PermissionMiddleware struct {
	rbacService service.RBACService
	logger      *zap.Logger
	tracer      trace.Tracer
}

// NewPermissionMiddleware 创建权限中间件
func NewPermissionMiddleware(rbacService service.RBACService, logger *zap.Logger, tracer trace.Tracer) *PermissionMiddleware {
	return &PermissionMiddleware{
		rbacService: rbacService,
		logger:      logger,
		tracer:      tracer,
	}
}

// RequirePermission 检查用户是否有特定权限
func (m *PermissionMiddleware) RequirePermission(resourceType, resourcePath, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		_, span := m.tracer.Start(c.Request.Context(), "PermissionMiddleware.RequirePermission")
		defer span.End()

		// 记录权限检查参数
		span.SetAttributes(
			attribute.String("permission.resource_type", resourceType),
			attribute.String("permission.resource_path", resourcePath),
			attribute.String("permission.action", action),
		)

		// 从上下文获取用户信息
		userID, exists := c.Get("user_id")
		if !exists {
			m.logger.Warn("权限检查失败：未找到用户ID")
			span.SetStatus(codes.Error, "未找到用户ID")
			m.respondUnauthorized(c, "请先登录")
			return
		}

		userIDUint, ok := userID.(uint)
		if !ok {
			m.logger.Warn("权限检查失败：用户ID类型错误")
			span.SetStatus(codes.Error, "用户ID类型错误")
			m.respondUnauthorized(c, "用户信息无效")
			return
		}

		// 构建实际的资源路径和动作
		actualResourcePath := m.buildResourcePath(c, resourcePath)
		actualAction := m.mapHTTPMethodToAction(c.Request.Method, action)

		span.SetAttributes(
			attribute.String("permission.actual_resource_path", actualResourcePath),
			attribute.String("permission.actual_action", actualAction),
		)

		// 检查权限 - 使用统一的RBAC服务
		fullResourcePath := resourceType + ":" + actualResourcePath
		allowed, err := m.rbacService.CheckPermission(c.Request.Context(), userIDUint, fullResourcePath, actualAction)
		if err != nil {
			m.logger.Error("权限检查错误",
				zap.Uint("user_id", userIDUint),
				zap.String("resource_path", fullResourcePath),
				zap.String("action", actualAction),
				zap.Error(err))
			span.RecordError(err)
			span.SetStatus(codes.Error, err.Error())
			m.respondServerError(c, "权限检查失败")
			return
		}

		span.SetAttributes(attribute.Bool("permission.allowed", allowed))

		if !allowed {
			m.logger.Warn("权限检查失败：用户无权限",
				zap.Uint("user_id", userIDUint),
				zap.String("resource_type", resourceType),
				zap.String("resource_path", actualResourcePath),
				zap.String("action", actualAction))

			m.respondForbidden(c, "无权访问该资源")
			return
		}

		// 权限检查通过，继续处理请求
		c.Next()
	}
}

// RequireAnyPermission 检查用户是否有任意一个指定的权限
func (m *PermissionMiddleware) RequireAnyPermission(permissions []PermissionCheck) gin.HandlerFunc {
	return func(c *gin.Context) {
		_, span := m.tracer.Start(c.Request.Context(), "PermissionMiddleware.RequireAnyPermission")
		defer span.End()

		span.SetAttributes(attribute.Int("permission.checks_count", len(permissions)))

		// 从上下文获取用户信息
		userID, exists := c.Get("user_id")
		if !exists {
			m.logger.Warn("权限检查失败：未找到用户ID")
			span.SetStatus(codes.Error, "未找到用户ID")
			m.respondUnauthorized(c, "请先登录")
			return
		}

		userIDUint, ok := userID.(uint)
		if !ok {
			m.logger.Warn("权限检查失败：用户ID类型错误")
			span.SetStatus(codes.Error, "用户ID类型错误")
			m.respondUnauthorized(c, "用户信息无效")
			return
		}

		// 检查每个权限
		for i, perm := range permissions {
			actualResourcePath := m.buildResourcePath(c, perm.ResourcePath)
			actualAction := m.mapHTTPMethodToAction(c.Request.Method, perm.Action)
			fullResourcePath := perm.ResourceType + ":" + actualResourcePath

			allowed, err := m.rbacService.CheckPermission(c.Request.Context(), userIDUint, fullResourcePath, actualAction)
			if err != nil {
				m.logger.Error("权限检查错误", zap.Int("permission_index", i), zap.Error(err))
				continue
			}

			if allowed {
				span.SetAttributes(
					attribute.Bool("permission.allowed", true),
					attribute.Int("permission.matched_index", i),
				)
				c.Next()
				return
			}
		}

		// 所有权限检查都失败
		span.SetAttributes(attribute.Bool("permission.allowed", false))
		m.logger.Warn("权限检查失败：用户无任何所需权限", zap.Uint("user_id", userIDUint))
		m.respondForbidden(c, "无权访问该资源")
	}
}

// PermissionCheck 权限检查结构
type PermissionCheck struct {
	ResourceType string
	ResourcePath string
	Action       string
}

// buildResourcePath 构建资源路径
func (m *PermissionMiddleware) buildResourcePath(c *gin.Context, resourcePath string) string {
	path := c.Request.URL.Path

	// 如果资源路径包含占位符，进行替换
	if strings.Contains(resourcePath, "{cluster}") {
		clusterName := m.extractClusterFromPath(path)
		if clusterName != "" {
			resourcePath = strings.ReplaceAll(resourcePath, "{cluster}", clusterName)
		}
	}

	if strings.Contains(resourcePath, "{project}") {
		projectName := m.extractProjectFromPath(path)
		if projectName != "" {
			resourcePath = strings.ReplaceAll(resourcePath, "{project}", projectName)
		}
	}

	if strings.Contains(resourcePath, "{application}") {
		appName := m.extractApplicationFromPath(path)
		if appName != "" {
			resourcePath = strings.ReplaceAll(resourcePath, "{application}", appName)
		}
	}

	return resourcePath
}

// mapHTTPMethodToAction HTTP方法映射到操作
func (m *PermissionMiddleware) mapHTTPMethodToAction(method, defaultAction string) string {
	// 如果指定了默认动作，使用默认动作
	if defaultAction != "" && defaultAction != "*" {
		return defaultAction
	}

	// 根据HTTP方法映射
	actionMappings := map[string]string{
		"GET":    "read",
		"POST":   "create",
		"PUT":    "update",
		"PATCH":  "update",
		"DELETE": "delete",
	}

	if action, exists := actionMappings[method]; exists {
		return action
	}

	return "read" // 默认为读取权限
}

// extractClusterFromPath 从URL路径中提取集群名称
func (m *PermissionMiddleware) extractClusterFromPath(path string) string {
	parts := strings.Split(path, "/")
	for i, part := range parts {
		if part == "clusters" && i+1 < len(parts) {
			return parts[i+1]
		}
	}
	return ""
}

// extractProjectFromPath 从URL路径中提取项目名称
func (m *PermissionMiddleware) extractProjectFromPath(path string) string {
	parts := strings.Split(path, "/")
	for i, part := range parts {
		if part == "projects" && i+1 < len(parts) {
			return parts[i+1]
		}
	}
	return ""
}

// extractApplicationFromPath 从URL路径中提取应用名称
func (m *PermissionMiddleware) extractApplicationFromPath(path string) string {
	parts := strings.Split(path, "/")
	for i, part := range parts {
		if part == "applications" && i+1 < len(parts) {
			return parts[i+1]
		}
	}
	return ""
}

// 响应方法
func (m *PermissionMiddleware) respondUnauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, gin.H{
		"code":      CodeUnauthorized,
		"message":   message,
		"timestamp": time.Now().Unix(),
		"trace_id":  trace.SpanFromContext(c.Request.Context()).SpanContext().TraceID().String(),
	})
	c.Abort()
}

func (m *PermissionMiddleware) respondForbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, gin.H{
		"code":      CodeForbidden,
		"message":   message,
		"timestamp": time.Now().Unix(),
		"trace_id":  trace.SpanFromContext(c.Request.Context()).SpanContext().TraceID().String(),
	})
	c.Abort()
}

func (m *PermissionMiddleware) respondServerError(c *gin.Context, message string) {
	c.JSON(http.StatusInternalServerError, gin.H{
		"code":      CodeServerError,
		"message":   message,
		"timestamp": time.Now().Unix(),
		"trace_id":  trace.SpanFromContext(c.Request.Context()).SpanContext().TraceID().String(),
	})
	c.Abort()
}
