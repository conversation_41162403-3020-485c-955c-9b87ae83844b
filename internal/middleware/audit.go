package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"kubeops/internal/model"
	"kubeops/internal/repository"
)

// AuditLogMiddleware 审计日志中间件 - 优化版
func AuditLogMiddleware(auditService interface{}) gin.HandlerFunc {
	// 获取repository实例
	var repo repository.Repository
	switch r := auditService.(type) {
	case repository.Repository:
		repo = r
	default:
		zap.L().Error("Invalid repository type provided to AuditLogMiddleware")
		repo = nil
	}
	return func(c *gin.Context) {
		// 记录请求开始时间
		startTime := time.Now()

		// 执行请求
		c.Next()

		// 异步记录审计日志，避免影响响应时间
		// 复制必要的上下文数据，避免在goroutine中使用已释放的上下文
		userID, _ := c.Get("user_id")
		username, _ := c.Get("username")
		userType, _ := c.Get("userType")
		department, _ := c.Get("department")
		role, _ := c.Get("role")
		requestPath := c.Request.URL.Path
		clientIP := c.ClientIP()
		userAgent := c.Request.UserAgent()
		status := c.Writer.Status()

		// 获取请求参数
		var requestParams string
		if c.Request.Body != nil && c.Request.Method != "GET" {
			if body, exists := c.Get("requestBody"); exists {
				if bodyBytes, err := json.Marshal(body); err == nil {
					requestParams = string(bodyBytes)
				}
			}
		}

		// 获取错误信息
		var errorMsg string
		if len(c.Errors) > 0 {
			errorMsg = c.Errors.String()
		} else if status >= 400 {
			errorMsg = fmt.Sprintf("HTTP %d", status)
		}

		// 获取traceID
		traceID := GetTraceID(c)

		go func() {
			// 创建审计日志记录
			auditLog := &model.AuditLog{
				UserID:        getUserID(userID),
				Username:      getString(username, "anonymous"),
				UserType:      getString(userType, "unknown"),
				Department:    getString(department, ""),
				Role:          getString(role, ""),
				Action:        mapMethodToAction(c.Request.Method),
				ResourceType:  mapPathToResourceType(requestPath),
				ResourceID:    extractResourceID(requestPath),
				RequestPath:   requestPath,
				RequestParams: requestParams,
				Result:        mapStatusToResult(status),
				ErrorMessage:  errorMsg,
				IP:            clientIP,
				Client:        userAgent,
				TraceID:       traceID,
				CreatedAt:     startTime,
			}

			// 异步写入，带重试机制
			if repo != nil {
				ctx := context.Background()
				if err := writeAuditLogWithRetry(ctx, repo, auditLog); err != nil {
					zap.L().Error("Failed to write audit log after retries",
						zap.Error(err),
						zap.String("trace_id", auditLog.TraceID),
						zap.String("user", auditLog.Username),
						zap.String("action", auditLog.Action),
					)
				}
			} else {
				zap.L().Error("Cannot write audit log: repository is nil",
					zap.String("trace_id", auditLog.TraceID),
					zap.String("user", auditLog.Username),
					zap.String("action", auditLog.Action),
				)
			}
		}()
	}
}

// 注意：buildAuditLog 函数已被内联到 AuditLogMiddleware 中，以避免在 goroutine 中使用已释放的上下文

// writeAuditLogWithRetry 带重试机制的审计日志写入
func writeAuditLogWithRetry(ctx context.Context, repo repository.Repository, log *model.AuditLog) error {
	maxRetries := 3
	retryDelay := 100 * time.Millisecond

	for i := 0; i < maxRetries; i++ {
		if err := repo.Audit().CreateAuditLog(ctx, log); err == nil {
			return nil
		} else if i < maxRetries-1 {
			// 指数退避重试
			time.Sleep(retryDelay * time.Duration(1<<i))
		} else {
			return fmt.Errorf("failed to write audit log after %d retries: %w", maxRetries, err)
		}
	}

	return nil
}

// 辅助函数
func getUserID(userID interface{}) uint {
	if userID == nil {
		return 0
	}
	if id, ok := userID.(uint); ok {
		return id
	}
	return 0
}

func getString(value interface{}, defaultValue string) string {
	if value == nil {
		return defaultValue
	}
	if str, ok := value.(string); ok {
		return str
	}
	return defaultValue
}

// mapMethodToAction 将HTTP方法映射到审计操作类型
func mapMethodToAction(method string) string {
	switch method {
	case "GET":
		return "read"
	case "POST":
		return "create"
	case "PUT", "PATCH":
		return "update"
	case "DELETE":
		return "delete"
	default:
		return "other"
	}
}

// mapPathToResourceType 将请求路径映射到资源类型
func mapPathToResourceType(path string) string {
	path = strings.ToLower(path)

	switch {
	case strings.Contains(path, "/users"):
		return "user"
	case strings.Contains(path, "/clusters"):
		return "cluster"
	case strings.Contains(path, "/namespaces"):
		return "namespace"
	case strings.Contains(path, "/pods"):
		return "pod"
	case strings.Contains(path, "/deployments"):
		return "deployment"
	case strings.Contains(path, "/services"):
		return "service"
	case strings.Contains(path, "/roles"):
		return "role"
	case strings.Contains(path, "/permissions"):
		return "permission"
	case strings.Contains(path, "/approvals"):
		return "approval"
	case strings.Contains(path, "/audit"):
		return "audit"
	case strings.Contains(path, "/alerts"):
		return "alert"
	case strings.Contains(path, "/tasks"):
		return "task"
	default:
		return "other"
	}
}

// extractResourceID 从路径中提取资源ID
func extractResourceID(path string) string {
	parts := strings.Split(strings.Trim(path, "/"), "/")
	if len(parts) >= 2 {
		// 尝试从路径末尾获取ID
		lastPart := parts[len(parts)-1]
		if lastPart != "" && !strings.Contains(lastPart, "?") {
			return lastPart
		}
	}
	return ""
}

// mapStatusToResult 将HTTP状态码映射到操作结果
func mapStatusToResult(status int) string {
	if status >= 200 && status < 300 {
		return "success"
	}
	return "failed"
}

// 注意：getErrorMessage 函数已被内联到 AuditLogMiddleware 中，以避免在 goroutine 中使用已释放的上下文

// AuditMiddleware 审计中间件结构体
type AuditMiddleware struct {
	auditService interface{}
	logger       *zap.Logger
}

// NewAuditMiddleware 创建新的审计中间件
func NewAuditMiddleware(auditService interface{}, logger *zap.Logger) *AuditMiddleware {
	return &AuditMiddleware{
		auditService: auditService,
		logger:       logger,
	}
}

// Handle 返回审计中间件处理函数
func (m *AuditMiddleware) Handle() gin.HandlerFunc {
	return AuditLogMiddleware(m.auditService)
}
