package redis

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// redisLock Redis分布式锁实现
type redisLock struct {
	client     redis.Cmdable
	key        string
	value      string
	expiration time.Duration
}

// Release 释放锁
func (l *redisLock) Release(ctx context.Context) error {
	// 使用Lua脚本确保原子性
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	
	result, err := l.client.Eval(ctx, script, []string{l.key}, l.value).Result()
	if err != nil {
		return fmt.Errorf("failed to release lock: %w", err)
	}
	
	if result.(int64) == 0 {
		return fmt.Errorf("lock not owned by this instance")
	}
	
	return nil
}

// Refresh 刷新锁的过期时间
func (l *redisLock) Refresh(ctx context.Context, expiration time.Duration) error {
	// 使用Lua脚本确保原子性
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("expire", KEYS[1], ARGV[2])
		else
			return 0
		end
	`
	
	result, err := l.client.Eval(ctx, script, []string{l.key}, l.value, int(expiration.Seconds())).Result()
	if err != nil {
		return fmt.Errorf("failed to refresh lock: %w", err)
	}
	
	if result.(int64) == 0 {
		return fmt.Errorf("lock not owned by this instance")
	}
	
	l.expiration = expiration
	return nil
}

// TTL 获取锁的剩余时间
func (l *redisLock) TTL(ctx context.Context) (time.Duration, error) {
	ttl, err := l.client.TTL(ctx, l.key).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get lock TTL: %w", err)
	}
	
	return ttl, nil
}

// generateLockValue 生成锁的唯一值
func generateLockValue() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// LockOptions 锁选项
type LockOptions struct {
	Expiration time.Duration
	RetryDelay time.Duration
	MaxRetries int
}

// DefaultLockOptions 默认锁选项
func DefaultLockOptions() *LockOptions {
	return &LockOptions{
		Expiration: 30 * time.Second,
		RetryDelay: 100 * time.Millisecond,
		MaxRetries: 3,
	}
}

// TryLockWithRetry 带重试的尝试获取锁
func TryLockWithRetry(ctx context.Context, manager RedisManager, key string, opts *LockOptions) (Lock, error) {
	if opts == nil {
		opts = DefaultLockOptions()
	}
	
	var lastErr error
	for i := 0; i <= opts.MaxRetries; i++ {
		lock, acquired, err := manager.TryLock(ctx, key, opts.Expiration)
		if err != nil {
			lastErr = err
			continue
		}
		
		if acquired {
			return lock, nil
		}
		
		// 如果不是最后一次重试，等待一段时间
		if i < opts.MaxRetries {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(opts.RetryDelay):
				// 继续重试
			}
		}
	}
	
	if lastErr != nil {
		return nil, lastErr
	}
	
	return nil, fmt.Errorf("failed to acquire lock after %d retries", opts.MaxRetries)
}

// WithLock 在锁保护下执行函数
func WithLock(ctx context.Context, manager RedisManager, key string, expiration time.Duration, fn func() error) error {
	lock, err := manager.Lock(ctx, key, expiration)
	if err != nil {
		return fmt.Errorf("failed to acquire lock: %w", err)
	}
	
	defer func() {
		if releaseErr := lock.Release(ctx); releaseErr != nil {
			// 记录释放锁失败的错误，但不影响主要逻辑
			fmt.Printf("Warning: failed to release lock %s: %v\n", key, releaseErr)
		}
	}()
	
	return fn()
}

// AutoRefreshLock 自动刷新锁
type AutoRefreshLock struct {
	lock       Lock
	stopCh     chan struct{}
	refreshErr chan error
}

// NewAutoRefreshLock 创建自动刷新锁
func NewAutoRefreshLock(ctx context.Context, lock Lock, refreshInterval time.Duration, expiration time.Duration) *AutoRefreshLock {
	autoLock := &AutoRefreshLock{
		lock:       lock,
		stopCh:     make(chan struct{}),
		refreshErr: make(chan error, 1),
	}
	
	go autoLock.refreshLoop(ctx, refreshInterval, expiration)
	
	return autoLock
}

// refreshLoop 刷新循环
func (al *AutoRefreshLock) refreshLoop(ctx context.Context, refreshInterval, expiration time.Duration) {
	ticker := time.NewTicker(refreshInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-al.stopCh:
			return
		case <-ticker.C:
			if err := al.lock.Refresh(ctx, expiration); err != nil {
				select {
				case al.refreshErr <- err:
				default:
				}
				return
			}
		}
	}
}

// Release 释放自动刷新锁
func (al *AutoRefreshLock) Release(ctx context.Context) error {
	close(al.stopCh)
	return al.lock.Release(ctx)
}

// RefreshError 获取刷新错误
func (al *AutoRefreshLock) RefreshError() <-chan error {
	return al.refreshErr
}
