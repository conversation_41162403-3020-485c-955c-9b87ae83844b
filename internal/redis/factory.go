package redis

import (
	"fmt"

	"kubeops/internal/config"
)

// NewRedisManager 创建Redis管理器
func NewRedisManager(cfg *config.Config) (RedisManager, error) {
	redisConfig := buildRedisConfig(cfg)
	
	// 验证配置
	if err := ValidateRedisConfig(redisConfig); err != nil {
		return nil, fmt.Errorf("invalid redis config: %w", err)
	}
	
	switch redisConfig.Mode {
	case "cluster":
		return NewClusterRedisManager(redisConfig)
	case "sentinel":
		return NewSentinelRedisManager(redisConfig)
	case "standalone":
		return NewStandaloneRedisManager(redisConfig)
	default:
		return nil, fmt.Errorf("unsupported redis mode: %s", redisConfig.Mode)
	}
}

// buildRedisConfig 构建Redis配置
func buildRedisConfig(cfg *config.Config) *RedisConfig {
	redis := cfg.Redis
	
	config := &RedisConfig{
		Mode:          redis.Global.Mode,
		Nodes:         redis.Global.Nodes,
		Password:      redis.Global.Password,
		Database:      redis.Global.Database,
		KeyPrefix:     redis.Global.KeyPrefix,
		Serialization: redis.Global.Serialization,
		
		// 连接池配置
		MaxActive:       redis.Pool.MaxActive,
		MaxIdle:         redis.Pool.MaxIdle,
		IdleTimeout:     redis.Pool.IdleTimeout,
		MaxConnLifetime: redis.Pool.MaxConnLifetime,
		WaitTimeout:     redis.Pool.WaitTimeout,
		MinIdle:         redis.Pool.MinIdle,
		
		// 超时配置
		DialTimeout:        redis.Timeout.Dial,
		ReadTimeout:        redis.Timeout.Read,
		WriteTimeout:       redis.Timeout.Write,
		IdleCheckFrequency: redis.Timeout.IdleCheckFrequency,
		
		// 重试配置
		MaxRetries:      redis.Retry.MaxRetries,
		MinRetryBackoff: redis.Retry.MinRetryBackoff,
		MaxRetryBackoff: redis.Retry.MaxRetryBackoff,
		
		// 监控配置
		MonitoringEnabled:   redis.Monitoring.Enabled,
		SlowQueryThreshold:  redis.Monitoring.SlowQueryThreshold,
		StatsInterval:       redis.Monitoring.StatsInterval,
		HealthCheckInterval: redis.Monitoring.HealthCheckInterval,
		
		// TLS配置
		TLSEnabled:            redis.Security.TLSEnabled,
		TLSCertFile:           redis.Security.TLSConfig.CertFile,
		TLSKeyFile:            redis.Security.TLSConfig.KeyFile,
		TLSCAFile:             redis.Security.TLSConfig.CAFile,
		TLSInsecureSkipVerify: redis.Security.TLSConfig.InsecureSkipVerify,
	}
	
	// 根据模式设置特定配置
	switch redis.Global.Mode {
	case "cluster":
		config.ClusterNodes = redis.Cluster.Nodes
		config.MaxRedirects = redis.Cluster.MaxRedirects
		config.ReadOnly = redis.Cluster.ReadOnly
		config.RouteByLatency = redis.Cluster.RouteByLatency
		config.RouteRandomly = redis.Cluster.RouteRandomly
		
	case "sentinel":
		config.SentinelMasterName = redis.Sentinel.MasterName
		config.SentinelNodes = redis.Sentinel.Nodes
		config.SentinelPassword = redis.Sentinel.SentinelPassword
	}
	
	return config
}

// ValidateRedisConfig 验证Redis配置
func ValidateRedisConfig(config *RedisConfig) error {
	if config.Mode == "" {
		return fmt.Errorf("redis mode is required")
	}
	
	if len(config.Nodes) == 0 {
		return fmt.Errorf("redis nodes cannot be empty")
	}
	
	switch config.Mode {
	case "cluster":
		if len(config.ClusterNodes) < 3 {
			return fmt.Errorf("cluster mode requires at least 3 nodes")
		}
		
	case "sentinel":
		if config.SentinelMasterName == "" {
			return fmt.Errorf("sentinel master name is required")
		}
		if len(config.SentinelNodes) < 3 {
			return fmt.Errorf("sentinel mode requires at least 3 sentinel nodes")
		}
		
	case "standalone":
		if len(config.Nodes) != 1 {
			return fmt.Errorf("standalone mode requires exactly 1 node")
		}
	}
	
	if config.MaxActive <= 0 {
		return fmt.Errorf("max active connections must be greater than 0")
	}
	
	if config.MaxIdle < 0 {
		return fmt.Errorf("max idle connections cannot be negative")
	}
	
	if config.MaxIdle > config.MaxActive {
		return fmt.Errorf("max idle connections cannot exceed max active connections")
	}
	
	return nil
}
