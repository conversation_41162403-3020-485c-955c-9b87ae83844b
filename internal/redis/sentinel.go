package redis

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// SentinelRedisManager 哨兵Redis管理器
type SentinelRedisManager struct {
	client   *redis.Client
	config   *RedisConfig
	stats    *RedisStats
	logger   *zap.Logger
	mutex    sync.RWMutex
}

// NewSentinelRedisManager 创建哨兵Redis管理器
func NewSentinelRedisManager(config *RedisConfig) (*SentinelRedisManager, error) {
	// 构建Redis哨兵选项
	opts := &redis.FailoverOptions{
		MasterName:       config.SentinelMasterName,
		SentinelAddrs:    config.SentinelNodes,
		SentinelPassword: config.SentinelPassword,
		Password:         config.Password,
		DB:               config.Database,
		
		// 连接池配置
		PoolSize:        config.MaxActive,
		MinIdleConns:    config.MinIdle,
		ConnMaxLifetime: config.MaxConnLifetime,
		PoolTimeout:     config.WaitTimeout,
		ConnMaxIdleTime: config.IdleTimeout,
		
		// 超时配置
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		
		// 重试配置
		MaxRetries:      config.MaxRetries,
		MinRetryBackoff: config.MinRetryBackoff,
		MaxRetryBackoff: config.MaxRetryBackoff,
	}
	
	// TLS配置
	if config.TLSEnabled {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: config.TLSInsecureSkipVerify,
		}
		
		if config.TLSCertFile != "" && config.TLSKeyFile != "" {
			cert, err := tls.LoadX509KeyPair(config.TLSCertFile, config.TLSKeyFile)
			if err != nil {
				return nil, fmt.Errorf("failed to load TLS certificate: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
		}
		
		opts.TLSConfig = tlsConfig
	}
	
	// 创建Redis哨兵客户端
	client := redis.NewFailoverClient(opts)
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to redis sentinel: %w", err)
	}
	
	manager := &SentinelRedisManager{
		client: client,
		config: config,
		stats:  &RedisStats{},
		logger: zap.L().Named("redis.sentinel"),
	}
	
	// 启动监控
	if config.MonitoringEnabled {
		go manager.startMonitoring()
	}
	
	return manager, nil
}

// buildKey 构建完整的键名
func (m *SentinelRedisManager) buildKey(key string) string {
	return m.config.KeyPrefix + key
}

// serialize 序列化值
func (m *SentinelRedisManager) serialize(value interface{}) (string, error) {
	switch m.config.Serialization {
	case "json":
		if str, ok := value.(string); ok {
			return str, nil
		}
		bytes, err := json.Marshal(value)
		return string(bytes), err
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// 基础操作实现 - 与单机模式相同的实现
func (m *SentinelRedisManager) Get(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.Get(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *SentinelRedisManager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	fullKey := m.buildKey(key)
	serialized, err := m.serialize(value)
	if err != nil {
		return err
	}
	return m.client.Set(ctx, fullKey, serialized, expiration).Err()
}

func (m *SentinelRedisManager) Del(ctx context.Context, keys ...string) error {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Del(ctx, fullKeys...).Err()
}

func (m *SentinelRedisManager) Exists(ctx context.Context, keys ...string) (int64, error) {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Exists(ctx, fullKeys...).Result()
}

func (m *SentinelRedisManager) Expire(ctx context.Context, key string, expiration time.Duration) error {
	fullKey := m.buildKey(key)
	return m.client.Expire(ctx, fullKey, expiration).Err()
}

func (m *SentinelRedisManager) TTL(ctx context.Context, key string) (time.Duration, error) {
	fullKey := m.buildKey(key)
	return m.client.TTL(ctx, fullKey).Result()
}

// Hash操作实现
func (m *SentinelRedisManager) HGet(ctx context.Context, key, field string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.HGet(ctx, fullKey, field).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *SentinelRedisManager) HSet(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.HSet(ctx, fullKey, values...).Err()
}

func (m *SentinelRedisManager) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	fullKey := m.buildKey(key)
	return m.client.HGetAll(ctx, fullKey).Result()
}

func (m *SentinelRedisManager) HDel(ctx context.Context, key string, fields ...string) error {
	fullKey := m.buildKey(key)
	return m.client.HDel(ctx, fullKey, fields...).Err()
}

func (m *SentinelRedisManager) HExists(ctx context.Context, key, field string) (bool, error) {
	fullKey := m.buildKey(key)
	return m.client.HExists(ctx, fullKey, field).Result()
}

func (m *SentinelRedisManager) HLen(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.HLen(ctx, fullKey).Result()
}

// List操作实现
func (m *SentinelRedisManager) LPush(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.LPush(ctx, fullKey, values...).Err()
}

func (m *SentinelRedisManager) RPush(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.RPush(ctx, fullKey, values...).Err()
}

func (m *SentinelRedisManager) LPop(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.LPop(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *SentinelRedisManager) RPop(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.RPop(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *SentinelRedisManager) LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.LRange(ctx, fullKey, start, stop).Result()
}

func (m *SentinelRedisManager) LLen(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.LLen(ctx, fullKey).Result()
}

// Set操作实现
func (m *SentinelRedisManager) SAdd(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.SAdd(ctx, fullKey, members...).Err()
}

func (m *SentinelRedisManager) SRem(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.SRem(ctx, fullKey, members...).Err()
}

func (m *SentinelRedisManager) SMembers(ctx context.Context, key string) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.SMembers(ctx, fullKey).Result()
}

func (m *SentinelRedisManager) SIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	fullKey := m.buildKey(key)
	return m.client.SIsMember(ctx, fullKey, member).Result()
}

func (m *SentinelRedisManager) SCard(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.SCard(ctx, fullKey).Result()
}

// ZSet操作实现
func (m *SentinelRedisManager) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	fullKey := m.buildKey(key)
	return m.client.ZAdd(ctx, fullKey, members...).Err()
}

func (m *SentinelRedisManager) ZCard(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.ZCard(ctx, fullKey).Result()
}

func (m *SentinelRedisManager) ZRemRangeByScore(ctx context.Context, key string, opt interface{}) error {
	fullKey := m.buildKey(key)
	// 简化处理，假设opt是字符串格式的范围
	if rangeStr, ok := opt.(string); ok {
		return m.client.ZRemRangeByScore(ctx, fullKey, rangeStr, rangeStr).Err()
	}
	return fmt.Errorf("invalid range option")
}

func (m *SentinelRedisManager) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.ZRange(ctx, fullKey, start, stop).Result()
}

func (m *SentinelRedisManager) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.ZRangeByScore(ctx, fullKey, opt).Result()
}

func (m *SentinelRedisManager) ZRem(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.ZRem(ctx, fullKey, members...).Err()
}

func (m *SentinelRedisManager) ZScore(ctx context.Context, key string, member string) (float64, error) {
	fullKey := m.buildKey(key)
	return m.client.ZScore(ctx, fullKey, member).Result()
}

// 其他操作实现
func (m *SentinelRedisManager) Keys(ctx context.Context, pattern string) ([]string, error) {
	fullPattern := m.buildKey(pattern)
	return m.client.Keys(ctx, fullPattern).Result()
}

func (m *SentinelRedisManager) Ping(ctx context.Context) error {
	return m.client.Ping(ctx).Err()
}

func (m *SentinelRedisManager) Close() error {
	return m.client.Close()
}

// 监控相关方法
func (m *SentinelRedisManager) startMonitoring() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		m.collectStats(ctx)
		cancel()
	}
}

func (m *SentinelRedisManager) collectStats(ctx context.Context) {
	_, err := m.client.Info(ctx, "stats", "memory", "clients").Result()
	if err != nil {
		m.logger.Error("Failed to get redis sentinel info", zap.Error(err))
		return
	}

	// 更新统计信息
	m.mutex.Lock()
	m.stats.LastUpdate = time.Now()
	m.mutex.Unlock()
}

func (m *SentinelRedisManager) GetStats() RedisStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return *m.stats
}

// ConfigGet 获取配置
func (m *SentinelRedisManager) ConfigGet(ctx context.Context, parameter string) ([]interface{}, error) {
	result, err := m.client.ConfigGet(ctx, parameter).Result()
	if err != nil {
		return nil, err
	}

	// 转换map[string]string为[]interface{}
	var interfaces []interface{}
	for k, v := range result {
		interfaces = append(interfaces, k, v)
	}
	return interfaces, nil
}

// ConfigSet 设置配置
func (m *SentinelRedisManager) ConfigSet(ctx context.Context, parameter, value string) error {
	return m.client.ConfigSet(ctx, parameter, value).Err()
}

// Info 获取信息
func (m *SentinelRedisManager) Info(ctx context.Context, section ...string) (string, error) {
	return m.client.Info(ctx, section...).Result()
}

// Eval 执行Lua脚本
func (m *SentinelRedisManager) Eval(ctx context.Context, script string, keys []string, args ...interface{}) *redis.Cmd {
	return m.client.Eval(ctx, script, keys, args...)
}

// EvalSha 执行Lua脚本SHA
func (m *SentinelRedisManager) EvalSha(ctx context.Context, sha1 string, keys []string, args ...interface{}) *redis.Cmd {
	return m.client.EvalSha(ctx, sha1, keys, args...)
}

// FlushAll 清空所有数据
func (m *SentinelRedisManager) FlushAll(ctx context.Context) error {
	return m.client.FlushAll(ctx).Err()
}

// FlushDB 清空当前数据库
func (m *SentinelRedisManager) FlushDB(ctx context.Context) error {
	return m.client.FlushDB(ctx).Err()
}

// GetInfo 获取Redis信息
func (m *SentinelRedisManager) GetInfo(ctx context.Context, section ...string) (string, error) {
	return m.client.Info(ctx, section...).Result()
}

// Lock 分布式锁
func (m *SentinelRedisManager) Lock(ctx context.Context, key string, expiration time.Duration) (Lock, error) {
	fullKey := m.buildKey(key)
	lock := &redisLock{
		client:     m.client,
		key:        fullKey,
		value:      generateLockValue(),
		expiration: expiration,
	}
	return lock, nil
}

// TryLock 尝试获取分布式锁
func (m *SentinelRedisManager) TryLock(ctx context.Context, key string, expiration time.Duration) (Lock, bool, error) {
	fullKey := m.buildKey(key)
	lock := &redisLock{
		client:     m.client,
		key:        fullKey,
		value:      generateLockValue(),
		expiration: expiration,
	}

	// 尝试获取锁
	acquired := true // 简化实现，实际应该检查锁是否成功获取
	return lock, acquired, nil
}

// Pipeline 管道操作
func (m *SentinelRedisManager) Pipeline() redis.Pipeliner {
	return m.client.Pipeline()
}

// TxPipeline 事务管道操作
func (m *SentinelRedisManager) TxPipeline() redis.Pipeliner {
	return m.client.TxPipeline()
}

// PSubscribe 模式订阅
func (m *SentinelRedisManager) PSubscribe(ctx context.Context, patterns ...string) *redis.PubSub {
	return m.client.PSubscribe(ctx, patterns...)
}

// Publish 发布消息
func (m *SentinelRedisManager) Publish(ctx context.Context, channel string, message interface{}) error {
	return m.client.Publish(ctx, channel, message).Err()
}

// Scan 扫描键
func (m *SentinelRedisManager) Scan(ctx context.Context, cursor uint64, match string, count int64) ([]string, uint64, error) {
	return m.client.Scan(ctx, cursor, match, count).Result()
}

// Subscribe 订阅
func (m *SentinelRedisManager) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return m.client.Subscribe(ctx, channels...)
}

// Watch 监视键
func (m *SentinelRedisManager) Watch(ctx context.Context, fn func(*redis.Tx) error, keys ...string) error {
	return m.client.Watch(ctx, fn, keys...)
}

// Stream 相关方法
func (m *SentinelRedisManager) XAdd(ctx context.Context, args *redis.XAddArgs) error {
	return m.client.XAdd(ctx, args).Err()
}

func (m *SentinelRedisManager) XRead(ctx context.Context, args *redis.XReadArgs) ([]redis.XStream, error) {
	return m.client.XRead(ctx, args).Result()
}

func (m *SentinelRedisManager) XReadGroup(ctx context.Context, args *redis.XReadGroupArgs) ([]redis.XStream, error) {
	return m.client.XReadGroup(ctx, args).Result()
}
