package redis

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// StandaloneRedisManager 单机Redis管理器
type StandaloneRedisManager struct {
	client   *redis.Client
	config   *RedisConfig
	stats    *RedisStats
	logger   *zap.Logger
	mutex    sync.RWMutex
}

// NewStandaloneRedisManager 创建单机Redis管理器
func NewStandaloneRedisManager(config *RedisConfig) (*StandaloneRedisManager, error) {
	// 构建Redis选项
	opts := &redis.Options{
		Addr:         config.Nodes[0],
		Password:     config.Password,
		DB:           config.Database,
		
		// 连接池配置
		PoolSize:        config.MaxActive,
		MinIdleConns:    config.MinIdle,
		ConnMaxLifetime: config.MaxConnLifetime,
		PoolTimeout:     config.WaitTimeout,
		ConnMaxIdleTime: config.IdleTimeout,
		
		// 超时配置
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
		
		// 重试配置
		MaxRetries:      config.MaxRetries,
		MinRetryBackoff: config.MinRetryBackoff,
		MaxRetryBackoff: config.MaxRetryBackoff,
	}
	
	// TLS配置
	if config.TLSEnabled {
		tlsConfig := &tls.Config{
			InsecureSkipVerify: config.TLSInsecureSkipVerify,
		}
		
		if config.TLSCertFile != "" && config.TLSKeyFile != "" {
			cert, err := tls.LoadX509KeyPair(config.TLSCertFile, config.TLSKeyFile)
			if err != nil {
				return nil, fmt.Errorf("failed to load TLS certificate: %w", err)
			}
			tlsConfig.Certificates = []tls.Certificate{cert}
		}
		
		opts.TLSConfig = tlsConfig
	}
	
	// 创建Redis客户端
	client := redis.NewClient(opts)
	
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		return nil, fmt.Errorf("failed to connect to redis: %w", err)
	}
	
	manager := &StandaloneRedisManager{
		client: client,
		config: config,
		stats:  &RedisStats{},
		logger: zap.L().Named("redis.standalone"),
	}
	
	// 启动监控
	if config.MonitoringEnabled {
		go manager.startMonitoring()
	}
	
	return manager, nil
}

// buildKey 构建完整的键名
func (m *StandaloneRedisManager) buildKey(key string) string {
	return m.config.KeyPrefix + key
}

// serialize 序列化值
func (m *StandaloneRedisManager) serialize(value interface{}) (string, error) {
	switch m.config.Serialization {
	case "json":
		if str, ok := value.(string); ok {
			return str, nil
		}
		bytes, err := json.Marshal(value)
		return string(bytes), err
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// 基础操作实现
func (m *StandaloneRedisManager) Get(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.Get(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *StandaloneRedisManager) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	fullKey := m.buildKey(key)
	serialized, err := m.serialize(value)
	if err != nil {
		return err
	}
	return m.client.Set(ctx, fullKey, serialized, expiration).Err()
}

func (m *StandaloneRedisManager) Del(ctx context.Context, keys ...string) error {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Del(ctx, fullKeys...).Err()
}

func (m *StandaloneRedisManager) Exists(ctx context.Context, keys ...string) (int64, error) {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Exists(ctx, fullKeys...).Result()
}

func (m *StandaloneRedisManager) Expire(ctx context.Context, key string, expiration time.Duration) error {
	fullKey := m.buildKey(key)
	return m.client.Expire(ctx, fullKey, expiration).Err()
}

func (m *StandaloneRedisManager) TTL(ctx context.Context, key string) (time.Duration, error) {
	fullKey := m.buildKey(key)
	return m.client.TTL(ctx, fullKey).Result()
}

// Hash操作实现
func (m *StandaloneRedisManager) HGet(ctx context.Context, key, field string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.HGet(ctx, fullKey, field).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *StandaloneRedisManager) HSet(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.HSet(ctx, fullKey, values...).Err()
}

func (m *StandaloneRedisManager) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	fullKey := m.buildKey(key)
	return m.client.HGetAll(ctx, fullKey).Result()
}

func (m *StandaloneRedisManager) HDel(ctx context.Context, key string, fields ...string) error {
	fullKey := m.buildKey(key)
	return m.client.HDel(ctx, fullKey, fields...).Err()
}

func (m *StandaloneRedisManager) HExists(ctx context.Context, key, field string) (bool, error) {
	fullKey := m.buildKey(key)
	return m.client.HExists(ctx, fullKey, field).Result()
}

func (m *StandaloneRedisManager) HLen(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.HLen(ctx, fullKey).Result()
}

// List操作实现
func (m *StandaloneRedisManager) LPush(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.LPush(ctx, fullKey, values...).Err()
}

func (m *StandaloneRedisManager) RPush(ctx context.Context, key string, values ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.RPush(ctx, fullKey, values...).Err()
}

func (m *StandaloneRedisManager) LPop(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.LPop(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *StandaloneRedisManager) RPop(ctx context.Context, key string) (string, error) {
	fullKey := m.buildKey(key)
	result, err := m.client.RPop(ctx, fullKey).Result()
	if err == redis.Nil {
		return "", ErrKeyNotFound
	}
	return result, err
}

func (m *StandaloneRedisManager) LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.LRange(ctx, fullKey, start, stop).Result()
}

func (m *StandaloneRedisManager) LLen(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.LLen(ctx, fullKey).Result()
}

// Set操作实现
func (m *StandaloneRedisManager) SAdd(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.SAdd(ctx, fullKey, members...).Err()
}

func (m *StandaloneRedisManager) SRem(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.SRem(ctx, fullKey, members...).Err()
}

func (m *StandaloneRedisManager) SMembers(ctx context.Context, key string) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.SMembers(ctx, fullKey).Result()
}

func (m *StandaloneRedisManager) SIsMember(ctx context.Context, key string, member interface{}) (bool, error) {
	fullKey := m.buildKey(key)
	return m.client.SIsMember(ctx, fullKey, member).Result()
}

func (m *StandaloneRedisManager) SCard(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.SCard(ctx, fullKey).Result()
}

// Sorted Set操作实现
func (m *StandaloneRedisManager) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	fullKey := m.buildKey(key)
	return m.client.ZAdd(ctx, fullKey, members...).Err()
}

func (m *StandaloneRedisManager) ZRem(ctx context.Context, key string, members ...interface{}) error {
	fullKey := m.buildKey(key)
	return m.client.ZRem(ctx, fullKey, members...).Err()
}

func (m *StandaloneRedisManager) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.ZRange(ctx, fullKey, start, stop).Result()
}

func (m *StandaloneRedisManager) ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error) {
	fullKey := m.buildKey(key)
	return m.client.ZRangeByScore(ctx, fullKey, opt).Result()
}

func (m *StandaloneRedisManager) ZRemRangeByScore(ctx context.Context, key string, opt interface{}) error {
	fullKey := m.buildKey(key)
	// 简化处理，假设opt是字符串格式的范围
	if rangeStr, ok := opt.(string); ok {
		return m.client.ZRemRangeByScore(ctx, fullKey, rangeStr, rangeStr).Err()
	}
	return fmt.Errorf("invalid range option")
}

func (m *StandaloneRedisManager) ZCard(ctx context.Context, key string) (int64, error) {
	fullKey := m.buildKey(key)
	return m.client.ZCard(ctx, fullKey).Result()
}

func (m *StandaloneRedisManager) ZScore(ctx context.Context, key, member string) (float64, error) {
	fullKey := m.buildKey(key)
	return m.client.ZScore(ctx, fullKey, member).Result()
}

// 发布订阅实现
func (m *StandaloneRedisManager) Publish(ctx context.Context, channel string, message interface{}) error {
	serialized, err := m.serialize(message)
	if err != nil {
		return err
	}
	return m.client.Publish(ctx, channel, serialized).Err()
}

func (m *StandaloneRedisManager) Subscribe(ctx context.Context, channels ...string) *redis.PubSub {
	return m.client.Subscribe(ctx, channels...)
}

func (m *StandaloneRedisManager) PSubscribe(ctx context.Context, patterns ...string) *redis.PubSub {
	return m.client.PSubscribe(ctx, patterns...)
}

// Stream操作实现
func (m *StandaloneRedisManager) XAdd(ctx context.Context, args *redis.XAddArgs) error {
	args.Stream = m.buildKey(args.Stream)
	return m.client.XAdd(ctx, args).Err()
}

func (m *StandaloneRedisManager) XRead(ctx context.Context, args *redis.XReadArgs) ([]redis.XStream, error) {
	// 为所有stream添加前缀
	for i, stream := range args.Streams {
		if i%2 == 0 { // 偶数索引是stream名称
			args.Streams[i] = m.buildKey(stream)
		}
	}
	return m.client.XRead(ctx, args).Result()
}

func (m *StandaloneRedisManager) XReadGroup(ctx context.Context, args *redis.XReadGroupArgs) ([]redis.XStream, error) {
	// 为所有stream添加前缀
	for i, stream := range args.Streams {
		if i%2 == 0 { // 偶数索引是stream名称
			args.Streams[i] = m.buildKey(stream)
		}
	}
	return m.client.XReadGroup(ctx, args).Result()
}

// 分布式锁实现
func (m *StandaloneRedisManager) Lock(ctx context.Context, key string, expiration time.Duration) (Lock, error) {
	lockKey := m.buildKey(fmt.Sprintf("lock:%s", key))
	lockValue := generateLockValue()

	success, err := m.client.SetNX(ctx, lockKey, lockValue, expiration).Result()
	if err != nil {
		return nil, err
	}

	if !success {
		return nil, ErrLockAcquisitionFailed
	}

	return &redisLock{
		client:     m.client,
		key:        lockKey,
		value:      lockValue,
		expiration: expiration,
	}, nil
}

func (m *StandaloneRedisManager) TryLock(ctx context.Context, key string, expiration time.Duration) (Lock, bool, error) {
	lock, err := m.Lock(ctx, key, expiration)
	if err == ErrLockAcquisitionFailed {
		return nil, false, nil
	}
	if err != nil {
		return nil, false, err
	}
	return lock, true, nil
}

// 管道操作实现
func (m *StandaloneRedisManager) Pipeline() redis.Pipeliner {
	return m.client.Pipeline()
}

func (m *StandaloneRedisManager) TxPipeline() redis.Pipeliner {
	return m.client.TxPipeline()
}

// 事务操作实现
func (m *StandaloneRedisManager) Watch(ctx context.Context, fn func(*redis.Tx) error, keys ...string) error {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Watch(ctx, fn, fullKeys...)
}

// Lua脚本实现
func (m *StandaloneRedisManager) Eval(ctx context.Context, script string, keys []string, args ...interface{}) *redis.Cmd {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.Eval(ctx, script, fullKeys, args...)
}

func (m *StandaloneRedisManager) EvalSha(ctx context.Context, sha1 string, keys []string, args ...interface{}) *redis.Cmd {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = m.buildKey(key)
	}
	return m.client.EvalSha(ctx, sha1, fullKeys, args...)
}

// 管理功能实现
func (m *StandaloneRedisManager) Ping(ctx context.Context) error {
	return m.client.Ping(ctx).Err()
}

func (m *StandaloneRedisManager) GetStats() RedisStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return *m.stats
}

func (m *StandaloneRedisManager) GetInfo(ctx context.Context, section ...string) (string, error) {
	return m.client.Info(ctx, section...).Result()
}

func (m *StandaloneRedisManager) Close() error {
	return m.client.Close()
}

func (m *StandaloneRedisManager) ConfigGet(ctx context.Context, parameter string) ([]interface{}, error) {
	result, err := m.client.ConfigGet(ctx, parameter).Result()
	if err != nil {
		return nil, err
	}

	// 转换map[string]string为[]interface{}
	var interfaces []interface{}
	for k, v := range result {
		interfaces = append(interfaces, k, v)
	}
	return interfaces, nil
}

func (m *StandaloneRedisManager) ConfigSet(ctx context.Context, parameter, value string) error {
	return m.client.ConfigSet(ctx, parameter, value).Err()
}

func (m *StandaloneRedisManager) FlushDB(ctx context.Context) error {
	return m.client.FlushDB(ctx).Err()
}

func (m *StandaloneRedisManager) FlushAll(ctx context.Context) error {
	return m.client.FlushAll(ctx).Err()
}

func (m *StandaloneRedisManager) Keys(ctx context.Context, pattern string) ([]string, error) {
	fullPattern := m.buildKey(pattern)
	return m.client.Keys(ctx, fullPattern).Result()
}

func (m *StandaloneRedisManager) Scan(ctx context.Context, cursor uint64, match string, count int64) ([]string, uint64, error) {
	fullMatch := m.buildKey(match)
	return m.client.Scan(ctx, cursor, fullMatch, count).Result()
}

// startMonitoring 启动监控
func (m *StandaloneRedisManager) startMonitoring() {
	ticker := time.NewTicker(m.config.StatsInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.updateStats()
	}
}

// updateStats 更新统计信息
func (m *StandaloneRedisManager) updateStats() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := m.client.Info(ctx, "stats", "memory", "clients").Result()
	if err != nil {
		m.logger.Error("Failed to get redis info", zap.Error(err))
		return
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 解析info信息并更新stats
	// 这里简化处理，实际应该解析info字符串
	m.stats.LastUpdate = time.Now()
}
