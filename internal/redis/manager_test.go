package redis

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"kubeops/internal/config"
)

func TestRedisConfigValidation(t *testing.T) {
	tests := []struct {
		name        string
		config      *RedisConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid standalone config",
			config: &RedisConfig{
				Mode:      "standalone",
				Nodes:     []string{"localhost:6379"},
				MaxActive: 100,
				MaxIdle:   20,
			},
			expectError: false,
		},
		{
			name: "valid cluster config",
			config: &RedisConfig{
				Mode:         "cluster",
				Nodes:        []string{"node1:6379", "node2:6379", "node3:6379"},
				ClusterNodes: []string{"node1:6379", "node2:6379", "node3:6379"},
				MaxActive:    100,
				MaxIdle:      20,
			},
			expectError: false,
		},
		{
			name: "invalid cluster config - insufficient nodes",
			config: &RedisConfig{
				Mode:         "cluster",
				Nodes:        []string{"node1:6379"},
				ClusterNodes: []string{"node1:6379"},
				MaxActive:    100,
				MaxIdle:      20,
			},
			expectError: true,
			errorMsg:    "cluster mode requires at least 3 nodes",
		},
		{
			name: "invalid sentinel config - no master name",
			config: &RedisConfig{
				Mode:          "sentinel",
				Nodes:         []string{"localhost:6379"},
				SentinelNodes: []string{"sentinel1:26379", "sentinel2:26379", "sentinel3:26379"},
				MaxActive:     100,
				MaxIdle:       20,
			},
			expectError: true,
			errorMsg:    "sentinel master name is required",
		},
		{
			name: "invalid max idle greater than max active",
			config: &RedisConfig{
				Mode:      "standalone",
				Nodes:     []string{"localhost:6379"},
				MaxActive: 50,
				MaxIdle:   100,
			},
			expectError: true,
			errorMsg:    "max idle connections cannot exceed max active connections",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateRedisConfig(tt.config)
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestBuildRedisConfig(t *testing.T) {
	cfg := &config.Config{
		Redis: struct {
			Global struct {
				Mode          string            `yaml:"mode"`
				Nodes         []string          `yaml:"nodes"`
				Password      string            `yaml:"password"`
				Database      int               `yaml:"database"`
				KeyPrefix     string            `yaml:"key_prefix"`
				Serialization string            `yaml:"serialization"`
				Compression   config.CompressionConfig `yaml:"compression"`
			} `yaml:"global"`

			Cluster struct {
				Nodes            []string `yaml:"nodes"`
				MaxRedirects     int      `yaml:"max_redirects"`
				ReadOnly         bool     `yaml:"read_only"`
				RouteByLatency   bool     `yaml:"route_by_latency"`
				RouteRandomly    bool     `yaml:"route_randomly"`
			} `yaml:"cluster"`

			Sentinel struct {
				MasterName       string   `yaml:"master_name"`
				Nodes            []string `yaml:"nodes"`
				SentinelPassword string   `yaml:"sentinel_password"`
			} `yaml:"sentinel"`

			Pool struct {
				MaxIdle         int           `yaml:"max_idle"`
				MaxActive       int           `yaml:"max_active"`
				IdleTimeout     time.Duration `yaml:"idle_timeout"`
				MaxConnLifetime time.Duration `yaml:"max_conn_lifetime"`
				WaitTimeout     time.Duration `yaml:"wait_timeout"`
				MinIdle         int           `yaml:"min_idle"`
			} `yaml:"pool"`

			Timeout struct {
				Dial               time.Duration `yaml:"dial"`
				Read               time.Duration `yaml:"read"`
				Write              time.Duration `yaml:"write"`
				IdleCheckFrequency time.Duration `yaml:"idle_check_frequency"`
			} `yaml:"timeout"`

			Retry struct {
				MaxRetries      int           `yaml:"max_retries"`
				MinRetryBackoff time.Duration `yaml:"min_retry_backoff"`
				MaxRetryBackoff time.Duration `yaml:"max_retry_backoff"`
			} `yaml:"retry"`

			Monitoring struct {
				Enabled             bool          `yaml:"enabled"`
				SlowQueryThreshold  time.Duration `yaml:"slow_query_threshold"`
				StatsInterval       time.Duration `yaml:"stats_interval"`
				HealthCheckInterval time.Duration `yaml:"health_check_interval"`
			} `yaml:"monitoring"`

			Security struct {
				TLSEnabled bool      `yaml:"tls_enabled"`
				TLSConfig  config.TLSConfig `yaml:"tls_config"`
			} `yaml:"security"`
		}{
			Global: struct {
				Mode          string            `yaml:"mode"`
				Nodes         []string          `yaml:"nodes"`
				Password      string            `yaml:"password"`
				Database      int               `yaml:"database"`
				KeyPrefix     string            `yaml:"key_prefix"`
				Serialization string            `yaml:"serialization"`
				Compression   config.CompressionConfig `yaml:"compression"`
			}{
				Mode:          "standalone",
				Nodes:         []string{"localhost:6379"},
				Password:      "test-password",
				Database:      0,
				KeyPrefix:     "test:",
				Serialization: "json",
			},
			Pool: struct {
				MaxIdle         int           `yaml:"max_idle"`
				MaxActive       int           `yaml:"max_active"`
				IdleTimeout     time.Duration `yaml:"idle_timeout"`
				MaxConnLifetime time.Duration `yaml:"max_conn_lifetime"`
				WaitTimeout     time.Duration `yaml:"wait_timeout"`
				MinIdle         int           `yaml:"min_idle"`
			}{
				MaxActive:       100,
				MaxIdle:         20,
				IdleTimeout:     300 * time.Second,
				MaxConnLifetime: 3600 * time.Second,
				WaitTimeout:     5 * time.Second,
				MinIdle:         5,
			},
			Timeout: struct {
				Dial               time.Duration `yaml:"dial"`
				Read               time.Duration `yaml:"read"`
				Write              time.Duration `yaml:"write"`
				IdleCheckFrequency time.Duration `yaml:"idle_check_frequency"`
			}{
				Dial:               5 * time.Second,
				Read:               3 * time.Second,
				Write:              3 * time.Second,
				IdleCheckFrequency: 60 * time.Second,
			},
		},
	}

	redisConfig := buildRedisConfig(cfg)

	assert.Equal(t, "standalone", redisConfig.Mode)
	assert.Equal(t, []string{"localhost:6379"}, redisConfig.Nodes)
	assert.Equal(t, "test-password", redisConfig.Password)
	assert.Equal(t, "test:", redisConfig.KeyPrefix)
	assert.Equal(t, "json", redisConfig.Serialization)
	assert.Equal(t, 100, redisConfig.MaxActive)
	assert.Equal(t, 20, redisConfig.MaxIdle)
	assert.Equal(t, 5*time.Second, redisConfig.DialTimeout)
}

// 注意：以下测试需要实际的Redis实例，在CI/CD环境中可能需要跳过
func TestRedisManagerIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// 创建测试配置
	cfg := &config.Config{}
	// 设置默认值
	cfg.Redis.Global.Mode = "standalone"
	cfg.Redis.Global.Nodes = []string{"localhost:6379"}
	cfg.Redis.Global.KeyPrefix = "test:"
	cfg.Redis.Pool.MaxActive = 10
	cfg.Redis.Pool.MaxIdle = 5

	// 尝试创建Redis管理器
	manager, err := NewRedisManager(cfg)
	if err != nil {
		t.Skipf("Skipping Redis integration test: %v", err)
		return
	}
	defer manager.Close()

	ctx := context.Background()

	// 测试基础操作
	t.Run("basic operations", func(t *testing.T) {
		key := "test:key"
		value := "test-value"

		// 设置值
		err := manager.Set(ctx, key, value, time.Minute)
		require.NoError(t, err)

		// 获取值
		result, err := manager.Get(ctx, key)
		require.NoError(t, err)
		assert.Equal(t, value, result)

		// 检查存在性
		exists, err := manager.Exists(ctx, key)
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists)

		// 删除键
		err = manager.Del(ctx, key)
		require.NoError(t, err)

		// 验证删除
		exists, err = manager.Exists(ctx, key)
		require.NoError(t, err)
		assert.Equal(t, int64(0), exists)
	})

	// 测试健康检查
	t.Run("health check", func(t *testing.T) {
		err := manager.Ping(ctx)
		assert.NoError(t, err)
	})
}
