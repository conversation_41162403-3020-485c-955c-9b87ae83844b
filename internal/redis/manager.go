package redis

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

// RedisManager Redis管理器核心接口
type RedisManager interface {
	// 基础操作接口
	BasicOperations
	
	// 数据结构操作接口
	DataStructureOperations
	
	// 高级功能接口
	AdvancedOperations
	
	// 管理功能接口
	ManagementOperations
}

// BasicOperations 基础操作接口
type BasicOperations interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Del(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, keys ...string) (int64, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	TTL(ctx context.Context, key string) (time.Duration, error)
}

// DataStructureOperations 数据结构操作接口
type DataStructureOperations interface {
	// Hash操作
	HGet(ctx context.Context, key, field string) (string, error)
	HSet(ctx context.Context, key string, values ...interface{}) error
	HGetAll(ctx context.Context, key string) (map[string]string, error)
	HDel(ctx context.Context, key string, fields ...string) error
	HExists(ctx context.Context, key, field string) (bool, error)
	HLen(ctx context.Context, key string) (int64, error)
	
	// List操作
	LPush(ctx context.Context, key string, values ...interface{}) error
	RPush(ctx context.Context, key string, values ...interface{}) error
	LPop(ctx context.Context, key string) (string, error)
	RPop(ctx context.Context, key string) (string, error)
	LRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	LLen(ctx context.Context, key string) (int64, error)
	
	// Set操作
	SAdd(ctx context.Context, key string, members ...interface{}) error
	SRem(ctx context.Context, key string, members ...interface{}) error
	SMembers(ctx context.Context, key string) ([]string, error)
	SIsMember(ctx context.Context, key string, member interface{}) (bool, error)
	SCard(ctx context.Context, key string) (int64, error)
	
	// Sorted Set操作
	ZAdd(ctx context.Context, key string, members ...redis.Z) error
	ZRem(ctx context.Context, key string, members ...interface{}) error
	ZRange(ctx context.Context, key string, start, stop int64) ([]string, error)
	ZRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) ([]string, error)
	ZRemRangeByScore(ctx context.Context, key string, opt interface{}) error
	ZCard(ctx context.Context, key string) (int64, error)
	ZScore(ctx context.Context, key, member string) (float64, error)
}

// AdvancedOperations 高级功能接口
type AdvancedOperations interface {
	// 发布订阅
	Publish(ctx context.Context, channel string, message interface{}) error
	Subscribe(ctx context.Context, channels ...string) *redis.PubSub
	PSubscribe(ctx context.Context, patterns ...string) *redis.PubSub
	
	// Stream操作
	XAdd(ctx context.Context, args *redis.XAddArgs) error
	XRead(ctx context.Context, args *redis.XReadArgs) ([]redis.XStream, error)
	XReadGroup(ctx context.Context, args *redis.XReadGroupArgs) ([]redis.XStream, error)
	
	// 分布式锁
	Lock(ctx context.Context, key string, expiration time.Duration) (Lock, error)
	TryLock(ctx context.Context, key string, expiration time.Duration) (Lock, bool, error)
	
	// 管道操作
	Pipeline() redis.Pipeliner
	TxPipeline() redis.Pipeliner
	
	// 事务操作
	Watch(ctx context.Context, fn func(*redis.Tx) error, keys ...string) error
	
	// Lua脚本
	Eval(ctx context.Context, script string, keys []string, args ...interface{}) *redis.Cmd
	EvalSha(ctx context.Context, sha1 string, keys []string, args ...interface{}) *redis.Cmd
}

// ManagementOperations 管理功能接口
type ManagementOperations interface {
	// 健康检查
	Ping(ctx context.Context) error
	
	// 统计信息
	GetStats() RedisStats
	GetInfo(ctx context.Context, section ...string) (string, error)
	
	// 连接管理
	Close() error
	
	// 配置管理
	ConfigGet(ctx context.Context, parameter string) ([]interface{}, error)
	ConfigSet(ctx context.Context, parameter, value string) error
	
	// 数据库管理
	FlushDB(ctx context.Context) error
	FlushAll(ctx context.Context) error
	
	// 键管理
	Keys(ctx context.Context, pattern string) ([]string, error)
	Scan(ctx context.Context, cursor uint64, match string, count int64) ([]string, uint64, error)
}

// Lock 分布式锁接口
type Lock interface {
	Release(ctx context.Context) error
	Refresh(ctx context.Context, expiration time.Duration) error
	TTL(ctx context.Context) (time.Duration, error)
}

// RedisStats Redis统计信息
type RedisStats struct {
	ConnectedClients int64     `json:"connected_clients"`
	UsedMemory       int64     `json:"used_memory"`
	TotalCommands    int64     `json:"total_commands"`
	HitRate          float64   `json:"hit_rate"`
	Uptime           int64     `json:"uptime"`
	LastUpdate       time.Time `json:"last_update"`
}

// RedisConfig Redis配置结构
type RedisConfig struct {
	Mode          string
	Nodes         []string
	Password      string
	Database      int
	KeyPrefix     string
	Serialization string
	
	// 连接池配置
	MaxActive       int
	MaxIdle         int
	IdleTimeout     time.Duration
	MaxConnLifetime time.Duration
	WaitTimeout     time.Duration
	MinIdle         int
	
	// 超时配置
	DialTimeout            time.Duration
	ReadTimeout            time.Duration
	WriteTimeout           time.Duration
	IdleCheckFrequency     time.Duration
	
	// 重试配置
	MaxRetries      int
	MinRetryBackoff time.Duration
	MaxRetryBackoff time.Duration
	
	// 监控配置
	MonitoringEnabled      bool
	SlowQueryThreshold     time.Duration
	StatsInterval          time.Duration
	HealthCheckInterval    time.Duration
	
	// 集群配置
	ClusterNodes       []string
	MaxRedirects       int
	ReadOnly           bool
	RouteByLatency     bool
	RouteRandomly      bool
	
	// 哨兵配置
	SentinelMasterName string
	SentinelNodes      []string
	SentinelPassword   string
	
	// TLS配置
	TLSEnabled         bool
	TLSCertFile        string
	TLSKeyFile         string
	TLSCAFile          string
	TLSInsecureSkipVerify bool
}

// 错误定义
var (
	ErrKeyNotFound            = redis.Nil
	ErrLockAcquisitionFailed  = redis.Nil
	ErrConnectionFailed       = redis.Nil
)
