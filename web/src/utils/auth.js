import Cookies from 'js-cookie'
import axios from 'axios'
import store from '@/store'

const TokenKey = 'kubeops_token'
const TokenExpireKey = 'kubeops_token_expire'

// 获取令牌
export function getToken() {
  // 优先从localStorage获取，其次从Cookie获取
  const token = localStorage.getItem(TokenKey) || Cookies.get(TokenKey)
  return token
}

// 设置令牌
export function setToken(token, expires) {
  if (!token) {
    console.error('尝试设置空token')
    return
  }

  console.log('设置token:', token.substr(0, 20) + '...')

  // 同时存储到localStorage和Cookie
  localStorage.setItem(TokenKey, token)

  // 如果提供了过期时间，也存储过期时间
  if (expires) {
    localStorage.setItem(TokenExpireKey, expires)
  }

  // 设置Cookie，如果有过期时间则设置过期时间
  const cookieOptions = {}
  if (expires) {
    // 计算过期时间与当前时间的差值（秒）
    const expiresDate = new Date(expires)
    const expireSeconds = Math.floor((expiresDate.getTime() - Date.now()) / 1000)
    if (expireSeconds > 0) {
      cookieOptions.expires = expireSeconds / 86400 // 转换为天
    }
  }

  return Cookies.set(TokenKey, token, cookieOptions)
}

// 移除令牌
export function removeToken() {
  // 同时从localStorage和Cookie中移除
  localStorage.removeItem(TokenKey)
  localStorage.removeItem(TokenExpireKey)
  return Cookies.remove(TokenKey)
}

// 检查令牌是否即将过期（如果剩余时间不足30%则认为即将过期）
export function isTokenExpiringSoon() {
  const expireStr = localStorage.getItem(TokenExpireKey)
  if (!expireStr) return false

  const expireTime = new Date(expireStr).getTime()
  const now = Date.now()

  // 如果已过期，返回true
  if (expireTime <= now) return true

  // 获取令牌的总有效期
  const token = getToken()
  if (!token) return true

  // 解析JWT令牌获取签发时间
  try {
    const tokenParts = token.split('.')
    if (tokenParts.length !== 3) return true

    const payload = JSON.parse(atob(tokenParts[1]))
    const issuedAt = payload.iat ? payload.iat * 1000 : now // 签发时间（毫秒）

    // 计算总有效期和剩余有效期
    const totalValidity = expireTime - issuedAt
    const remainingTime = expireTime - now

    // 如果剩余时间不足总有效期的30%，则认为即将过期
    return remainingTime < totalValidity * 0.3
  } catch (e) {
    console.error('解析JWT令牌失败:', e)
    return true
  }
}

// 刷新令牌
export async function refreshToken() {
  try {
    const response = await axios.post('/api/v1/auth/refresh')
    const { access_token, expires_at } = response.data.data

    if (access_token) {
      setToken(access_token, expires_at)
      // 更新store中的token
      if (store && store.commit) {
        store.commit('user/SET_TOKEN', access_token)
      }
      return access_token
    }
    return null
  } catch (error) {
    console.error('刷新令牌失败:', error)
    return null
  }
}

// 登出
export async function logout() {
  try {
    await axios.post('/api/v1/auth/logout')
  } catch (error) {
    console.error('登出请求失败:', error)
  } finally {
    // 无论请求成功与否，都清除本地令牌
    removeToken()
  }
}
