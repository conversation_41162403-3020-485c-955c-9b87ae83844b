import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken, isTokenExpiringSoon, refreshToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: 'http://localhost:8080', // 修改为8080端口
  withCredentials: false, // 默认不发送cookies
  timeout: 10000 // 增大请求超时时间
})

// 是否正在刷新令牌
let isRefreshing = false
// 等待令牌刷新的请求队列
let requests = []

// request interceptor
service.interceptors.request.use(
  async config => {
    // 检查是否需要跳过令牌处理（如登录、刷新令牌请求）
    const skipTokenUrls = ['/api/v1/auth/login', '/api/v1/auth/refresh', '/api/v1/auth/logout']
    
    // 处理API路径前缀问题
    if (config.url && config.url.startsWith('/api')) {
      // 替换/api前缀为实际后端API路径
      config.url = config.url
      console.log('发送请求到:', config.url)
    }
    
    const skipToken = skipTokenUrls.some(url => config.url.includes(url))
    
    if (!skipToken) {
      // 获取令牌
      let token = getToken()
      
      // 如果有令牌且令牌即将过期，尝试刷新令牌
      if (token && isTokenExpiringSoon() && !isRefreshing) {
        isRefreshing = true
        
        try {
          // 刷新令牌
          const newToken = await refreshToken()
          if (newToken) {
            token = newToken
          }
          
          // 处理队列中的请求
          requests.forEach(cb => cb(token))
          requests = []
        } catch (err) {
          console.error('刷新令牌失败:', err)
        } finally {
          isRefreshing = false
        }
      }
      
      // 如果令牌正在刷新中，将请求加入队列
      if (token && isRefreshing) {
        return new Promise(resolve => {
          requests.push(newToken => {
            config.headers['Authorization'] = 'Bearer ' + newToken
            resolve(config)
          })
        })
      }
      
      // 如果有令牌，添加到请求头
      if (token) {
        config.headers['Authorization'] = 'Bearer ' + token
      }
    }
    
    return config
  },
  error => {
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    console.log('请求URL:', response.config.url)
    console.log('请求方法:', response.config.method)
    console.log('请求参数:', response.config.params || response.config.data)
    console.log('响应状态码:', response.status)
    console.log('响应数据:', response.data)
    
    const res = response.data

    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 20000) {
      Message({
        message: res.message || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // to re-login
        MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
          confirmButtonText: 'Re-Login',
          cancelButtonText: 'Cancel',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.log('请求失败:', error)
    console.log('请求配置:', error.config)
    if (error.response) {
      console.log('错误响应:', error.response.data)
      console.log('错误状态码:', error.response.status)
    }
    
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
