import store from '@/store'
import { hasPermission as configHasPermission, hasAnyPermission as configHasAnyPermission, hasAllPermissions as configHasAllPermissions } from '@/config/permissions'

// 权限缓存，避免重复检查相同的权限
const permissionCache = new Map()

// 缓存过期时间（毫秒）
const CACHE_EXPIRATION = 60 * 1000 // 1分钟

/**
 * 清除权限缓存
 */
export function clearPermissionCache() {
  permissionCache.clear()
}

/**
 * 检查是否有权限
 * @param {String|Array} value - 权限标识，可以是字符串或数组
 * @returns {Boolean}
 * @example
 *   checkPermission('ui:user:view')
 *   checkPermission('api:user:read')
 *   checkPermission('k8s:pods:list')
 *   checkPermission(['ui:user:view', 'api:user:read'])
 */
export default function checkPermission(value) {
  const permissions = store.getters && store.getters.permissions

  if (!permissions) {
    console.warn('No permissions found in store')
    return false
  }

  if (value) {
    if (typeof value === 'string') {
      // 检查缓存
      const cacheKey = value
      const cachedResult = getCachedPermission(cacheKey)
      if (cachedResult !== undefined) {
        return cachedResult
      }

      // 使用配置文件中的权限检查函数
      const result = configHasPermission(permissions, value)

      // 缓存结果
      cachePermission(cacheKey, result)

      return result
    } else if (Array.isArray(value)) {
      // 检查缓存
      const cacheKey = value.join(',')
      const cachedResult = getCachedPermission(cacheKey)
      if (cachedResult !== undefined) {
        return cachedResult
      }

      // 使用配置文件中的权限检查函数
      const result = configHasAnyPermission(permissions, value)

      // 缓存结果
      cachePermission(cacheKey, result)

      return result
    }
  }

  console.error(`Invalid permission format: ${value}`)
  return false
}



/**
 * 从缓存中获取权限检查结果
 * @param {String} key - 缓存键
 * @returns {Boolean|undefined} - 缓存的结果，如果不存在则返回undefined
 */
function getCachedPermission(key) {
  const cached = permissionCache.get(key)
  if (cached && Date.now() < cached.expiry) {
    return cached.value
  }
  return undefined
}

/**
 * 缓存权限检查结果
 * @param {String} key - 缓存键
 * @param {Boolean} value - 检查结果
 */
function cachePermission(key, value) {
  permissionCache.set(key, {
    value,
    expiry: Date.now() + CACHE_EXPIRATION
  })
}

/**
 * 批量检查多个权限
 * @param {Array} permissions - 权限数组
 * @returns {Object} - 权限检查结果映射
 */
export function batchCheckPermissions(permissions) {
  const results = {}

  if (!Array.isArray(permissions) || permissions.length === 0) {
    return results
  }

  permissions.forEach(perm => {
    results[perm] = checkPermission(perm)
  })

  return results
}

/**
 * 检查是否拥有所有指定的权限
 * @param {Array} permissions - 权限数组
 * @returns {Boolean} - 是否拥有所有权限
 */
export function checkAllPermissions(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return true
  }

  const userPermissions = store.getters && store.getters.permissions
  if (!userPermissions) {
    return false
  }

  // 检查缓存
  const cacheKey = `all:${permissions.join(',')}`
  const cachedResult = getCachedPermission(cacheKey)
  if (cachedResult !== undefined) {
    return cachedResult
  }

  // 使用配置文件中的权限检查函数
  const result = configHasAllPermissions(userPermissions, permissions)

  // 缓存结果
  cachePermission(cacheKey, result)

  return result
}

/**
 * 检查是否拥有任意一个指定的权限
 * @param {Array} permissions - 权限数组
 * @returns {Boolean} - 是否拥有任意一个权限
 */
export function checkAnyPermission(permissions) {
  if (!Array.isArray(permissions) || permissions.length === 0) {
    return false
  }

  const userPermissions = store.getters && store.getters.permissions
  if (!userPermissions) {
    return false
  }

  return configHasAnyPermission(userPermissions, permissions)
}

// Named export for compatibility
export { checkPermission }
