<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ onlyAuthorized ? '授权管理' : '资源权限管理' }}</span>
      </div>

      <el-tabs v-model="activeTab" v-if="!onlyAuthorized">
        <el-tab-pane label="资源权限列表" name="list">
          <div class="filter-container">
            <el-select v-model="query.type" placeholder="资源类型" class="filter-item" style="width: 120px;" @change="handleFilter">
              <el-option label="全部" value="" />
              <el-option label="API资源" value="api" />
              <el-option label="UI资源" value="ui" />
              <el-option label="K8s资源" value="k8s" />
            </el-select>
            <el-input v-model="query.keyword" placeholder="搜索关键词" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
            <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
          </div>

          <el-table
            v-loading="loading"
            :data="displayedPermissions"
            border
            fit
            highlight-current-row
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="资源类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getResourceTypeTag(scope.row.type)">
                  {{ scope.row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="resource_code" label="资源代码" min-width="150" />
            <el-table-column prop="action_code" label="操作代码" min-width="120" />
            <el-table-column prop="name" label="权限名称" min-width="180" />
            <el-table-column prop="description" label="描述" min-width="180" />
          </el-table>

          <div class="pagination-container">
            <el-pagination
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="!hideAssignment" label="资源树形结构" name="tree">
          <div class="filter-container">
            <el-select v-model="treeType" placeholder="资源类型" class="filter-item" style="width: 120px;" @change="filterTreeByType">
              <el-option label="全部" value="" />
              <el-option label="API资源" value="api" />
              <el-option label="UI资源" value="ui" />
              <el-option label="K8s资源" value="k8s" />
            </el-select>
            <el-input v-model="treeFilter" placeholder="筛选资源" class="filter-item" style="width: 200px;" @input="filterTree" />
          </div>

          <el-tree
            :data="resourceTreeData"
            :props="treeProps"
            node-key="id"
            default-expand-all
            :filter-node-method="filterNode"
            ref="resourceTree"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span :class="{'tree-node-title': data.isResource}">{{ node.label }}</span>
              <span v-if="!data.children || data.children.length === 0" class="tree-node-tag">
                <el-tag size="mini" :type="getResourceTypeTag(data.type)">{{ data.type }}</el-tag>
              </span>
            </span>
          </el-tree>
        </el-tab-pane>

        <el-tab-pane v-if="!hideAssignment" label="角色权限分配" name="assignment">
          <div class="assignment-container">
            <div class="selector-container">
              <div class="selector-header">选择角色</div>
              <el-select v-model="selectedRole" placeholder="请选择角色" style="width: 100%">
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>

              <div class="selector-header" style="margin-top: 20px">权限列表</div>
              <el-tabs v-model="permissionTab" v-if="selectedRole" class="permission-tabs">
                <el-tab-pane label="API权限" name="api">
                  <el-tree
                    ref="apiPermissionTree"
                    :data="apiPermissionTree"
                    :props="permissionProps"
                    show-checkbox
                    node-key="id"
                    :default-checked-keys="roleResourcePermissions"
                    :filter-node-method="filterPermissionNode"
                  >
                    <span slot-scope="{ node, data }" class="custom-tree-node">
                      <span>{{ node.label }}</span>
                      <span v-if="!data.children || data.children.length === 0" class="tree-node-info">
                        {{ data.description }}
                      </span>
                    </span>
                  </el-tree>
                </el-tab-pane>
                <el-tab-pane label="UI权限" name="ui">
                  <el-tree
                    ref="uiPermissionTree"
                    :data="uiPermissionTree"
                    :props="permissionProps"
                    show-checkbox
                    node-key="id"
                    :default-checked-keys="roleResourcePermissions"
                    :filter-node-method="filterPermissionNode"
                  >
                    <span slot-scope="{ node, data }" class="custom-tree-node">
                      <span>{{ node.label }}</span>
                      <span v-if="!data.children || data.children.length === 0" class="tree-node-info">
                        {{ data.description }}
                      </span>
                    </span>
                  </el-tree>
                </el-tab-pane>
                <el-tab-pane label="K8s权限" name="k8s">
                  <el-tree
                    ref="k8sPermissionTree"
                    :data="k8sPermissionTree"
                    :props="permissionProps"
                    show-checkbox
                    node-key="id"
                    :default-checked-keys="roleResourcePermissions"
                    :filter-node-method="filterPermissionNode"
                  >
                    <span slot-scope="{ node, data }" class="custom-tree-node">
                      <span>{{ node.label }}</span>
                      <span v-if="!data.children || data.children.length === 0" class="tree-node-info">
                        {{ data.description }}
                      </span>
                    </span>
                  </el-tree>
                </el-tab-pane>
              </el-tabs>

              <div v-else class="empty-tip">请先选择一个角色</div>

              <div class="action-bar" v-if="selectedRole">
                <el-button type="primary" @click="handleSavePermissions">保存权限设置</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane v-if="!onlyAuthorized" label="资源统计" name="stats">
          <el-card>
            <div slot="header">
              <span>资源权限统计</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">API资源</div>
                  <div class="stat-number">{{ getResourceCountByType('api') }}</div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">UI资源</div>
                  <div class="stat-number">{{ getResourceCountByType('ui') }}</div>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">K8s资源</div>
                  <div class="stat-number">{{ getResourceCountByType('k8s') }}</div>
                </el-card>
              </el-col>
            </el-row>
          </el-card>
        </el-tab-pane>
      </el-tabs>

      <!-- 授权管理页面 -->
      <div v-if="onlyAuthorized">
        <div class="filter-container">
          <el-select v-model="selectedGroup" placeholder="选择用户组" class="filter-item" style="width: 200px;" @change="handleGroupChange">
            <el-option
              v-for="item in groupList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-button v-if="selectedGroup" type="primary" @click="showAuthDialog">授权</el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="authorizedGroups"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="用户组名称" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="180" />
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="{row}">
              <el-button size="mini" type="danger" @click="handleRevokePermission(row)">
                撤销
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 授权对话框 -->
      <el-dialog title="用户组授权" :visible.sync="authDialogVisible" width="50%">
        <div v-if="selectedGroup">
          <p>为用户组 <strong>{{ getGroupName(selectedGroup) }}</strong> 授权:</p>
          <el-transfer
            v-model="selectedGroups"
            :data="availableGroups"
            :titles="['可用用户组', '已选用户组']"
            :props="{
              key: 'id',
              label: 'name'
            }"
          />
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="authDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAssignGroups">确认授权</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { fetchAllPermissions, fetchAllRoles, getRolePermissions, updateRolePermissions } from '@/api/role'
import Pagination from '@/components/Pagination'
import waves from '@/directive/waves'

export default {
  name: 'ResourcePermissionManagement',
  components: { Pagination },
  directives: { waves },
  props: {
    defaultTab: {
      type: String,
      default: 'list'
    },
    hideAssignment: {
      type: Boolean,
      default: false
    },
    onlyAuthorized: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: this.onlyAuthorized ? 'authorized' : this.defaultTab,
      loading: false,
      resourcePermissions: [], // 所有资源权限数据
      displayedPermissions: [], // 当前页显示的数据
      total: 0,
      currentPage: 1,
      pageSize: 10,
      query: {
        keyword: '',
        type: ''
      },

      // 树形结构
      treeType: '',
      treeFilter: '',
      resourceTreeData: [],
      treeProps: {
        children: 'children',
        label: 'name'
      },

      // 用户组权限
      groupList: [],
      selectedGroup: null,
      groupResourcePermissions: [],
      permissionTab: 'api',
      permissionFilter: '',
      apiPermissionTree: [],
      uiPermissionTree: [],
      k8sPermissionTree: [],
      permissionProps: {
        children: 'children',
        label: 'name'
      },

      // 用户组授权
      availableGroups: [],
      selectedGroups: [],
      authDialogVisible: false,
      authorizedGroups: []
    }
  },
  created() {
    this.fetchResources()
    this.fetchRoles()
    this.fetchGroups() // 新增：获取用户组列表
  },
  methods: {
    // 获取资源权限列表
    fetchResources() {
      this.loading = true
      console.log('发起请求前的query参数:', JSON.stringify(this.query))
      fetchAllPermissions({
        page: this.currentPage,
        size: this.pageSize,
        keyword: this.query.keyword,
        type: this.query.type
      }).then(response => {
        console.log('API返回的原始数据:', response)
        console.log('当前页码:', this.currentPage)
        console.log('每页条数:', this.pageSize)

        // 处理分页数据
        if (response.data && response.data.items) {
          this.resourcePermissions = response.data.items
          this.total = response.data.total || this.resourcePermissions.length
          console.log('API返回的总数:', response.data.total)
          console.log('API返回的页码:', response.data.page)
          console.log('API返回的每页条数:', response.data.size)
        } else {
          // 兼容不分页的情况
          this.resourcePermissions = response.data || []
          this.total = this.resourcePermissions.length
        }

        // 更新当前页显示的数据
        this.displayedPermissions = this.resourcePermissions

        console.log('处理后的resourcePermissions:', this.resourcePermissions)
        console.log('resourcePermissions长度:', this.resourcePermissions.length)

        this.loading = false

        if (this.resourcePermissions.length > 0) {
          // 构建树形结构
          this.buildResourceTree()
          // 构建权限树
          this.buildPermissionTrees()
        } else {
          // 清空树形结构
          this.resourceTreeData = []
          this.apiPermissionTree = []
          this.uiPermissionTree = []
          this.k8sPermissionTree = []
        }
      }).catch(error => {
        console.error('获取资源权限失败:', error)
        this.loading = false
      })
    },

    // 更新当前页显示的数据
    updateDisplayedPermissions() {
      const { page, size, keyword, type } = this.query

      // 先根据关键词和类型进行过滤
      let filteredData = this.resourcePermissions

      if (keyword) {
        const lowercaseKeyword = keyword.toLowerCase()
        filteredData = filteredData.filter(item =>
          (item.name && item.name.toLowerCase().includes(lowercaseKeyword)) ||
          (item.resource_code && item.resource_code.toLowerCase().includes(lowercaseKeyword)) ||
          (item.action_code && item.action_code.toLowerCase().includes(lowercaseKeyword)) ||
          (item.full_code && item.full_code.toLowerCase().includes(lowercaseKeyword))
        )
      }

      if (type) {
        filteredData = filteredData.filter(item => item.type === type)
      }

      // 更新总数
      this.total = filteredData.length

      // 再根据页码和每页条数截取数据
      const start = (page - 1) * size
      const end = Math.min(start + size, filteredData.length)

      this.displayedPermissions = filteredData.slice(start, end)
    },

    // 处理分页
    handlePagination(val) {
      console.log('分页事件触发:', val)
      this.currentPage = parseInt(val.page) || 1
      this.pageSize = parseInt(val.limit) || 10
      console.log('更新后的query参数:', JSON.stringify(this.query))
      this.fetchResources()

      // 滚动到顶部
      window.scrollTo(0, 0)
    },

    // 处理过滤
    handleFilter() {
      this.currentPage = 1
      this.fetchResources()
    },

    // 处理页大小变化
    handleSizeChange(val) {
      console.log('每页条数变更为:', val)
      this.pageSize = val
      this.fetchResources()
    },

    // 处理页码变化
    handleCurrentChange(val) {
      console.log('当前页变更为:', val)
      this.currentPage = val
      this.fetchResources()
    },

    // 获取角色列表
    fetchRoles() {
      fetchAllRoles().then(response => {
        this.roleList = response.data
        this.availableRoles = response.data.map(item => ({
          key: item.id,
          label: item.name
        }))
      })
    },

    // 获取用户组列表
    fetchGroups() {
      import('@/api/role').then(module => {
        const { fetchAllGroups } = module;
        fetchAllGroups().then(response => {
          this.groupList = response.data.items || response.data || [];
        }).catch(error => {
          console.error('获取用户组失败:', error);
          this.$message.error('获取用户组列表失败');
        });
      });
    },

    // 用户组变更
    handleGroupChange(groupId) {
      if (!groupId) return;

      // 获取该用户组的资源权限
      this.loading = true;

      import('@/api/role').then(module => {
        const { getGroupPermissions } = module;
        getGroupPermissions(groupId).then(response => {
          this.authorizedGroups = response.data || [];
          this.loading = false;
        }).catch(error => {
          console.error('获取用户组权限失败:', error);
          this.$message.error('获取用户组权限失败');
          this.loading = false;
        });
      });
    },

    // 获取资源类型标签
    getResourceTypeTag(type) {
      const typeMap = {
        'api': 'success',
        'ui': 'primary',
        'k8s': 'warning'
      }
      return typeMap[type] || type
    },

    // 获取特定类型资源的数量
    getResourceCountByType(type) {
      if (!this.resourcePermissions || !Array.isArray(this.resourcePermissions)) {
        return 0;
      }

      // 获取指定类型的资源数量
      const resources = this.resourcePermissions.filter(item => item.type === type);

      // 计算唯一资源代码的数量
      const uniqueResourceCodes = new Set();
      resources.forEach(item => {
        uniqueResourceCodes.add(item.resource_code);
      });

      return uniqueResourceCodes.size;
    },

    // 构建资源树
    buildResourceTree() {
      // 确保resourcePermissions是数组
      if (!this.resourcePermissions || !Array.isArray(this.resourcePermissions)) {
        this.resourceTreeData = []
        return
      }

      // 按类型分组资源
      const typeGroups = {}

      this.resourcePermissions.forEach(perm => {
        if (!typeGroups[perm.type]) {
          typeGroups[perm.type] = {}
        }

        if (!typeGroups[perm.type][perm.resource_code]) {
          typeGroups[perm.type][perm.resource_code] = {
            id: `${perm.type}:${perm.resource_code}`,
            name: perm.resource_code,
            type: perm.type,
            isResource: true,
            children: []
          }
        }

        // 添加操作
        typeGroups[perm.type][perm.resource_code].children.push({
          id: perm.id.toString(),
          name: perm.action_code,
          fullName: perm.name,
          type: perm.type,
          resource_code: perm.resource_code,
          action_code: perm.action_code,
          full_code: perm.full_code,
          description: perm.description
        })
      })

      // 转换为树形结构
      const tree = []

      // 添加类型根节点
      Object.keys(typeGroups).forEach(type => {
        const typeNode = {
          id: type,
          name: this.getTypeLabel(type),
          type: type,
          children: Object.values(typeGroups[type])
        }
        tree.push(typeNode)
      })

      this.resourceTreeData = tree
    },

    // 构建权限树
    buildPermissionTrees() {
      // 确保resourcePermissions是数组
      if (!this.resourcePermissions || !Array.isArray(this.resourcePermissions)) {
        this.apiPermissionTree = []
        this.uiPermissionTree = []
        this.k8sPermissionTree = []
        return
      }

      // 按类型构建不同的权限树
      const apiTree = {}
      const uiTree = {}
      const k8sTree = {}

      this.resourcePermissions.forEach(perm => {
        let targetTree

        if (perm.type === 'api') {
          targetTree = apiTree
        } else if (perm.type === 'ui') {
          targetTree = uiTree
        } else if (perm.type === 'k8s') {
          targetTree = k8sTree
        } else {
          return
        }

        if (!targetTree[perm.resource_code]) {
          targetTree[perm.resource_code] = {
            id: `${perm.type}:${perm.resource_code}`,
            name: perm.resource_code,
            type: perm.type,
            children: []
          }
        }

        // 添加操作节点
        targetTree[perm.resource_code].children.push({
          id: perm.id.toString(),
          name: perm.action_code,
          fullName: perm.name,
          type: perm.type,
          resource_code: perm.resource_code,
          action_code: perm.action_code,
          full_code: perm.full_code,
          description: perm.description
        })
      })

      this.apiPermissionTree = Object.values(apiTree)
      this.uiPermissionTree = Object.values(uiTree)
      this.k8sPermissionTree = Object.values(k8sTree)
    },

    // 获取类型标签
    getTypeLabel(type) {
      const typeMap = {
        'api': 'API资源',
        'ui': 'UI资源',
        'k8s': 'K8s资源'
      }
      return typeMap[type] || type
    },

    // 获取用户组名称
    getGroupName(groupId) {
      const group = this.groupList.find(g => g.id === groupId);
      return group ? group.name : `用户组 ${groupId}`;
    },

    // 按类型筛选树
    filterTreeByType() {
      this.$refs.resourceTree.filter(this.treeFilter)
    },

    // 筛选树节点
    filterTree() {
      this.$refs.resourceTree.filter(this.treeFilter)
    },

    // 树节点过滤方法
    filterNode(value, data) {
      if (!value) return true

      // 先按类型过滤
      if (this.treeType && data.type !== this.treeType) {
        return false
      }

      // 再按搜索词过滤
      if (!value) return true
      return data.name.toLowerCase().includes(value.toLowerCase()) ||
             (data.description && data.description.toLowerCase().includes(value.toLowerCase())) ||
             (data.full_code && data.full_code.toLowerCase().includes(value.toLowerCase()))
    },

    // 角色权限变化
    handleRoleChange() {
      if (this.selectedRole) {
        this.getRoleResourcePermissions(this.selectedRole)
      } else {
        this.roleResourcePermissions = []
      }
    },

    // 获取角色资源权限
    getRoleResourcePermissions(roleId) {
      getRolePermissions(roleId).then(response => {
        this.roleResourcePermissions = response.data.map(p => p.id.toString())
      })
    },

    // 显示授权对话框
    showAuthDialog() {
      this.authDialogVisible = true;

      // 获取所有可用的用户组
      import('@/api/role').then(module => {
        const { fetchAllGroups } = module;
        fetchAllGroups().then(response => {
          this.availableGroups = (response.data.items || response.data).map(group => ({
            id: group.id,
            name: group.name,
            description: group.description || ''
          }));

          // 设置已选择的用户组
          this.selectedGroups = this.authorizedGroups.map(group => group.id);
        }).catch(error => {
          console.error('获取可用用户组失败:', error);
          this.$message.error('获取可用用户组失败');
        });
      });
    },

    handleAssignGroups() {
      if (!this.selectedGroup) return;

      this.loading = true;

      import('@/api/role').then(module => {
        const { updateGroupPermissions } = module;
        updateGroupPermissions(this.selectedGroup, { permissionIds: this.selectedGroups }).then(() => {
          this.$message.success('授权成功');
          this.authDialogVisible = false;
          this.handleGroupChange(this.selectedGroup); // 刷新数据
          this.loading = false;
        }).catch(error => {
          console.error('授权失败:', error);
          this.$message.error('授权失败');
          this.loading = false;
        });
      });
    },

    handleRevokePermission(permission) {
      if (!this.selectedGroup) return;

      this.$confirm('确定要撤销此权限吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;

        import('@/api/role').then(module => {
          const { updateGroupPermissions } = module;

          // 获取当前权限列表，排除要撤销的权限
          const permissionIds = this.authorizedGroups
            .filter(p => p.id !== permission.id)
            .map(p => p.id);

          updateGroupPermissions(this.selectedGroup, { permissionIds }).then(() => {
            this.$message.success('撤销权限成功');
            this.handleGroupChange(this.selectedGroup); // 刷新数据
          }).catch(error => {
            console.error('撤销权限失败:', error);
            this.$message.error('撤销权限失败');
          }).finally(() => {
            this.loading = false;
          });
        });
      }).catch(() => {
        // 取消操作
      });
    },

    // 筛选权限
    filterPermissions() {
      const filter = this.permissionFilter.toLowerCase()

      if (this.permissionTab === 'api' && this.$refs.apiPermissionTree) {
        this.$refs.apiPermissionTree.filter(filter)
      } else if (this.permissionTab === 'ui' && this.$refs.uiPermissionTree) {
        this.$refs.uiPermissionTree.filter(filter)
      } else if (this.permissionTab === 'k8s' && this.$refs.k8sPermissionTree) {
        this.$refs.k8sPermissionTree.filter(filter)
      }
    },

    // 权限节点过滤方法
    filterPermissionNode(value, data) {
      if (!value) return true

      // 节点或子节点匹配
      if (data.name.toLowerCase().includes(value)) return true
      if (data.fullName && data.fullName.toLowerCase().includes(value)) return true
      if (data.description && data.description.toLowerCase().includes(value)) return true

      // 递归检查子节点
      if (data.children) {
        return data.children.some(child => this.filterPermissionNode(value, child))
      }

      return false
    },

    // 保存角色权限
    handleSavePermissions() {
      if (!this.selectedRole) return

      // 获取所有选中的权限ID
      const apiChecked = this.$refs.apiPermissionTree ? this.$refs.apiPermissionTree.getCheckedKeys() : []
      const uiChecked = this.$refs.uiPermissionTree ? this.$refs.uiPermissionTree.getCheckedKeys() : []
      const k8sChecked = this.$refs.k8sPermissionTree ? this.$refs.k8sPermissionTree.getCheckedKeys() : []

      // 合并所有选中的ID
      const checkedIds = [...apiChecked, ...uiChecked, ...k8sChecked]

      // 只保留数字ID (叶子节点)
      const permissionIds = checkedIds.filter(id => !isNaN(parseInt(id)))

      updateRolePermissions(this.selectedRole, { resourcePermissionIds: permissionIds }).then(() => {
        this.$message({
          type: 'success',
          message: '角色权限设置成功!'
        })
      })
    },

    // 获取唯一资源列表(合并同一资源的不同操作)
    getUniqueResources() {
      // 确保resourcePermissions是数组
      if (!this.resourcePermissions || !Array.isArray(this.resourcePermissions)) {
        return []
      }

      const resourceMap = {}

      this.resourcePermissions.forEach(perm => {
        const key = `${perm.type}:${perm.resource_code}`

        if (!resourceMap[key]) {
          resourceMap[key] = {
            type: perm.type,
            resource_code: perm.resource_code,
            actions: []
          }
        }

        if (!resourceMap[key].actions.includes(perm.action_code)) {
          resourceMap[key].actions.push(perm.action_code)
        }
      })

      return Object.values(resourceMap)
    }
  },
  watch: {
    selectedRole(val) {
      this.handleRoleChange()
    },
    treeType() {
      this.filterTreeByType()
    },
    'query.page': function() {
      this.updateDisplayedPermissions()
    },
    'query.size': function() {
      this.updateDisplayedPermissions()
    },
    selectedGroup(val) {
      if (val) {
        // 当用户组改变时，重新获取可用的角色列表
        this.fetchRoles().then(() => {
          this.availableRoles = this.roleList.map(item => ({
            key: item.id,
            label: item.name
          }));
          // 根据已授权的角色过滤掉
          this.availableRoles = this.availableRoles.filter(item =>
            !this.authorizedRoles.some(auth => auth.id === item.key)
          );
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 15px;
}

.assignment-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.selector-container {
  flex: 1;
  margin-bottom: 20px;
}

.selector-header {
  font-weight: bold;
  margin-bottom: 10px;
}

.action-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.empty-tip {
  color: #909399;
  text-align: center;
  margin-top: 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.tree-node-title {
  font-weight: bold;
}

.tree-node-tag {
  margin-left: 8px;
}

.tree-node-info {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.permission-tabs {
  margin-top: 15px;

  :deep(.el-tabs__content) {
    max-height: 400px;
    overflow: auto;
  }
}

.chart-container {
  margin-bottom: 30px;
}

.stat-card {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.resource-list {
  margin-top: 20px;
}
</style>
