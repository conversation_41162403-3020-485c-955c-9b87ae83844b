<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限检查</span>
      </div>

      <el-form ref="checkForm" :model="checkForm" label-width="120px" class="permission-form">
        <el-form-item label="用户">
          <el-select v-model="selectedUser" placeholder="选择用户" filterable>
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
          <span class="or-divider">或</span>
          <el-input v-model="userId" placeholder="输入用户ID" style="width: 150px;" />
        </el-form-item>

        <el-form-item label="资源类型">
          <el-select v-model="checkForm.type" placeholder="选择资源类型" @change="handleTypeChange">
            <el-option label="API资源" value="api" />
            <el-option label="UI资源" value="ui" />
            <el-option label="K8s资源" value="k8s" />
          </el-select>
        </el-form-item>

        <el-form-item label="资源代码">
          <el-select 
            v-model="checkForm.resource_code" 
            placeholder="选择资源代码"
            filterable 
            :disabled="!checkForm.type || !resourceCodes.length"
            @change="handleResourceChange"
          >
            <el-option
              v-for="resource in resourceCodes"
              :key="resource"
              :label="resource"
              :value="resource"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作代码">
          <el-select 
            v-model="checkForm.action_code" 
            placeholder="选择操作代码"
            filterable 
            :disabled="!checkForm.resource_code || !actionCodes.length"
          >
            <el-option
              v-for="action in actionCodes"
              :key="action"
              :label="action"
              :value="action"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="域">
          <el-input v-model="checkForm.domain" placeholder="默认为 'system'" style="width: 200px;" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="checkPermission">检查权限</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>

      <el-divider v-if="checkResult !== null" content-position="center">检查结果</el-divider>

      <div v-if="checkResult !== null" class="check-result">
        <el-alert
          :title="checkResult ? '有权限' : '无权限'"
          :type="checkResult ? 'success' : 'error'"
          :description="checkResultText"
          show-icon
          :closable="false"
        />
      </div>

      <el-collapse v-if="userPermissions.length" style="margin-top: 20px;">
        <el-collapse-item title="用户所有权限" name="1">
          <el-table
            :data="userPermissions"
            border
            style="width: 100%"
          >
            <el-table-column prop="resource_code" label="资源代码" />
            <el-table-column prop="action_code" label="操作代码" />
            <el-table-column prop="name" label="权限名称" />
            <el-table-column prop="type" label="类型">
              <template slot-scope="scope">
                <el-tag size="mini" :type="getTypeTag(scope.row.type)">
                  {{ scope.row.type }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </el-card>
  </div>
</template>

<script>
import { fetchUserList } from '@/api/user'
import { fetchAllPermissions, checkPermission as apiCheckPermission } from '@/api/role'
import { getUserPermissions } from '@/api/permission'

export default {
  name: 'PermissionCheck',
  data() {
    return {
      userList: [],
      selectedUser: null,
      userId: '',
      checkForm: {
        type: '',
        resource_code: '',
        action_code: '',
        domain: 'system'
      },
      resourcePermissions: [],
      resourceCodes: [],
      actionCodes: [],
      checkResult: null,
      checkResultText: '',
      userPermissions: []
    }
  },
  created() {
    this.fetchUsers()
    this.fetchResourcePermissions()
  },
  methods: {
    fetchUsers() {
      fetchUserList().then(response => {
        this.userList = response.data.items || response.data
      })
    },
    fetchResourcePermissions() {
      fetchAllPermissions().then(response => {
        this.resourcePermissions = response.data
      })
    },
    handleTypeChange() {
      this.checkForm.resource_code = ''
      this.checkForm.action_code = ''
      
      // 按类型过滤资源代码
      const filteredResources = this.resourcePermissions.filter(
        item => item.type === this.checkForm.type
      )
      
      // 提取不重复的资源代码
      const uniqueResourceCodes = [...new Set(filteredResources.map(item => item.resource_code))]
      this.resourceCodes = uniqueResourceCodes.sort()
    },
    handleResourceChange() {
      this.checkForm.action_code = ''
      
      // 按资源代码和类型过滤操作代码
      const filteredActions = this.resourcePermissions.filter(
        item => item.resource_code === this.checkForm.resource_code && 
               item.type === this.checkForm.type
      )
      
      // 提取操作代码
      this.actionCodes = filteredActions.map(item => item.action_code)
    },
    getTypeTag(type) {
      const typeMap = {
        'api': 'primary',
        'ui': 'success',
        'k8s': 'warning'
      }
      return typeMap[type] || 'info'
    },
    checkPermission() {
      const { resource_code, action_code, domain } = this.checkForm
      
      if (!resource_code || !action_code) {
        this.$message.warning('请选择资源和操作')
        return
      }
      
      const effectiveUserId = this.selectedUser || parseInt(this.userId)
      
      if (!effectiveUserId) {
        this.$message.warning('请选择或输入用户ID')
        return
      }
      
      apiCheckPermission(resource_code, action_code, domain).then(response => {
        this.checkResult = response.data.allowed
        
        if (this.checkResult) {
          this.checkResultText = `用户ID: ${effectiveUserId} 有权限执行 ${resource_code}:${action_code}`
        } else {
          this.checkResultText = `用户ID: ${effectiveUserId} 没有权限执行 ${resource_code}:${action_code}`
        }
        
        // 加载用户所有权限
        this.fetchUserPermissions(effectiveUserId)
      }).catch(error => {
        this.checkResult = false
        this.checkResultText = `检查失败: ${error.message}`
      })
    },
    fetchUserPermissions(userId) {
      getUserPermissions(userId).then(response => {
        this.userPermissions = response.data
      }).catch(() => {
        this.userPermissions = []
      })
    },
    resetForm() {
      this.selectedUser = null
      this.userId = ''
      this.checkForm = {
        type: '',
        resource_code: '',
        action_code: '',
        domain: 'system'
      }
      this.checkResult = null
      this.resourceCodes = []
      this.actionCodes = []
      this.userPermissions = []
    }
  },
  watch: {
    selectedUser(val) {
      if (val) {
        this.userId = ''
      }
    },
    userId(val) {
      if (val) {
        this.selectedUser = null
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.permission-form {
  max-width: 600px;
  margin: 0 auto;
}

.check-result {
  margin: 20px 0;
}

.or-divider {
  margin: 0 10px;
  color: #909399;
}
</style> 