<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>系统设置</span>
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本设置" name="basic">
          <el-form ref="basicForm" :model="basicForm" label-width="150px" :rules="basicRules">
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="basicForm.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统Logo">
              <el-upload
                class="avatar-uploader"
                action="/api/v1/system/upload"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicForm.logo" :src="basicForm.logo" class="avatar">
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <span class="form-help">建议尺寸: 120px × 120px，支持PNG、JPG格式</span>
            </el-form-item>
            
            <el-form-item label="系统描述" prop="description">
              <el-input v-model="basicForm.description" type="textarea" :rows="3" placeholder="请输入系统描述" />
            </el-form-item>
            
            <el-form-item label="管理员邮箱" prop="adminEmail">
              <el-input v-model="basicForm.adminEmail" placeholder="请输入管理员邮箱" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveBasic">保存基本设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="登录设置" name="login">
          <el-form ref="loginForm" :model="loginForm" label-width="150px" :rules="loginRules">
            <el-form-item label="允许本地登录">
              <el-switch v-model="loginForm.enableLocalLogin" />
            </el-form-item>
            
            <el-form-item label="允许OIDC登录">
              <el-switch v-model="loginForm.enableOidcLogin" />
            </el-form-item>
            
            <el-divider content-position="left">OIDC登录配置</el-divider>
            
            <div v-if="loginForm.enableOidcLogin">
              <el-form-item label="发行方URL" prop="oidcIssuerUrl">
                <el-input v-model="loginForm.oidcIssuerUrl" placeholder="请输入OIDC发行方URL (例如: https://keycloak.example.com/auth/realms/master)" />
                <span class="form-help">例如: https://keycloak.example.com/auth/realms/master</span>
              </el-form-item>
              
              <el-form-item label="Client ID" prop="oidcClientId">
                <el-input v-model="loginForm.oidcClientId" placeholder="请输入OIDC Client ID" />
              </el-form-item>
              
              <el-form-item label="Client Secret" prop="oidcClientSecret">
                <el-input v-model="loginForm.oidcClientSecret" placeholder="请输入OIDC Client Secret" show-password />
              </el-form-item>
              
              <el-form-item label="回调URL" prop="oidcRedirectUri">
                <el-input v-model="loginForm.oidcRedirectUri" placeholder="请输入回调URL" />
                <span class="form-help">例如: http://localhost:8080/api/v1/auth/oidc/callback</span>
              </el-form-item>
              
              <el-form-item label="Scopes" prop="oidcScopes">
                <el-input v-model="loginForm.oidcScopes" placeholder="请输入Scopes (逗号分隔)" />
                <span class="form-help">例如: openid,profile,email</span>
              </el-form-item>
              
              <el-form-item label="Groups Claim" prop="oidcGroupsClaim">
                <el-input v-model="loginForm.oidcGroupsClaim" placeholder="请输入Groups Claim字段名" />
                <span class="form-help">用于获取用户组信息的Claim字段名，例如: groups</span>
              </el-form-item>
              
              <el-form-item label="Roles Claim" prop="oidcRolesClaim">
                <el-input v-model="loginForm.oidcRolesClaim" placeholder="请输入Roles Claim字段名" />
                <span class="form-help">用于获取用户角色信息的Claim字段名，例如: roles</span>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="testOidcConnection">测试OIDC配置</el-button>
              </el-form-item>
            </div>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveLogin">保存登录设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="飞书配置" name="feishu">
          <el-form ref="feishuForm" :model="feishuForm" label-width="150px" :rules="feishuRules">
            <el-form-item label="启用飞书集成">
              <el-switch v-model="feishuForm.enabled" />
              <span class="form-help">启用后将允许与飞书机器人交互</span>
            </el-form-item>
            
            <div v-if="feishuForm.enabled">
              <el-form-item label="App ID" prop="appId">
                <el-input v-model="feishuForm.appId" placeholder="请输入飞书应用 App ID" />
              </el-form-item>
              
              <el-form-item label="App Secret" prop="appSecret">
                <el-input v-model="feishuForm.appSecret" placeholder="请输入飞书应用 App Secret" show-password />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="testFeishuConnection">测试飞书配置</el-button>
              </el-form-item>
            </div>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveFeishu">保存飞书配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="对象存储配置" name="obs">
          <el-form ref="obsForm" :model="obsForm" label-width="150px" :rules="obsRules">
            <el-form-item label="启用对象存储">
              <el-switch v-model="obsForm.enabled" />
              <span class="form-help">启用后可以使用对象存储服务存储审计日志等数据</span>
            </el-form-item>
            
            <div v-if="obsForm.enabled">
              <el-form-item label="端点地址" prop="endpoint">
                <el-input v-model="obsForm.endpoint" placeholder="请输入OBS端点地址" />
                <span class="form-help">例如: https://obs.cn-north-4.myhuaweicloud.com</span>
              </el-form-item>
              
              <el-form-item label="Access Key" prop="accessKey">
                <el-input v-model="obsForm.accessKey" placeholder="请输入Access Key" />
              </el-form-item>
              
              <el-form-item label="Secret Key" prop="secretKey">
                <el-input v-model="obsForm.secretKey" placeholder="请输入Secret Key" show-password />
              </el-form-item>
              
              <el-form-item label="存储桶名称" prop="bucket">
                <el-input v-model="obsForm.bucket" placeholder="请输入存储桶名称" />
              </el-form-item>
              
              <el-form-item label="区域" prop="region">
                <el-input v-model="obsForm.region" placeholder="请输入区域" />
                <span class="form-help">例如: cn-north-4</span>
              </el-form-item>
              
              <el-form-item label="加密密钥">
                <el-input v-model="obsForm.encryptionKey" placeholder="可选，用于加密存储的数据" show-password />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="testObsConnection">测试OBS配置</el-button>
              </el-form-item>
            </div>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveOBS">保存对象存储配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="审计配置" name="audit">
          <el-form ref="auditForm" :model="auditForm" label-width="150px" :rules="auditRules">
            <el-form-item label="数据保留天数" prop="retentionDays">
              <el-input-number v-model="auditForm.retentionDays" :min="1" :max="3650" />
              <span class="form-help">数据库中保留审计日志的天数，超过此天数的日志将被归档或删除</span>
            </el-form-item>
            
            <el-form-item label="启用归档">
              <el-switch v-model="auditForm.archiveEnabled" />
              <span class="form-help">启用后系统将自动归档过期的审计日志</span>
            </el-form-item>
            
            <el-form-item v-if="auditForm.archiveEnabled" label="归档间隔" prop="archiveInterval">
              <el-select v-model="auditForm.archiveInterval">
                <el-option label="每月" value="monthly" />
                <el-option label="每季度" value="quarterly" />
                <el-option label="每半年" value="half-yearly" />
                <el-option label="每年" value="yearly" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="启用加密">
              <el-switch v-model="auditForm.encryptionEnabled" />
              <span class="form-help">启用后归档的审计日志将被加密存储</span>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSaveAudit">保存审计配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
      
      <!-- 删除保存所有设置按钮 -->
    </el-card>
  </div>
</template>

<script>
import { 
  getSystemSettings, 
  updateSystemSettings, 
  getOIDCConfig,
  updateOIDCConfig,
  testOIDCConfig,
  getFeishuConfig,
  updateFeishuConfig,
  testFeishuConfig,
  getOBSConfig,
  updateOBSConfig,
  testOBSConfig,
  getAuditConfig,
  updateAuditConfig
} from '@/api/system'
import checkPermission from '@/utils/permission'

export default {
  name: 'SystemSettings',
  data() {
    return {
      activeTab: 'basic',
      loading: false,
      basicForm: {
        systemName: 'KubeOps平台',
        logo: '',
        description: '',
        adminEmail: '',
        debugMode: false
      },
      loginForm: {
        enableLocalLogin: true,
        enableOidcLogin: false,
        oidcIssuerUrl: '',
        oidcClientId: '',
        oidcClientSecret: '',
        oidcRedirectUri: '',
        oidcScopes: 'openid,profile,email',
        oidcGroupsClaim: 'groups',
        oidcRolesClaim: 'roles',
        hasExistingClientSecret: false
      },
      feishuForm: {
        enabled: false,
        appId: '',
        appSecret: '',
        hasExistingAppSecret: false
      },
      obsForm: {
        enabled: false,
        endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
        accessKey: '',
        secretKey: '',
        bucket: 'kubeops-audit-logs',
        region: 'cn-north-4',
        encryptionKey: '',
        hasExistingSecretKey: false,
        hasExistingEncryptionKey: false
      },
      auditForm: {
        retentionDays: 90,
        archiveEnabled: false,
        archiveInterval: 'quarterly',
        encryptionEnabled: false
      },
      basicRules: {
        systemName: [
          { required: true, message: '请输入系统名称', trigger: 'blur' }
        ],
        adminEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      loginRules: {
        oidcIssuerUrl: [
          { required: true, message: '请输入OIDC发行方URL', trigger: 'blur' }
        ],
        oidcClientId: [
          { required: true, message: '请输入OIDC Client ID', trigger: 'blur' }
        ],
        oidcClientSecret: [
          { required: true, message: '请输入OIDC Client Secret', trigger: 'blur' }
        ],
        oidcRedirectUri: [
          { required: true, message: '请输入回调URL', trigger: 'blur' }
        ]
      },
      feishuRules: {
        appId: [
          { required: true, message: '请输入飞书 App ID', trigger: 'blur' }
        ],
        appSecret: [
          { required: true, message: '请输入飞书 App Secret', trigger: 'blur' }
        ]
      },
      obsRules: {
        endpoint: [
          { required: true, message: '请输入端点地址', trigger: 'blur' }
        ],
        accessKey: [
          { required: true, message: '请输入Access Key', trigger: 'blur' }
        ],
        secretKey: [
          { required: true, message: '请输入Secret Key', trigger: 'blur' }
        ],
        bucket: [
          { required: true, message: '请输入存储桶名称', trigger: 'blur' }
        ],
        region: [
          { required: true, message: '请输入区域', trigger: 'blur' }
        ]
      },
      auditRules: {
        retentionDays: [
          { required: true, message: '请输入保留天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 3650, message: '保留天数必须在1-3650之间', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchAllConfigs()
  },
  methods: {
    checkPermission,
    
    // 获取所有配置
    fetchAllConfigs() {
      console.log('开始获取所有配置...')
      this.loading = true
      
      // 获取基本系统设置
      getSystemSettings().then(response => {
        console.log('基本配置响应:', response)
        const config = this.extractResponseData(response)
        
        if (config) {
        this.basicForm = {
            systemName: config.name || 'KubeOps平台',
            logo: config.logo || '',
            description: config.description || '',
            adminEmail: config.contact_email || '',
            debugMode: config.debug_mode || false
          }
          
          // 记录系统版本，但通常不允许在UI中修改
          if (config.version) {
            this.systemVersion = config.version
          }
          
          console.log('基本配置已更新:', this.basicForm)
        }
      }).catch(error => {
        console.error('获取基本配置失败:', error)
        this.$message.warning('获取基本配置失败，将使用默认值')
      })
      
      // 独立获取OIDC配置
        this.fetchOidcConfig()
        
      // 独立获取飞书配置
      this.fetchFeishuConfig()
      
      // 独立获取OBS配置
      this.fetchOBSConfig()
      
      // 独立获取审计配置
      this.fetchAuditConfig()
        
        this.loading = false
    },
    
    // 获取OIDC配置
    fetchOidcConfig() {
      console.log('开始获取OIDC配置...')
      getOIDCConfig().then(response => {
        console.log('OIDC配置响应原始数据:', response)
        
        let oidcConfig = this.extractResponseData(response)
        console.log('提取的OIDC配置数据:', oidcConfig)
        
        if (oidcConfig) {
          // 确保布尔值正确处理
          this.loginForm.enableOidcLogin = oidcConfig.enabled === true || oidcConfig.enabled === 'true'
          this.loginForm.oidcIssuerUrl = oidcConfig.issuer_url || ''
          this.loginForm.oidcClientId = oidcConfig.client_id || ''
          
          // 敏感数据处理
          if (oidcConfig.client_secret) {
            // 服务端已对敏感数据进行掩码处理，显示掩码
            this.loginForm.oidcClientSecret = '******'
            this.loginForm.hasExistingClientSecret = true
          } else {
            this.loginForm.oidcClientSecret = ''
            this.loginForm.hasExistingClientSecret = false
          }
          
          this.loginForm.oidcRedirectUri = oidcConfig.redirect_uri || ''
          this.loginForm.oidcScopes = oidcConfig.scopes || 'openid,profile,email'
          this.loginForm.oidcGroupsClaim = oidcConfig.groups_claim || 'groups'
          this.loginForm.oidcRolesClaim = oidcConfig.roles_claim || 'roles'
          
          console.log('OIDC表单数据已更新:', this.loginForm)
        } else {
          console.error('无法从响应中提取OIDC配置数据')
          this.$message.warning('获取OIDC配置失败，将使用默认值')
        }
      }).catch(error => {
        console.error('获取OIDC配置请求失败:', error)
        this.$message.warning('获取OIDC配置失败，将使用默认值')
      })
    },
    
    // 获取飞书配置
    fetchFeishuConfig() {
      getFeishuConfig().then(response => {
        let config = this.extractResponseData(response)
        console.log('获取到的飞书配置:', config)
        
        if (config) {
          this.feishuForm.enabled = config.app_id && config.app_id !== ''
          this.feishuForm.appId = config.app_id || ''
          
          // 敏感数据处理
          if (config.app_secret) {
            // 服务端已对敏感数据进行掩码处理，显示掩码
            this.feishuForm.appSecret = '******'
            this.feishuForm.hasExistingAppSecret = true
          } else {
            this.feishuForm.appSecret = ''
            this.feishuForm.hasExistingAppSecret = false
          }
          
          console.log('飞书表单数据已更新:', this.feishuForm)
        }
      }).catch(error => {
        console.error('获取飞书配置失败:', error)
      })
    },
    
    // 获取OBS配置
    fetchOBSConfig() {
      getOBSConfig().then(response => {
        let config = this.extractResponseData(response)
        console.log('获取到的OBS配置:', config)
        
        if (config) {
          this.obsForm.enabled = config.enabled === true || config.enabled === 'true'
          this.obsForm.endpoint = config.endpoint || 'https://obs.cn-north-4.myhuaweicloud.com'
          this.obsForm.accessKey = config.access_key || ''
          
          // 敏感数据处理 - SecretKey
          if (config.secret_key) {
            this.obsForm.secretKey = '******'
            this.obsForm.hasExistingSecretKey = true
          } else {
            this.obsForm.secretKey = ''
            this.obsForm.hasExistingSecretKey = false
          }
          
          this.obsForm.bucket = config.bucket || 'kubeops-audit-logs'
          this.obsForm.region = config.region || 'cn-north-4'
          
          // 敏感数据处理 - EncryptionKey
          if (config.encryption_key) {
            this.obsForm.encryptionKey = '******'
            this.obsForm.hasExistingEncryptionKey = true
          } else {
            this.obsForm.encryptionKey = ''
            this.obsForm.hasExistingEncryptionKey = false
          }
          
          console.log('OBS表单数据已更新:', this.obsForm)
        }
      }).catch(error => {
        console.error('获取OBS配置失败:', error)
      })
    },
    
    // 获取审计配置
    fetchAuditConfig() {
      getAuditConfig().then(response => {
        let config = this.extractResponseData(response)
        console.log('获取到的审计配置:', config)
        
        if (config) {
          this.auditForm.retentionDays = config.retention_days || 90
          this.auditForm.archiveEnabled = config.archive_enabled === true || config.archive_enabled === 'true'
          this.auditForm.archiveInterval = config.archive_interval || 'quarterly'
          this.auditForm.encryptionEnabled = config.encryption_enabled === true || config.encryption_enabled === 'true'
          
          console.log('审计表单数据已更新:', this.auditForm)
        }
      }).catch(error => {
        console.error('获取审计配置失败:', error)
      })
    },
    
    // 从响应中提取数据
    extractResponseData(response) {
      console.log('开始提取响应数据, 原始响应:', response)
      
      // 处理标准响应格式 {code: 20000, message: "成功", data: {...}}
      if (response.data && response.data.code === 20000 && response.data.data) {
        console.log('从标准响应格式提取数据:', response.data.data)
        return response.data.data
      } 
      // 处理简化响应格式 {data: {...}}
      else if (response.data && response.data.data) {
        console.log('从嵌套data提取数据:', response.data.data)
        return response.data.data
      }
      // 处理直接响应格式 {...}
      else if (response.data) {
        console.log('直接使用response.data:', response.data)
        return response.data
      }
      console.log('未找到有效数据')
      return null
    },

    // 测试OIDC配置
    testOidcConnection() {
      this.$refs.loginForm.validate(valid => {
        if (!valid) {
          this.$message.error('请填写必要的OIDC配置信息')
          return
        }
        
        const oidcConfig = {
          enabled: this.loginForm.enableOidcLogin,
          issuer_url: this.loginForm.oidcIssuerUrl,
          client_id: this.loginForm.oidcClientId,
          client_secret: this.loginForm.oidcClientSecret === '******' ? '' : this.loginForm.oidcClientSecret,
          redirect_uri: this.loginForm.oidcRedirectUri,
          scopes: this.loginForm.oidcScopes,
          groups_claim: this.loginForm.oidcGroupsClaim,
          roles_claim: this.loginForm.oidcRolesClaim
        }
        
        this.$message.info('正在测试OIDC配置...')
        testOIDCConfig(oidcConfig).then(response => {
          let authUrl = null
          let data = this.extractResponseData(response)
          
          if (data && data.auth_url) {
            authUrl = data.auth_url
          }
          
          if (authUrl) {
            this.$message({
              type: 'success',
              message: 'OIDC配置测试成功! 认证URL: ' + authUrl
            })
          } else {
            this.$message({
              type: 'success',
              message: 'OIDC配置测试成功!'
            })
          }
        }).catch(error => {
          this.$message({
            type: 'error',
            message: 'OIDC配置测试失败: ' + (error.message || '请检查OIDC配置')
          })
        })
      })
    },
    
    // 测试飞书配置
    testFeishuConnection() {
      this.$refs.feishuForm.validate(valid => {
        if (!valid) {
          this.$message.error('请填写必要的飞书配置信息')
          return
        }
        
        const feishuConfig = {
          app_id: this.feishuForm.appId,
          app_secret: this.feishuForm.appSecret
        }
        
        this.$message.info('正在测试飞书配置...')
        testFeishuConfig(feishuConfig).then(response => {
          this.$message({
            type: 'success',
            message: '飞书配置测试成功!'
          })
        }).catch(error => {
          this.$message({
            type: 'error',
            message: '飞书配置测试失败: ' + (error.message || '请检查飞书配置')
          })
        })
      })
    },
    
    // 测试OBS配置
    testObsConnection() {
      this.$refs.obsForm.validate(valid => {
        if (!valid) {
          this.$message.error('请填写必要的OBS配置信息')
          return
        }
        
        const obsConfig = {
          enabled: this.obsForm.enabled,
          endpoint: this.obsForm.endpoint,
          access_key: this.obsForm.accessKey,
          secret_key: this.obsForm.secretKey,
          bucket: this.obsForm.bucket,
          region: this.obsForm.region,
          encryption_key: this.obsForm.encryptionKey
        }
        
        this.$message.info('正在测试OBS配置...')
        testOBSConfig(obsConfig).then(response => {
          this.$message({
            type: 'success',
            message: 'OBS配置测试成功!'
          })
        }).catch(error => {
          this.$message({
            type: 'error',
            message: 'OBS配置测试失败: ' + (error.message || '请检查OBS配置')
          })
        })
      })
    },

    handleLogoSuccess(res, file) {
      this.basicForm.logo = URL.createObjectURL(file.raw)
    },
    
    beforeLogoUpload(file) {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPGOrPNG) {
        this.$message.error('上传Logo只能是JPG或PNG格式!')
      }
      if (!isLt2M) {
        this.$message.error('上传Logo大小不能超过2MB!')
      }
      return isJPGOrPNG && isLt2M
    },

    handleSaveBasic() {
      this.$refs.basicForm.validate(valid => {
        if (valid) {
          this.loading = true
          const basicData = {
            name: this.basicForm.systemName,
            logo: this.basicForm.logo,
            contact_email: this.basicForm.adminEmail,
            debug_mode: this.basicForm.debugMode
          }
          
          updateSystemSettings(basicData).then(() => {
            this.$message({
              type: 'success',
              message: '基本设置保存成功!'
            })
            this.loading = false
          }).catch((error) => {
            this.loading = false
            console.error('保存基本设置失败:', error)
            this.$message.error('保存基本设置失败: ' + (error.message || '未知错误'))
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },
    
    // 保存登录设置
    handleSaveLogin() {
      if (!this.loginForm.enableOidcLogin) {
        // 如果未启用OIDC，直接保存本地登录设置
        this.loading = true
        const data = {
          enable_local_login: this.loginForm.enableLocalLogin
        }
        
        // 更新基本系统配置
        updateSystemSettings(data).then(() => {
          this.$message({
            type: 'success',
            message: '登录设置保存成功!'
          })
          this.loading = false
        }).catch((error) => {
          this.loading = false
          console.error('保存登录设置失败:', error)
          this.$message.error('保存登录设置失败: ' + (error.message || '未知错误'))
        })
        return
      }
      
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          const oidcConfig = {
            enabled: this.loginForm.enableOidcLogin,
            issuer_url: this.loginForm.oidcIssuerUrl,
            client_id: this.loginForm.oidcClientId,
            client_secret: this.loginForm.oidcClientSecret === '******' ? '' : this.loginForm.oidcClientSecret,
            redirect_uri: this.loginForm.oidcRedirectUri,
            scopes: this.loginForm.oidcScopes,
            groups_claim: this.loginForm.oidcGroupsClaim,
            roles_claim: this.loginForm.oidcRolesClaim
          }
          
          updateOIDCConfig(oidcConfig).then(() => {
            // 更新标记，表示已经有保存的密钥
            if (this.loginForm.oidcClientSecret && this.loginForm.oidcClientSecret !== '******') {
              this.loginForm.hasExistingClientSecret = true
              // 更新后显示掩码
              this.loginForm.oidcClientSecret = '******'
            }
            
            this.$message({
              type: 'success',
              message: '登录设置保存成功!'
            })
          }).catch((error) => {
            console.error('OIDC配置保存失败:', error)
            this.$message.error('OIDC配置保存失败: ' + (error.message || '未知错误'))
          }).finally(() => {
            this.loading = false
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },
    
    // 保存飞书配置
    handleSaveFeishu() {
      this.$refs.feishuForm.validate(valid => {
        if (valid) {
          this.loading = true
          const feishuConfig = {
            app_id: this.feishuForm.appId,
            app_secret: this.feishuForm.appSecret === '******' ? '' : this.feishuForm.appSecret
          }
          
          updateFeishuConfig(feishuConfig).then(() => {
            // 更新标记，表示已经有保存的密钥
            if (this.feishuForm.appSecret && this.feishuForm.appSecret !== '******') {
              this.feishuForm.hasExistingAppSecret = true
              // 更新后显示掩码
              this.feishuForm.appSecret = '******'
            }
            
            this.$message({
              type: 'success',
              message: '飞书配置保存成功!'
            })
            this.loading = false
          }).catch((error) => {
            this.loading = false
            console.error('保存飞书配置失败:', error)
            this.$message.error('保存飞书配置失败: ' + (error.message || '未知错误'))
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },
    
    // 保存OBS配置
    handleSaveOBS() {
      this.$refs.obsForm.validate(valid => {
        if (valid) {
          this.loading = true
          const obsConfig = {
            enabled: this.obsForm.enabled,
            endpoint: this.obsForm.endpoint,
            access_key: this.obsForm.accessKey,
            secret_key: this.obsForm.secretKey === '******' ? '' : this.obsForm.secretKey,
            bucket: this.obsForm.bucket,
            region: this.obsForm.region,
            encryption_key: this.obsForm.encryptionKey === '******' ? '' : this.obsForm.encryptionKey
          }
          
          updateOBSConfig(obsConfig).then(() => {
            // 更新标记，表示已经有保存的密钥
            if (this.obsForm.secretKey && this.obsForm.secretKey !== '******') {
              this.obsForm.hasExistingSecretKey = true
              // 更新后显示掩码
              this.obsForm.secretKey = '******'
            }
            
            if (this.obsForm.encryptionKey && this.obsForm.encryptionKey !== '******') {
              this.obsForm.hasExistingEncryptionKey = true
              // 更新后显示掩码
              this.obsForm.encryptionKey = '******'
            }
            
            this.$message({
              type: 'success',
              message: '对象存储配置保存成功!'
            })
            this.loading = false
          }).catch((error) => {
            this.loading = false
            console.error('保存对象存储配置失败:', error)
            this.$message.error('保存对象存储配置失败: ' + (error.message || '未知错误'))
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    },
    
    // 保存审计配置
    handleSaveAudit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          this.loading = true
          const auditConfig = {
            retention_days: this.auditForm.retentionDays,
            archive_enabled: this.auditForm.archiveEnabled,
            archive_interval: this.auditForm.archiveInterval,
            encryption_enabled: this.auditForm.encryptionEnabled
          }
          
          updateAuditConfig(auditConfig).then(() => {
            this.$message({
              type: 'success',
              message: '审计配置保存成功!'
            })
            this.loading = false
          }).catch((error) => {
            this.loading = false
            console.error('保存审计配置失败:', error)
            this.$message.error('保存审计配置失败: ' + (error.message || '未知错误'))
          })
        } else {
          this.$message.error('请检查表单填写是否正确')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.form-help {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.sub-form-item {
  margin-left: 24px;
  margin-top: 10px;
}

.avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    
    &:hover {
      border-color: #409EFF;
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
}

.el-divider {
  margin: 24px 0;
}
</style>
