<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>权限管理</span>
      </div>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="权限列表" name="permissions">
          <div class="filter-container">
            <el-input v-model="permissionQuery.keyword" placeholder="权限名称/资源" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilterPermission" />
            
            <el-select v-model="permissionQuery.type" placeholder="权限类型" clearable class="filter-item" style="width: 120px;">
              <el-option label="系统" value="system" />
              <el-option label="资源" value="resource" />
              <el-option label="API" value="api" />
              <el-option label="UI" value="ui" />
            </el-select>
            
            <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilterPermission">
              搜索
            </el-button>
            
            <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreatePermission">
              创建权限
            </el-button>
          </div>
          
          <el-table
            v-loading="loading"
            :data="permissionList"
            border
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="权限名称" min-width="150" />
            <el-table-column prop="resource" label="资源" min-width="120" />
            <el-table-column prop="action" label="操作" min-width="120" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column label="类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getPermissionTypeTag(scope.row.type)">
                  {{ getPermissionTypeName(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="handleUpdatePermission(row)">
                  编辑
                </el-button>
                <el-button size="mini" type="danger" @click="handleDeletePermission(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination 
            v-show="totalPermissions>0" 
            :total="totalPermissions" 
            :page.sync="permissionQuery.page" 
            :limit.sync="permissionQuery.size" 
            @pagination="getPermissions" 
          />
        </el-tab-pane>
        
        <el-tab-pane label="资源映射" name="resources">
          <el-tree
            :data="resourceTree"
            :props="defaultProps"
            node-key="id"
            default-expand-all
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span v-if="data.actions && data.actions.length">
                <el-tag v-for="action in data.actions" :key="action" size="mini" style="margin-left: 5px;">
                  {{ action }}
                </el-tag>
              </span>
            </span>
          </el-tree>
        </el-tab-pane>
        
        <el-tab-pane label="权限分配" name="assignment">
          <div class="assignment-container">
            <div class="selector-container">
              <div class="selector-header">选择角色</div>
              <el-select v-model="selectedRole" placeholder="请选择角色" style="width: 100%">
                <el-option
                  v-for="item in roleList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              
              <div class="selector-header" style="margin-top: 20px">权限列表</div>
              <el-tree
                v-if="selectedRole"
                ref="permissionTree"
                :data="permissionTreeData"
                :props="permissionProps"
                show-checkbox
                node-key="id"
                :default-checked-keys="rolePermissions"
              />
              <div v-else class="empty-tip">请先选择一个角色</div>
            </div>
            
            <div class="action-container">
              <el-button type="primary" :disabled="!selectedRole" @click="saveRolePermissions">保存权限</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 创建/编辑权限对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建权限' : '编辑权限'" :visible.sync="permissionDialogVisible">
      <el-form ref="permissionForm" :model="permissionForm" :rules="permissionRules" label-width="100px">
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="permissionForm.name" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="资源" prop="resource">
          <el-input v-model="permissionForm.resource" placeholder="请输入资源名称，如：user, role" />
        </el-form-item>
        <el-form-item label="操作" prop="action">
          <el-input v-model="permissionForm.action" placeholder="请输入操作名称，如：create, read, update, delete" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="permissionForm.description" type="textarea" placeholder="请输入权限描述" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="permissionForm.type" placeholder="请选择权限类型">
            <el-option label="系统权限" value="system" />
            <el-option label="资源权限" value="resource" />
            <el-option label="API权限" value="api" />
            <el-option label="UI权限" value="ui" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPermissionForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAllPermissions, fetchAllRoles, getRolePermissions, updateRolePermissions, createPermission, updatePermission, deletePermission } from '@/api/role'
import checkPermission from '@/utils/permission'
import Pagination from '@/components/Pagination' // 引入分页组件
import waves from '@/directive/waves' // 引入波浪指令

export default {
  name: 'PermissionManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      activeTab: 'permissions',
      loading: false,
      permissionList: [],
      totalPermissions: 0,
      permissionQuery: {
        page: 1,
        size: 10,
        keyword: '',
        type: ''
      },
      resourceTree: [],
      roleList: [],
      selectedRole: null,
      rolePermissions: [],
      permissionTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      permissionProps: {
        children: 'children',
        label: 'name'
      },
      // 权限对话框相关数据
      permissionDialogVisible: false,
      dialogStatus: 'create',
      permissionForm: {
        name: '',
        resource: '',
        action: '',
        description: '',
        type: 'resource'
      },
      permissionRules: {
        name: [{ required: true, message: '请输入权限名称', trigger: 'blur' }],
        resource: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
        action: [{ required: true, message: '请输入操作名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择权限类型', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getPermissions()
    this.getRoles()
  },
  methods: {
    checkPermission,
    getPermissions() {
      this.loading = true
      fetchAllPermissions(this.permissionQuery).then(response => {
        this.permissionList = response.data.items || response.data
        this.totalPermissions = response.data.total || this.permissionList.length
        this.buildResourceTree()
        this.buildPermissionTree()
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleFilterPermission() {
      this.permissionQuery.page = 1
      this.getPermissions()
    },
    getRoles() {
      fetchAllRoles().then(response => {
        this.roleList = response.data
      })
    },
    buildResourceTree() {
      // 构建资源树
      const tree = []
      const resourceMap = {}
      
      // 首先创建所有资源节点
      this.permissionList.forEach(perm => {
        if (!resourceMap[perm.resource]) {
          resourceMap[perm.resource] = {
            id: perm.resource,
            name: perm.resource,
            actions: [],
            children: []
          }
          tree.push(resourceMap[perm.resource])
        }
        
        // 添加操作
        if (!resourceMap[perm.resource].actions.includes(perm.action)) {
          resourceMap[perm.resource].actions.push(perm.action)
        }
      })
      
      this.resourceTree = tree
    },
    buildPermissionTree() {
      // 构建权限树
      const tree = []
      const resourceMap = {}
      
      // 首先创建所有资源节点
      this.permissionList.forEach(perm => {
        if (!resourceMap[perm.resource]) {
          resourceMap[perm.resource] = {
            id: perm.resource,
            name: perm.resource,
            children: []
          }
          tree.push(resourceMap[perm.resource])
        }
        
        // 添加操作节点
        resourceMap[perm.resource].children.push({
          id: `${perm.resource}:${perm.action}`,
          name: perm.action,
          description: perm.description
        })
      })
      
      this.permissionTreeData = tree
    },
    getPermissionTypeTag(type) {
      const typeMap = {
        'system': 'danger',
        'resource': 'success',
        'api': 'warning',
        'ui': 'info'
      }
      return typeMap[type] || ''
    },
    getPermissionTypeName(type) {
      const typeMap = {
        'system': '系统',
        'resource': '资源',
        'api': 'API',
        'ui': 'UI'
      }
      return typeMap[type] || type
    },
    handleRoleChange() {
      if (this.selectedRole) {
        this.getRolePermissions(this.selectedRole)
      } else {
        this.rolePermissions = []
      }
    },
    getRolePermissions(roleId) {
      getRolePermissions(roleId).then(response => {
        this.rolePermissions = response.data.map(p => `${p.resource}:${p.action}`)
      })
    },
    saveRolePermissions() {
      if (!this.selectedRole) return
      
      const checkedNodes = this.$refs.permissionTree.getCheckedNodes()
      const halfCheckedNodes = this.$refs.permissionTree.getHalfCheckedNodes()
      
      // 只保存叶子节点的权限
      const permissions = checkedNodes
        .filter(node => !node.children || node.children.length === 0)
        .map(node => {
          const [resource, action] = node.id.split(':')
          return { resource, action }
        })
      
      updateRolePermissions(this.selectedRole, { permissions }).then(() => {
        this.$message({
          type: 'success',
          message: '权限保存成功!'
        })
      })
    },
    // 重置表单
    resetPermissionForm() {
      this.permissionForm = {
        name: '',
        resource: '',
        action: '',
        description: '',
        type: 'resource'
      }
    },
    // 创建权限
    handleCreatePermission() {
      this.resetPermissionForm()
      this.dialogStatus = 'create'
      this.permissionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['permissionForm'].clearValidate()
      })
    },
    // 编辑权限
    handleUpdatePermission(row) {
      this.permissionForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.permissionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['permissionForm'].clearValidate()
      })
    },
    // 删除权限
    handleDeletePermission(row) {
      this.$confirm('确认删除该权限吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePermission(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getPermissions()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 提交权限表单
    submitPermissionForm() {
      this.$refs['permissionForm'].validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            createPermission(this.permissionForm).then(() => {
              this.$message({
                type: 'success',
                message: '创建成功!'
              })
              this.permissionDialogVisible = false
              this.getPermissions()
            })
          } else {
            updatePermission(this.permissionForm.id, this.permissionForm).then(() => {
              this.$message({
                type: 'success',
                message: '更新成功!'
              })
              this.permissionDialogVisible = false
              this.getPermissions()
            })
          }
        }
      })
    }
  },
  watch: {
    selectedRole(val) {
      this.handleRoleChange()
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 15px;
}
.assignment-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.selector-container {
  flex: 1;
  margin-bottom: 20px;
}

.selector-header {
  font-weight: bold;
  margin-bottom: 10px;
}

.action-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.empty-tip {
  color: #909399;
  text-align: center;
  margin-top: 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}
</style>
