<template>
  <div class="permission-test">
    <div class="page-header">
      <h1>权限系统测试页面</h1>
      <p>此页面用于测试前端权限系统是否正常工作</p>
    </div>

    <el-row :gutter="20">
      <!-- 用户信息 -->
      <el-col :span="8">
        <el-card header="当前用户信息">
          <div class="user-info">
            <p><strong>用户名:</strong> {{ userInfo.name || userInfo.username }}</p>
            <p><strong>邮箱:</strong> {{ userInfo.email }}</p>
            <p><strong>用户组:</strong></p>
            <ul v-if="userGroups.length > 0">
              <li v-for="group in userGroups" :key="group.id || group.name">
                {{ group.name || group }} ({{ group.description || '无描述' }})
              </li>
            </ul>
            <p v-else>无用户组</p>
            <p><strong>权限数量:</strong> {{ permissions.length }}</p>
          </div>
        </el-card>
      </el-col>

      <!-- 权限测试 -->
      <el-col :span="16">
        <el-card header="权限测试">
          <div class="permission-tests">
            <h3>UI权限测试</h3>
            <div class="test-section">
              <el-button 
                v-permission="'ui:user:view'"
                type="primary"
              >
                用户查看权限 (ui:user:view)
              </el-button>
              <el-button 
                v-permission="'ui:user:create'"
                type="success"
              >
                用户创建权限 (ui:user:create)
              </el-button>
              <el-button 
                v-permission="'ui:user-group:view'"
                type="info"
              >
                用户组查看权限 (ui:user-group:view)
              </el-button>
              <el-button 
                v-permission="'ui:cluster:view'"
                type="warning"
              >
                集群查看权限 (ui:cluster:view)
              </el-button>
            </div>

            <h3>用户组权限测试</h3>
            <div class="test-section">
              <el-button 
                v-permission="{ groups: ['admin'] }"
                type="danger"
              >
                仅管理员可见
              </el-button>
              <el-button 
                v-permission="{ groups: ['admin', 'user-admin'] }"
                type="primary"
              >
                管理员或用户管理员可见
              </el-button>
              <el-button 
                v-permission="{ groups: ['k8s-admin', 'k8s-user'] }"
                type="success"
              >
                K8s管理员或用户可见
              </el-button>
            </div>

            <h3>混合权限测试</h3>
            <div class="test-section">
              <el-button 
                v-permission="{ 
                  permissions: ['ui:user:view'], 
                  groups: ['admin', 'user-admin'] 
                }"
                type="info"
              >
                需要用户查看权限且为管理员
              </el-button>
              <el-button 
                v-permission="{ 
                  any: ['ui:user:create', 'ui:user:update'] 
                }"
                type="warning"
              >
                用户创建或更新权限
              </el-button>
            </div>

            <h3>权限检查结果</h3>
            <div class="test-results">
              <el-table :data="permissionTestResults" style="width: 100%">
                <el-table-column prop="permission" label="权限" width="200" />
                <el-table-column prop="hasPermission" label="是否拥有" width="100">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.hasPermission ? 'success' : 'danger'">
                      {{ scope.row.hasPermission ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 权限详情 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card header="用户权限详情">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="权限列表" name="permissions">
              <div v-if="permissions.length > 0">
                <el-table :data="permissions" style="width: 100%">
                  <el-table-column prop="resource.type" label="资源类型" width="100" />
                  <el-table-column prop="resource.code" label="资源代码" width="200" />
                  <el-table-column prop="action" label="操作" width="100" />
                  <el-table-column prop="resource.name" label="资源名称" />
                  <el-table-column prop="resource.description" label="描述" />
                </el-table>
              </div>
              <div v-else>
                <p>暂无权限数据</p>
              </div>
            </el-collapse-item>
            <el-collapse-item title="原始数据" name="raw">
              <pre>{{ JSON.stringify({ userInfo, userGroups, permissions }, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import checkPermission from '@/utils/permission'
import { UI_PERMISSIONS } from '@/config/permissions'

export default {
  name: 'PermissionTest',
  data() {
    return {
      activeCollapse: [],
      testPermissions: [
        { permission: 'ui:user:view', description: 'UI用户查看权限' },
        { permission: 'ui:user:create', description: 'UI用户创建权限' },
        { permission: 'ui:user:update', description: 'UI用户更新权限' },
        { permission: 'ui:user:delete', description: 'UI用户删除权限' },
        { permission: 'ui:user-group:view', description: 'UI用户组查看权限' },
        { permission: 'ui:user-group:create', description: 'UI用户组创建权限' },
        { permission: 'ui:cluster:view', description: 'UI集群查看权限' },
        { permission: 'ui:audit-log:view', description: 'UI审计日志查看权限' },
        { permission: 'api:user:read', description: 'API用户读取权限' },
        { permission: 'api:user:write', description: 'API用户写入权限' },
        { permission: 'k8s:pods:list', description: 'K8s Pod列表权限' },
        { permission: 'k8s:services:get', description: 'K8s Service获取权限' }
      ]
    }
  },
  computed: {
    ...mapGetters(['name', 'avatar', 'userGroups', 'permissions']),
    userInfo() {
      return this.$store.state.user
    },
    permissionTestResults() {
      return this.testPermissions.map(test => ({
        ...test,
        hasPermission: checkPermission(test.permission)
      }))
    }
  },
  mounted() {
    console.log('权限测试页面加载完成')
    console.log('用户信息:', this.userInfo)
    console.log('用户组:', this.userGroups)
    console.log('权限:', this.permissions)
  },
  methods: {
    checkPermission
  }
}
</script>

<style scoped>
.permission-test {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.user-info p {
  margin: 8px 0;
}

.user-info ul {
  margin: 8px 0;
  padding-left: 20px;
}

.permission-tests h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 5px;
}

.test-section {
  margin-bottom: 20px;
}

.test-section .el-button {
  margin: 5px 10px 5px 0;
}

.test-results {
  margin-top: 20px;
}

pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}
</style>
