<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>资源管理</span>
      </div>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="资源列表" name="resources">
          <div class="filter-container">
            <el-input v-model="resourceQuery.keyword" placeholder="资源名称/代码" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilterResource" />
            
            <el-select v-model="resourceQuery.type" placeholder="资源类型" clearable class="filter-item" style="width: 120px;">
              <el-option label="API" value="api" />
              <el-option label="UI" value="ui" />
              <el-option label="Kubernetes" value="k8s" />
            </el-select>
            
            <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilterResource">
              搜索
            </el-button>
            
            <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreateResource">
              创建资源
            </el-button>
            
            <el-button class="filter-item" style="margin-left: 10px;" type="success" @click="handleInitResources">
              初始化系统资源
            </el-button>
          </div>
          
          <el-table
            v-loading="loading"
            :data="resourceList"
            border
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="资源名称" min-width="150" />
            <el-table-column prop="code" label="资源代码" min-width="150" />
            <el-table-column prop="description" label="描述" min-width="200" />
            <el-table-column label="类型" width="100">
              <template slot-scope="scope">
                <el-tag :type="getResourceTypeTag(scope.row.type)">
                  {{ getResourceTypeName(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作数量" width="100" align="center">
              <template slot-scope="scope">
                <el-badge :value="scope.row.actions ? scope.row.actions.length : 0" class="item">
                  <el-button size="mini" type="info" plain @click="handleViewActions(scope.row)">查看</el-button>
                </el-badge>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="handleUpdateResource(row)">
                  编辑
                </el-button>
                <el-button type="success" size="mini" @click="handleManageActions(row)">
                  管理操作
                </el-button>
                <el-button size="mini" type="danger" @click="handleDeleteResource(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <pagination 
            v-show="totalResources>0" 
            :total="totalResources" 
            :page.sync="resourceQuery.page" 
            :limit.sync="resourceQuery.size" 
            @pagination="getResources" 
          />
        </el-tab-pane>
        
        <el-tab-pane label="资源树" name="resourceTree">
          <div class="filter-container">
            <el-select v-model="treeType" placeholder="资源类型" clearable class="filter-item" style="width: 120px;" @change="buildResourceTree">
              <el-option label="全部" value="" />
              <el-option label="API" value="api" />
              <el-option label="UI" value="ui" />
              <el-option label="Kubernetes" value="k8s" />
              <el-option label="系统" value="system" />
            </el-select>
          </div>
          
          <el-tree
            :data="resourceTreeData"
            :props="defaultProps"
            node-key="id"
            default-expand-all
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span>{{ node.label }}</span>
              <span v-if="data.actions && data.actions.length">
                <el-tag v-for="action in data.actions" :key="action.code" size="mini" style="margin-left: 5px;">
                  {{ action.name }}
                </el-tag>
              </span>
            </span>
          </el-tree>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 创建/编辑资源对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建资源' : '编辑资源'" :visible.sync="resourceDialogVisible">
      <el-form ref="resourceForm" :model="resourceForm" :rules="resourceRules" label-width="100px">
        <el-form-item label="资源名称" prop="name">
          <el-input v-model="resourceForm.name" placeholder="请输入资源名称" />
        </el-form-item>
        <el-form-item label="资源代码" prop="code">
          <el-input v-model="resourceForm.code" placeholder="请输入资源代码，如：api:user, k8s:pods" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="resourceForm.description" type="textarea" placeholder="请输入资源描述" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="resourceForm.type" placeholder="请选择资源类型">
            <el-option label="API" value="api" />
            <el-option label="UI" value="ui" />
            <el-option label="Kubernetes" value="k8s" />
            <el-option label="系统" value="system" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resourceDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitResourceForm">确认</el-button>
      </div>
    </el-dialog>
    
    <!-- 查看操作对话框 -->
    <el-dialog title="资源操作" :visible.sync="actionsDialogVisible" width="60%">
      <div v-if="currentResource">
        <p class="dialog-info">资源: {{ currentResource.name }} ({{ currentResource.code }})</p>
        
        <el-table
          v-loading="actionsLoading"
          :data="actionsList"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="操作名称" min-width="150" />
          <el-table-column prop="code" label="操作代码" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="200" />
        </el-table>
      </div>
    </el-dialog>
    
    <!-- 管理操作对话框 -->
    <el-dialog title="管理资源操作" :visible.sync="manageActionsDialogVisible" width="70%">
      <div v-if="currentResource">
        <p class="dialog-info">资源: {{ currentResource.name }} ({{ currentResource.code }})</p>
        
        <div class="filter-container">
          <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreateAction">
            添加操作
          </el-button>
        </div>
        
        <el-table
          v-loading="actionsLoading"
          :data="actionsList"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="操作名称" min-width="150" />
          <el-table-column prop="code" label="操作代码" min-width="150" />
          <el-table-column prop="description" label="描述" min-width="200" />
          <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button type="primary" size="mini" @click="handleUpdateAction(row)">
                编辑
              </el-button>
              <el-button size="mini" type="danger" @click="handleDeleteAction(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="manageActionsDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
    
    <!-- 创建/编辑操作对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建操作' : '编辑操作'" :visible.sync="actionDialogVisible">
      <el-form ref="actionForm" :model="actionForm" :rules="actionRules" label-width="100px">
        <el-form-item label="操作名称" prop="name">
          <el-input v-model="actionForm.name" placeholder="请输入操作名称" />
        </el-form-item>
        <el-form-item label="操作代码" prop="code">
          <el-input v-model="actionForm.code" placeholder="请输入操作代码，如：create, read, update, delete" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="actionForm.description" type="textarea" placeholder="请输入操作描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="actionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitActionForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchResourceList, fetchAllResources, getResourceDetail, createResource, updateResource, deleteResource, 
         getResourceActions, createResourceAction, updateResourceAction, deleteResourceAction, initSystemResources } from '@/api/resource'
import Pagination from '@/components/Pagination'
import waves from '@/directive/waves'

export default {
  name: 'ResourceManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      activeTab: 'resources',
      loading: false,
      resourceList: [],
      totalResources: 0,
      resourceQuery: {
        page: 1,
        size: 10,
        keyword: '',
        type: ''
      },
      treeType: '',
      resourceTreeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      
      // 资源对话框相关数据
      resourceDialogVisible: false,
      dialogStatus: 'create',
      resourceForm: {
        name: '',
        code: '',
        description: '',
        type: 'api'
      },
      resourceRules: {
        name: [{ required: true, message: '请输入资源名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入资源代码', trigger: 'blur' }],
        type: [{ required: true, message: '请选择资源类型', trigger: 'change' }]
      },
      
      // 操作相关数据
      currentResource: null,
      actionsDialogVisible: false,
      manageActionsDialogVisible: false,
      actionsLoading: false,
      actionsList: [],
      
      // 操作对话框相关数据
      actionDialogVisible: false,
      actionForm: {
        name: '',
        code: '',
        description: ''
      },
      actionRules: {
        name: [{ required: true, message: '请输入操作名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入操作代码', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getResources()
  },
  methods: {
    getResources() {
      this.loading = true
      fetchResourceList(this.resourceQuery).then(response => {
        this.resourceList = response.data.items || response.data
        this.totalResources = response.data.total || this.resourceList.length
        this.loading = false
        
        // 如果当前是资源树标签页，则构建资源树
        if (this.activeTab === 'resourceTree') {
          this.buildResourceTree()
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleFilterResource() {
      this.resourceQuery.page = 1
      this.getResources()
    },
    getResourceTypeTag(type) {
      const typeMap = {
        'api': 'success',
        'ui': 'info',
        'k8s': 'warning',
        'system': 'danger'
      }
      return typeMap[type] || ''
    },
    getResourceTypeName(type) {
      const typeMap = {
        'api': 'API',
        'ui': 'UI',
        'k8s': 'Kubernetes',
        'system': '系统'
      }
      return typeMap[type] || type
    },
    buildResourceTree() {
      // 获取所有资源
      fetchAllResources(this.treeType).then(response => {
        const resources = response.data.items || response.data
        this.resourceTreeData = resources.map(resource => ({
          id: resource.id,
          name: resource.name,
          code: resource.code,
          type: resource.type,
          description: resource.description,
          actions: resource.actions || []
        }))
      })
    },
    // 重置资源表单
    resetResourceForm() {
      this.resourceForm = {
        name: '',
        code: '',
        description: '',
        type: 'api'
      }
    },
    // 创建资源
    handleCreateResource() {
      this.resetResourceForm()
      this.dialogStatus = 'create'
      this.resourceDialogVisible = true
      this.$nextTick(() => {
        this.$refs['resourceForm'].clearValidate()
      })
    },
    // 编辑资源
    handleUpdateResource(row) {
      this.resourceForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.resourceDialogVisible = true
      this.$nextTick(() => {
        this.$refs['resourceForm'].clearValidate()
      })
    },
    // 删除资源
    handleDeleteResource(row) {
      this.$confirm('确认删除该资源吗？这将同时删除所有关联的操作。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteResource(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getResources()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 提交资源表单
    submitResourceForm() {
      this.$refs['resourceForm'].validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            createResource(this.resourceForm).then(() => {
              this.$message({
                type: 'success',
                message: '创建成功!'
              })
              this.resourceDialogVisible = false
              this.getResources()
            })
          } else {
            updateResource(this.resourceForm.id, this.resourceForm).then(() => {
              this.$message({
                type: 'success',
                message: '更新成功!'
              })
              this.resourceDialogVisible = false
              this.getResources()
            })
          }
        }
      })
    },
    // 查看资源操作
    handleViewActions(row) {
      this.currentResource = row
      this.actionsDialogVisible = true
      this.getResourceActions(row.id)
    },
    // 管理资源操作
    handleManageActions(row) {
      this.currentResource = row
      this.manageActionsDialogVisible = true
      this.getResourceActions(row.id)
    },
    // 获取资源操作
    getResourceActions(resourceId) {
      this.actionsLoading = true
      getResourceActions(resourceId).then(response => {
        this.actionsList = response.data
        this.actionsLoading = false
      }).catch(() => {
        this.actionsLoading = false
      })
    },
    // 重置操作表单
    resetActionForm() {
      this.actionForm = {
        name: '',
        code: '',
        description: ''
      }
    },
    // 创建操作
    handleCreateAction() {
      this.resetActionForm()
      this.dialogStatus = 'create'
      this.actionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['actionForm'].clearValidate()
      })
    },
    // 编辑操作
    handleUpdateAction(row) {
      this.actionForm = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.actionDialogVisible = true
      this.$nextTick(() => {
        this.$refs['actionForm'].clearValidate()
      })
    },
    // 删除操作
    handleDeleteAction(row) {
      this.$confirm('确认删除该操作吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteResourceAction(this.currentResource.id, row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getResourceActions(this.currentResource.id)
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    // 提交操作表单
    submitActionForm() {
      this.$refs['actionForm'].validate(valid => {
        if (valid) {
          if (this.dialogStatus === 'create') {
            createResourceAction(this.currentResource.id, this.actionForm).then(() => {
              this.$message({
                type: 'success',
                message: '创建成功!'
              })
              this.actionDialogVisible = false
              this.getResourceActions(this.currentResource.id)
            })
          } else {
            updateResourceAction(this.currentResource.id, this.actionForm.id, this.actionForm).then(() => {
              this.$message({
                type: 'success',
                message: '更新成功!'
              })
              this.actionDialogVisible = false
              this.getResourceActions(this.currentResource.id)
            })
          }
        }
      })
    },
    // 初始化系统资源
    handleInitResources() {
      this.$confirm('确认初始化系统资源吗？这将创建默认的系统资源和操作。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        initSystemResources().then(() => {
          this.$message({
            type: 'success',
            message: '系统资源初始化成功!'
          })
          this.getResources()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消操作'
        })
      })
    }
  },
  watch: {
    activeTab(val) {
      if (val === 'resourceTree') {
        this.buildResourceTree()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 15px;
}

.dialog-info {
  font-weight: bold;
  margin-bottom: 20px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}
</style> 