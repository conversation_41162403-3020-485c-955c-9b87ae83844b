<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.name" placeholder="用户组名称" class="filter-item" style="width: 200px;" @keyup.enter.native="handleFilter" />
      <el-button v-wave class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        添加用户组
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="groupList"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户组名称" min-width="150px">
        <template slot-scope="{row}">
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="域" min-width="100px">
        <template slot-scope="{row}">
          <span>{{ row.domain || '默认' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="{row}">
          <span>{{ row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="success" @click="handlePermissions(row)">
            权限
          </el-button>
          <el-button size="mini" type="info" @click="handleMembers(row)">
            成员
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getGroupList" />

    <!-- 用户组表单对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建用户组' : '编辑用户组'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="用户组名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入用户组名称" />
        </el-form-item>
        <el-form-item label="域" prop="domain">
          <el-input v-model="temp.domain" placeholder="请输入域（可选）" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="temp.description" type="textarea" :rows="3" placeholder="请输入用户组描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 权限对话框 -->
    <el-dialog title="管理权限" :visible.sync="dialogPermissionVisible" width="60%">
      <div v-if="currentGroup">
        <p class="dialog-info">用户组: {{ currentGroup.name }}</p>
        
        <el-tree
          ref="permissionTree"
          :data="permissionTreeData"
          :props="permissionProps"
          show-checkbox
          node-key="id"
          :default-checked-keys="groupPermissions"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogPermissionVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateGroupPermissions">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 用户组成员对话框 -->
    <el-dialog title="管理用户组成员" :visible.sync="dialogMembersVisible" width="70%">
      <div v-if="currentGroup">
        <p class="dialog-info">用户组: {{ currentGroup.name }}</p>
        
        <el-transfer
          v-model="selectedMembers"
          :data="allUsers"
          :titles="['可用用户', '组成员']"
          :button-texts="['移除', '添加']"
          filterable
          filter-placeholder="搜索用户"
          :props="{
            key: 'id',
            label: 'name'
          }"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogMembersVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateGroupMembers">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchGroupList, createGroup, updateGroup, deleteGroup, getGroupMembers, updateGroupMembers, getGroupPermissions, updateGroupPermissions } from '@/api/role'
import { fetchUserList } from '@/api/user'
import { fetchAllPermissions } from '@/api/role'
import Pagination from '@/components/Pagination'
import checkPermission from '@/utils/permission'
import waves from '@/directive/waves'

export default {
  name: 'UserGroupManagement',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      // 用户组管理相关
      listQuery: {
        page: 1,
        size: 10,
        name: undefined
      },
      groupList: [],
      total: 0,
      listLoading: false,
      temp: {
        id: undefined,
        name: '',
        domain: '',
        description: ''
      },
      dialogStatus: 'create',
      dialogFormVisible: false,
      rules: {
        name: [{ required: true, message: '用户组名称不能为空', trigger: 'blur' }]
      },
      
      // 权限管理相关
      currentGroup: null,
      dialogPermissionVisible: false,
      permissionTreeData: [],
      permissionProps: {
        children: 'children',
        label: 'name'
      },
      groupPermissions: [],
      
      // 用户组成员管理
      dialogMembersVisible: false,
      allUsers: [],
      selectedMembers: []
    }
  },
  created() {
    this.getGroupList()
  },
  methods: {
    checkPermission,
    // 用户组管理方法
    getGroupList() {
      this.listLoading = true
      fetchGroupList(this.listQuery).then(response => {
        this.groupList = response.data.items || response.data
        this.total = response.data.total || this.groupList.length
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getGroupList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        domain: '',
        description: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createGroup(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '创建成功'
            })
            this.getGroupList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // 使用对象的浅拷贝，避免影响原始数据
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateGroup(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '更新成功'
            })
            this.getGroupList()
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除这个用户组吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteGroup(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          // 删除后重新加载数据
          this.getGroupList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handlePermissions(row) {
      this.currentGroup = row
      this.dialogPermissionVisible = true
      // 获取用户组权限
      getGroupPermissions(row.id).then(response => {
        const permissions = response.data
        this.groupPermissions = permissions.map(p => p.id)
      })
      
      // 获取所有权限
      this.getPermissionTree()
    },
    getPermissionTree() {
      fetchAllPermissions().then(response => {
        const permissions = response.data
        // 构建权限树
        const tree = []
        const typeMap = {}
        
        // 首先按类型分组
        permissions.forEach(perm => {
          if (!typeMap[perm.type]) {
            typeMap[perm.type] = {
              id: `type:${perm.type}`,
              name: this.getResourceTypeName(perm.type),
              children: []
            }
            tree.push(typeMap[perm.type])
          }
          
          // 添加权限节点
          typeMap[perm.type].children.push({
            id: perm.id,
            name: `${perm.name} (${perm.resource_code}:${perm.action_code})`,
            description: perm.description
          })
        })
        
        this.permissionTreeData = tree
      })
    },
    getResourceTypeName(type) {
      const typeNames = {
        'api': 'API资源',
        'ui': 'UI资源',
        'k8s': 'Kubernetes资源'
      }
      return typeNames[type] || type
    },
    updateGroupPermissions() {
      if (!this.currentGroup) return
      
      const checkedNodes = this.$refs.permissionTree.getCheckedNodes()
      
      // 只保存叶子节点的权限
      const permissionIds = checkedNodes
        .filter(node => !node.children || node.children.length === 0)
        .map(node => node.id)
      
      updateGroupPermissions(this.currentGroup.id, { permissionIds }).then(() => {
        this.$message({
          type: 'success',
          message: '权限设置成功!'
        })
        this.dialogPermissionVisible = false
      })
    },
    handleMembers(row) {
      this.currentGroup = row
      this.dialogMembersVisible = true
      
      // 获取所有用户
      fetchUserList().then(response => {
        this.allUsers = (response.data.items || response.data).map(user => ({
          id: user.id,
          name: `${user.username || user.name} ${user.email ? `(${user.email})` : ''}`
        }))
      })
      
      // 获取用户组成员
      getGroupMembers(row.id).then(response => {
        this.selectedMembers = response.data.map(user => user.id)
      })
    },
    updateGroupMembers() {
      if (!this.currentGroup) return
      
      updateGroupMembers(this.currentGroup.id, { userIds: this.selectedMembers }).then(() => {
        this.$message({
          type: 'success',
          message: '成员设置成功!'
        })
        this.dialogMembersVisible = false
        this.getGroupList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 15px;
}

.dialog-info {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: bold;
}

.el-transfer {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}
</style>
