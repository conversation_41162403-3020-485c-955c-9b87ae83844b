<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button class="filter-item" type="success" icon="el-icon-refresh" @click="handleSyncFeishu">
        同步飞书部门
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreateDepartment">
        添加部门
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="departmentList"
      row-key="id"
      border
      default-expand-all
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="部门名称" min-width="180" />
      <el-table-column prop="code" label="部门编码" width="120" />
      <el-table-column prop="source" label="来源" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.source === 'feishu' ? 'success' : ''">
            {{ scope.row.source === 'feishu' ? '飞书' : '本地' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="member_count" label="成员数" width="100" align="center" />
      <el-table-column label="操作" align="center" width="280">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEditDepartment(scope.row)">
            编辑
          </el-button>
          <el-button size="mini" type="success" @click="handleRoleMapping(scope.row)">
            角色映射
          </el-button>
          <el-button size="mini" type="primary" @click="handleViewMembers(scope.row)">
            查看成员
          </el-button>
          <el-button v-if="scope.row.source !== 'feishu'" size="mini" type="danger" @click="handleDeleteDepartment(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 部门表单对话框 -->
    <el-dialog :title="dialogStatus === 'create' ? '创建部门' : '编辑部门'" :visible.sync="dialogFormVisible">
      <el-form ref="departmentForm" :rules="departmentRules" :model="departmentForm" label-position="left" label-width="100px" style="width: 400px; margin-left:50px;">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="departmentForm.name" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="部门编码" prop="code">
          <el-input v-model="departmentForm.code" placeholder="请输入部门编码" />
        </el-form-item>
        <el-form-item label="上级部门">
          <el-select v-model="departmentForm.parent_id" placeholder="请选择上级部门" style="width: 100%">
            <el-option label="无上级部门" :value="null" />
            <el-option
              v-for="item in departmentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus === 'create' ? createDepartment() : updateDepartment()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 角色映射对话框 -->
    <el-dialog title="部门角色映射" :visible.sync="dialogRoleVisible">
      <div v-if="currentDepartment">
        <p class="dialog-info">部门: {{ currentDepartment.name }}</p>
        
        <el-transfer
          v-model="selectedRoles"
          :data="allRoles"
          :titles="['可用角色', '已分配角色']"
          :button-texts="['移除', '添加']"
          :props="{
            key: 'id',
            label: 'name'
          }"
        />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRoleVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="updateDepartmentRoles">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 部门成员对话框 -->
    <el-dialog title="部门成员" :visible.sync="dialogMembersVisible">
      <div v-if="currentDepartment">
        <p class="dialog-info">部门: {{ currentDepartment.name }}</p>
        
        <el-table
          v-loading="membersLoading"
          :data="departmentMembers"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="用户名" prop="username" />
          <el-table-column label="姓名" prop="name" />
          <el-table-column label="邮箱" prop="email" />
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                {{ scope.row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 同步飞书部门对话框 -->
    <el-dialog title="同步飞书部门" :visible.sync="dialogSyncVisible">
      <div class="sync-warning">
        <i class="el-icon-warning"></i>
        <span>同步操作可能需要几分钟时间，期间请勿关闭页面</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogSyncVisible = false">
          取消
        </el-button>
        <el-button type="primary" :loading="syncLoading" @click="syncFeishuDepartments">
          开始同步
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchDepartmentList, syncFeishuDepartments, getDepartmentRoleMapping, updateDepartmentRoleMapping } from '@/api/role'
import { fetchAllRoles } from '@/api/role'
import checkPermission from '@/utils/permission'

export default {
  name: 'DepartmentManagement',
  data() {
    return {
      loading: true,
      departmentList: [],
      dialogRoleVisible: false,
      dialogMembersVisible: false,
      dialogSyncVisible: false,
      dialogFormVisible: false,
      dialogStatus: 'create',
      currentDepartment: null,
      allRoles: [],
      selectedRoles: [],
      departmentMembers: [],
      membersLoading: false,
      syncLoading: false,
      departmentForm: {
        name: '',
        code: '',
        parent_id: null
      },
      departmentRules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入部门编码', trigger: 'blur' }]
      },
      departmentOptions: []
    }
  },
  created() {
    this.getDepartments()
  },
  methods: {
    checkPermission,
    getDepartments() {
      this.loading = true
      fetchDepartmentList().then(response => {
        this.departmentList = response.data
        this.departmentOptions = this.flattenDepartments(this.departmentList)
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    // 扁平化部门树，用于选择上级部门
    flattenDepartments(departments, result = []) {
      departments.forEach(dept => {
        result.push({
          id: dept.id,
          name: dept.name
        })
        if (dept.children && dept.children.length > 0) {
          this.flattenDepartments(dept.children, result)
        }
      })
      return result
    },
    handleCreateDepartment() {
      this.dialogStatus = 'create'
      this.departmentForm = {
        name: '',
        code: '',
        parent_id: null
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['departmentForm'].clearValidate()
      })
    },
    handleEditDepartment(row) {
      this.dialogStatus = 'update'
      this.departmentForm = Object.assign({}, {
        id: row.id,
        name: row.name,
        code: row.code,
        parent_id: row.parent_id
      })
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['departmentForm'].clearValidate()
      })
    },
    handleDeleteDepartment(row) {
      this.$confirm('确认删除该部门吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用删除部门的API
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getDepartments()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    createDepartment() {
      this.$refs['departmentForm'].validate((valid) => {
        if (valid) {
          // 这里应该调用创建部门的API
          this.$message({
            type: 'success',
            message: '创建成功!'
          })
          this.dialogFormVisible = false
          this.getDepartments()
        }
      })
    },
    updateDepartment() {
      this.$refs['departmentForm'].validate((valid) => {
        if (valid) {
          // 这里应该调用更新部门的API
          this.$message({
            type: 'success',
            message: '更新成功!'
          })
          this.dialogFormVisible = false
          this.getDepartments()
        }
      })
    },
    handleRoleMapping(row) {
      this.currentDepartment = row
      this.dialogRoleVisible = true
      
      // 获取所有角色
      fetchAllRoles().then(response => {
        this.allRoles = response.data
        
        // 获取部门当前角色映射
        getDepartmentRoleMapping().then(response => {
          const mappings = response.data
          const departmentMapping = mappings.find(m => m.department_id === row.id)
          this.selectedRoles = departmentMapping ? departmentMapping.role_ids : []
        })
      })
    },
    updateDepartmentRoles() {
      const data = {
        department_id: this.currentDepartment.id,
        role_ids: this.selectedRoles
      }
      
      updateDepartmentRoleMapping(data).then(() => {
        this.$message({
          type: 'success',
          message: '角色映射更新成功!'
        })
        this.dialogRoleVisible = false
      })
    },
    handleViewMembers(row) {
      this.currentDepartment = row
      this.dialogMembersVisible = true
      this.membersLoading = true
      
      // 这里应该调用获取部门成员的API，但示例中未提供，使用模拟数据
      setTimeout(() => {
        this.departmentMembers = [
          { username: 'user1', name: '用户1', email: '<EMAIL>', status: 'active' },
          { username: 'user2', name: '用户2', email: '<EMAIL>', status: 'active' },
          { username: 'user3', name: '用户3', email: '<EMAIL>', status: 'inactive' }
        ]
        this.membersLoading = false
      }, 500)
    },
    handleSyncFeishu() {
      this.dialogSyncVisible = true
    },
    syncFeishuDepartments() {
      this.syncLoading = true
      syncFeishuDepartments().then(() => {
        this.$message({
          type: 'success',
          message: '部门同步成功!'
        })
        this.syncLoading = false
        this.dialogSyncVisible = false
        this.getDepartments()
      }).catch(() => {
        this.syncLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-info {
  margin-bottom: 20px;
  font-weight: bold;
}

.sync-warning {
  padding: 10px;
  background-color: #FDF6EC;
  color: #E6A23C;
  border-radius: 4px;
  
  i {
    margin-right: 5px;
  }
}

.el-transfer {
  margin: 20px 0;
}
</style>
