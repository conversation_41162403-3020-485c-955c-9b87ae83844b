<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.type" placeholder="审批类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in approvalTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="listQuery.keyword"
        placeholder="关键字"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        @click="getList"
      >
        刷新
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.type | approvalTypeFilter">{{ row.type | approvalTypeLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="标题" min-width="150px" align="center">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleDetail(row)">{{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.creator }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="180px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createdAt | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">{{ row.status | statusLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="当前审批人" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.currentApprover }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button v-if="canApprove(row)" size="mini" type="success" @click="handleApprove(row)">
            通过
          </el-button>
          <el-button v-if="canReject(row)" size="mini" type="danger" @click="handleReject(row)">
            拒绝
          </el-button>
          <el-button size="mini" @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 审批对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="500px">
      <el-form ref="dataForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            placeholder="请输入审批意见"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchPendingApprovals, approveRequest, rejectRequest } from '@/api/approvals'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'PendingApprovals',
  components: { Pagination },
  directives: { waves },
  filters: {
    approvalTypeFilter(type) {
      const typeMap = {
        'CLUSTER_CREATE': 'primary',
        'CLUSTER_DELETE': 'danger',
        'CLUSTER_SCALE': 'warning',
        'RESOURCE_CREATE': 'success',
        'RESOURCE_UPDATE': 'info',
        'RESOURCE_DELETE': 'danger',
        'USER_PERMISSION': 'primary'
      }
      return typeMap[type] || 'info'
    },
    approvalTypeLabel(type) {
      const labelMap = {
        'CLUSTER_CREATE': '创建集群',
        'CLUSTER_DELETE': '删除集群',
        'CLUSTER_SCALE': '集群扩缩',
        'RESOURCE_CREATE': '创建资源',
        'RESOURCE_UPDATE': '更新资源',
        'RESOURCE_DELETE': '删除资源',
        'USER_PERMISSION': '权限申请'
      }
      return labelMap[type] || type
    },
    statusFilter(status) {
      const statusMap = {
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'CANCELED': 'info'
      }
      return statusMap[status] || 'info'
    },
    statusLabel(status) {
      const labelMap = {
        'PENDING': '待审批',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELED': '已取消'
      }
      return labelMap[status] || status
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        type: undefined,
        status: 'PENDING',
        keyword: undefined,
        sort: '-id'
      },
      approvalTypeOptions: [
        { label: '创建集群', value: 'CLUSTER_CREATE' },
        { label: '删除集群', value: 'CLUSTER_DELETE' },
        { label: '集群扩缩', value: 'CLUSTER_SCALE' },
        { label: '创建资源', value: 'RESOURCE_CREATE' },
        { label: '更新资源', value: 'RESOURCE_UPDATE' },
        { label: '删除资源', value: 'RESOURCE_DELETE' },
        { label: '权限申请', value: 'USER_PERMISSION' }
      ],
      statusOptions: [
        { label: '待审批', value: 'PENDING' },
        { label: '已通过', value: 'APPROVED' },
        { label: '已拒绝', value: 'REJECTED' },
        { label: '已取消', value: 'CANCELED' }
      ],
      dialogFormVisible: false,
      dialogTitle: '',
      approvalType: 'approve', // 'approve' 或 'reject'
      approvalForm: {
        id: null,
        comment: ''
      },
      approvalRules: {
        comment: [{ required: true, message: '请输入审批意见', trigger: 'blur' }]
      },
      currentUser: {
        username: 'admin', // 模拟当前用户，实际应从全局状态或会话获取
        roles: ['admin']
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchPendingApprovals(this.listQuery)
        .then(response => {
          this.list = response.data.items
          this.total = response.data.total
        })
        .catch(error => {
          console.error('获取审批列表失败:', error)
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+id'
      } else {
        this.listQuery.sort = '-id'
      }
      this.handleFilter()
    },
    handleDetail(row) {
      this.$router.push(`/approvals/detail/${row.id}`)
    },
    canApprove(row) {
      // 检查当前用户是否有权限审批
      return row.status === 'PENDING' && 
        (row.currentApprover === this.currentUser.username || this.currentUser.roles.includes('admin'))
    },
    canReject(row) {
      // 检查当前用户是否有权限拒绝
      return row.status === 'PENDING' && 
        (row.currentApprover === this.currentUser.username || this.currentUser.roles.includes('admin'))
    },
    handleApprove(row) {
      this.approvalType = 'approve'
      this.dialogTitle = '审批通过'
      this.approvalForm.id = row.id
      this.approvalForm.comment = ''
      this.dialogFormVisible = true
    },
    handleReject(row) {
      this.approvalType = 'reject'
      this.dialogTitle = '审批拒绝'
      this.approvalForm.id = row.id
      this.approvalForm.comment = ''
      this.dialogFormVisible = true
    },
    submitApproval() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          const approvalPromise = this.approvalType === 'approve'
            ? approveRequest(this.approvalForm.id, this.approvalForm.comment)
            : rejectRequest(this.approvalForm.id, this.approvalForm.comment)

          approvalPromise
            .then(() => {
              this.$notify({
                title: '成功',
                message: `审批${this.approvalType === 'approve' ? '通过' : '拒绝'}成功`,
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getList()
            })
            .catch(error => {
              console.error('审批操作失败:', error)
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.link-type {
  color: #337ab7;
  cursor: pointer;
}
</style> 