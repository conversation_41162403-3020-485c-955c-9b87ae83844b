<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.type" placeholder="审批类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in approvalTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.status" placeholder="状态" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        align="right"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        class="filter-item date-range"
      />
      <el-input
        v-model="listQuery.keyword"
        placeholder="关键字"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        @click="getList"
      >
        刷新
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.type | approvalTypeFilter">{{ row.type | approvalTypeLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="标题" min-width="150px" align="center">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleDetail(row)">{{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.creator }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="180px" align="center" sortable="custom" prop="createdAt">
        <template slot-scope="{row}">
          <span>{{ row.createdAt | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成时间" width="180px" align="center" sortable="custom" prop="updatedAt">
        <template slot-scope="{row}">
          <span>{{ row.updatedAt | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="120" align="center" sortable="custom" prop="status">
        <template slot-scope="{row}">
          <el-tag :type="row.status | statusFilter">{{ row.status | statusLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审批人" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.approver || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button size="mini" @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { fetchApprovalHistory } from '@/api/approvals'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'ApprovalHistory',
  components: { Pagination },
  directives: { waves },
  filters: {
    approvalTypeFilter(type) {
      const typeMap = {
        'CLUSTER_CREATE': 'primary',
        'CLUSTER_DELETE': 'danger',
        'CLUSTER_SCALE': 'warning',
        'RESOURCE_CREATE': 'success',
        'RESOURCE_UPDATE': 'info',
        'RESOURCE_DELETE': 'danger',
        'USER_PERMISSION': 'primary'
      }
      return typeMap[type] || 'info'
    },
    approvalTypeLabel(type) {
      const labelMap = {
        'CLUSTER_CREATE': '创建集群',
        'CLUSTER_DELETE': '删除集群',
        'CLUSTER_SCALE': '集群扩缩',
        'RESOURCE_CREATE': '创建资源',
        'RESOURCE_UPDATE': '更新资源',
        'RESOURCE_DELETE': '删除资源',
        'USER_PERMISSION': '权限申请'
      }
      return labelMap[type] || type
    },
    statusFilter(status) {
      const statusMap = {
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'CANCELED': 'info'
      }
      return statusMap[status] || 'info'
    },
    statusLabel(status) {
      const labelMap = {
        'PENDING': '待审批',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELED': '已取消'
      }
      return labelMap[status] || status
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        type: undefined,
        status: undefined,
        keyword: undefined,
        startTime: undefined,
        endTime: undefined,
        sort: '-updatedAt'
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      approvalTypeOptions: [
        { label: '创建集群', value: 'CLUSTER_CREATE' },
        { label: '删除集群', value: 'CLUSTER_DELETE' },
        { label: '集群扩缩', value: 'CLUSTER_SCALE' },
        { label: '创建资源', value: 'RESOURCE_CREATE' },
        { label: '更新资源', value: 'RESOURCE_UPDATE' },
        { label: '删除资源', value: 'RESOURCE_DELETE' },
        { label: '权限申请', value: 'USER_PERMISSION' }
      ],
      statusOptions: [
        { label: '待审批', value: 'PENDING' },
        { label: '已通过', value: 'APPROVED' },
        { label: '已拒绝', value: 'REJECTED' },
        { label: '已取消', value: 'CANCELED' }
      ]
    }
  },
  watch: {
    dateRange(val) {
      if (val && val.length === 2) {
        this.listQuery.startTime = val[0]
        this.listQuery.endTime = val[1]
      } else {
        this.listQuery.startTime = undefined
        this.listQuery.endTime = undefined
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      fetchApprovalHistory(this.listQuery)
        .then(response => {
          this.list = response.data.items
          this.total = response.data.total
        })
        .catch(error => {
          console.error('获取审批历史失败:', error)
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop) {
        this.listQuery.sort = (order === 'ascending' ? '+' : '-') + prop
      }
      this.handleFilter()
    },
    handleDetail(row) {
      this.$router.push({
        path: `/approvals/detail/${row.id}`,
        query: { from: 'history' }
      })
    },
    handleDownload() {
      this.$confirm('确认导出审批历史数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        
        // 使用当前查询条件获取所有数据
        const exportQuery = { ...this.listQuery, limit: 1000, page: 1 }
        
        fetchApprovalHistory(exportQuery)
          .then(response => {
            const data = response.data.items
            
            // 格式化数据
            const exportData = data.map(item => {
              return {
                '审批ID': item.id,
                '审批类型': this.$options.filters.approvalTypeLabel(item.type),
                '标题': item.title,
                '创建人': item.creator,
                '创建时间': parseTime(item.createdAt),
                '完成时间': parseTime(item.updatedAt),
                '状态': this.$options.filters.statusLabel(item.status),
                '审批人': item.approver || '-'
              }
            })
            
            // 创建工作表
            import('@/vendor/Export2Excel').then(excel => {
              const tHeader = ['审批ID', '审批类型', '标题', '创建人', '创建时间', '完成时间', '状态', '审批人']
              const filterVal = ['审批ID', '审批类型', '标题', '创建人', '创建时间', '完成时间', '状态', '审批人']
              excel.export_json_to_excel({
                header: tHeader,
                data: exportData,
                filename: '审批历史',
                autoWidth: true,
                bookType: 'xlsx'
              })
              this.downloadLoading = false
            })
          })
          .catch(error => {
            console.error('导出审批历史失败:', error)
            this.downloadLoading = false
          })
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.date-range {
  width: 300px;
}

.link-type {
  color: #337ab7;
  cursor: pointer;
}
</style> 