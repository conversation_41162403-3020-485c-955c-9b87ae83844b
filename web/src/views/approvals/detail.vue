<template>
  <div class="app-container">
    <div v-loading="loading" class="approval-detail">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>审批详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
        </div>
        
        <el-descriptions class="margin-top" title="基本信息" :column="2" border>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-tickets"></i>
              审批编号
            </template>
            {{ approval.id }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-user"></i>
              创建人
            </template>
            {{ approval.creator }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-office-building"></i>
              审批类型
            </template>
            <el-tag :type="approval.type | approvalTypeFilter">{{ approval.type | approvalTypeLabel }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-date"></i>
              创建时间
            </template>
            {{ approval.createdAt | parseTime }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-s-flag"></i>
              状态
            </template>
            <el-tag :type="approval.status | statusFilter">{{ approval.status | statusLabel }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class="el-icon-date"></i>
              更新时间
            </template>
            {{ approval.updatedAt | parseTime }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="title-divider">
          <div class="divider-content">审批标题</div>
        </div>
        <div class="approval-title">{{ approval.title }}</div>
        
        <div class="title-divider">
          <div class="divider-content">审批内容</div>
        </div>
        <div class="approval-content">{{ approval.content }}</div>
        
        <div v-if="approval.yamlContent" class="yaml-section">
          <div class="title-divider">
            <div class="divider-content">资源定义</div>
          </div>
          <div class="yaml-content">
            <pre>{{ approval.yamlContent }}</pre>
          </div>
        </div>
        
        <div class="title-divider">
          <div class="divider-content">审批流程</div>
        </div>
        <el-steps :active="activeStep" finish-status="success" align-center>
          <el-step v-for="(step, index) in approval.steps" :key="index" :title="step.title" :description="step.description">
            <template slot="icon">
              <el-avatar :size="small" :src="step.avatar"></el-avatar>
            </template>
          </el-step>
        </el-steps>
        
        <div class="title-divider">
          <div class="divider-content">审批记录</div>
        </div>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in approval.activities"
            :key="index"
            :type="activity.type | activityTypeFilter"
            :timestamp="activity.timestamp | parseTime"
            :icon="activity.type | activityIconFilter"
          >
            <el-card class="activity-card">
              <div class="activity-user">
                <span class="user-name">{{ activity.username }}</span>
                <span class="user-action">{{ activity.action }}</span>
              </div>
              <div v-if="activity.comment" class="activity-comment">
                {{ activity.comment }}
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <div v-if="canApprove || canReject" class="approval-actions">
          <el-button v-if="canApprove" type="success" @click="handleApprove">通过</el-button>
          <el-button v-if="canReject" type="danger" @click="handleReject">拒绝</el-button>
        </div>
      </el-card>
    </div>
    
    <!-- 审批对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogFormVisible" width="500px">
      <el-form ref="dataForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批意见" prop="comment">
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            placeholder="请输入审批意见"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApprovalDetail, approveRequest, rejectRequest } from '@/api/approvals'
import { parseTime } from '@/utils'

export default {
  name: 'ApprovalDetail',
  filters: {
    approvalTypeFilter(type) {
      const typeMap = {
        'CLUSTER_CREATE': 'primary',
        'CLUSTER_DELETE': 'danger',
        'CLUSTER_SCALE': 'warning',
        'RESOURCE_CREATE': 'success',
        'RESOURCE_UPDATE': 'info',
        'RESOURCE_DELETE': 'danger',
        'USER_PERMISSION': 'primary'
      }
      return typeMap[type] || 'info'
    },
    approvalTypeLabel(type) {
      const labelMap = {
        'CLUSTER_CREATE': '创建集群',
        'CLUSTER_DELETE': '删除集群',
        'CLUSTER_SCALE': '集群扩缩',
        'RESOURCE_CREATE': '创建资源',
        'RESOURCE_UPDATE': '更新资源',
        'RESOURCE_DELETE': '删除资源',
        'USER_PERMISSION': '权限申请'
      }
      return labelMap[type] || type
    },
    statusFilter(status) {
      const statusMap = {
        'PENDING': 'warning',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'CANCELED': 'info'
      }
      return statusMap[status] || 'info'
    },
    statusLabel(status) {
      const labelMap = {
        'PENDING': '待审批',
        'APPROVED': '已通过',
        'REJECTED': '已拒绝',
        'CANCELED': '已取消'
      }
      return labelMap[status] || status
    },
    activityTypeFilter(type) {
      const typeMap = {
        'create': 'primary',
        'approve': 'success',
        'reject': 'danger',
        'comment': 'info',
        'cancel': 'warning'
      }
      return typeMap[type] || 'info'
    },
    activityIconFilter(type) {
      const iconMap = {
        'create': 'el-icon-plus',
        'approve': 'el-icon-check',
        'reject': 'el-icon-close',
        'comment': 'el-icon-chat-dot-round',
        'cancel': 'el-icon-remove'
      }
      return iconMap[type]
    },
    parseTime
  },
  data() {
    return {
      approvalId: null,
      loading: false,
      approval: {
        id: '',
        title: '',
        content: '',
        type: '',
        status: '',
        creator: '',
        createdAt: '',
        updatedAt: '',
        currentApprover: '',
        yamlContent: '',
        steps: [],
        activities: []
      },
      activeStep: 0,
      dialogFormVisible: false,
      dialogTitle: '',
      approvalType: 'approve', // 'approve' 或 'reject'
      approvalForm: {
        comment: ''
      },
      approvalRules: {
        comment: [{ required: true, message: '请输入审批意见', trigger: 'blur' }]
      },
      currentUser: {
        username: 'admin', // 模拟当前用户，实际应从全局状态或会话获取
        roles: ['admin']
      }
    }
  },
  computed: {
    canApprove() {
      return this.approval.status === 'PENDING' && 
        (this.approval.currentApprover === this.currentUser.username || this.currentUser.roles.includes('admin'))
    },
    canReject() {
      return this.approval.status === 'PENDING' &&
        (this.approval.currentApprover === this.currentUser.username || this.currentUser.roles.includes('admin'))
    }
  },
  created() {
    this.approvalId = this.$route.params.id
    this.getApprovalDetail()
  },
  methods: {
    getApprovalDetail() {
      this.loading = true
      getApprovalDetail(this.approvalId)
        .then(response => {
          this.approval = response.data
          
          // 计算当前步骤
          this.calculateActiveStep()
        })
        .catch(error => {
          console.error('获取审批详情失败:', error)
          this.$notify({
            title: '错误',
            message: '获取审批详情失败',
            type: 'error',
            duration: 2000
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    calculateActiveStep() {
      if (this.approval.status === 'APPROVED') {
        this.activeStep = this.approval.steps.length
      } else if (this.approval.status === 'REJECTED') {
        // 找出被拒绝的步骤
        for (let i = 0; i < this.approval.steps.length; i++) {
          if (this.approval.steps[i].status === 'REJECTED') {
            this.activeStep = i
            break
          }
        }
      } else {
        // 待审批状态，找出当前步骤
        for (let i = 0; i < this.approval.steps.length; i++) {
          if (this.approval.steps[i].status === 'PENDING') {
            this.activeStep = i
            break
          }
        }
      }
    },
    handleApprove() {
      this.approvalType = 'approve'
      this.dialogTitle = '审批通过'
      this.approvalForm.comment = ''
      this.dialogFormVisible = true
    },
    handleReject() {
      this.approvalType = 'reject'
      this.dialogTitle = '审批拒绝'
      this.approvalForm.comment = ''
      this.dialogFormVisible = true
    },
    submitApproval() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          const approvalPromise = this.approvalType === 'approve'
            ? approveRequest(this.approvalId, this.approvalForm.comment)
            : rejectRequest(this.approvalId, this.approvalForm.comment)

          approvalPromise
            .then(() => {
              this.$notify({
                title: '成功',
                message: `审批${this.approvalType === 'approve' ? '通过' : '拒绝'}成功`,
                type: 'success',
                duration: 2000
              })
              this.dialogFormVisible = false
              this.getApprovalDetail()
            })
            .catch(error => {
              console.error('审批操作失败:', error)
            })
        }
      })
    },
    goBack() {
      if (this.$route.query.from === 'history') {
        this.$router.push('/approvals/history')
      } else {
        this.$router.push('/approvals/pending')
      }
    }
  }
}
</script>

<style scoped>
.approval-detail {
  padding: 20px;
}

.title-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
}

.title-divider::before,
.title-divider::after {
  content: '';
  flex-grow: 1;
  height: 1px;
  background-color: #ebeef5;
}

.divider-content {
  padding: 0 15px;
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.approval-title {
  font-size: 18px;
  font-weight: bold;
  padding: 10px;
  background-color: #f8f8f8;
  border-left: 4px solid #409EFF;
}

.approval-content {
  margin: 20px 0;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  white-space: pre-line;
}

.yaml-section {
  margin-top: 20px;
}

.yaml-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 15px;
  background-color: #2d2d2d;
  border-radius: 4px;
}

.yaml-content pre {
  margin: 0;
  color: #e6e6e6;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.activity-card {
  margin-bottom: 0;
}

.activity-user {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-name {
  font-weight: bold;
  margin-right: 10px;
}

.user-action {
  color: #909399;
}

.activity-comment {
  font-size: 14px;
  color: #606266;
  white-space: pre-line;
}

.approval-actions {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style> 