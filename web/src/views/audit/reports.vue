<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="chartParams.cluster" placeholder="集群" clearable style="width: 180px" class="filter-item">
        <el-option v-for="item in clusterOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="chartParams.type" placeholder="操作类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in operationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        align="right"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
        class="filter-item date-range"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        @click="getStatistics"
      >
        刷新
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <div v-loading="loading" class="dashboard-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>总操作数</span>
            </div>
            <div class="card-panel">
              <div class="card-panel-icon-wrapper">
                <svg-icon icon-class="documentation" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">总操作</div>
                <count-to :start-val="0" :end-val="parseInt(statistics.totalOperations)" :duration="2000" class="card-panel-num" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>成功操作</span>
            </div>
            <div class="card-panel">
              <div class="card-panel-icon-wrapper" style="color: #67C23A;">
                <svg-icon icon-class="success" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">成功</div>
                <count-to :start-val="0" :end-val="parseInt(statistics.successOperations)" :duration="2000" class="card-panel-num" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>失败操作</span>
            </div>
            <div class="card-panel">
              <div class="card-panel-icon-wrapper" style="color: #F56C6C;">
                <svg-icon icon-class="bug" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">失败</div>
                <count-to :start-val="0" :end-val="parseInt(statistics.failedOperations)" :duration="2000" class="card-panel-num" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>操作用户数</span>
            </div>
            <div class="card-panel">
              <div class="card-panel-icon-wrapper" style="color: #409EFF;">
                <svg-icon icon-class="peoples" class-name="card-panel-icon" />
              </div>
              <div class="card-panel-description">
                <div class="card-panel-text">用户数</div>
                <count-to :start-val="0" :end-val="parseInt(statistics.uniqueUsers)" :duration="2000" class="card-panel-num" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>操作类型分布</span>
            </div>
            <div class="chart-wrapper">
              <pie-chart :chart-data="operationTypeData" height="350px" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>用户操作排行</span>
            </div>
            <div class="chart-wrapper">
              <bar-chart :chart-data="userOperationsData" height="350px" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>操作趋势图</span>
            </div>
            <div class="chart-wrapper">
              <line-chart :chart-data="operationTrendData" height="350px" />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>集群操作分布</span>
              <el-tooltip content="根据不同集群的操作数量统计" placement="top">
                <i class="el-icon-question" style="margin-left: 5px;"></i>
              </el-tooltip>
            </div>
            <div class="chart-wrapper">
              <pie-chart :chart-data="clusterOperationsData" height="350px" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>资源操作排行</span>
              <el-tooltip content="操作最频繁的资源类型" placement="top">
                <i class="el-icon-question" style="margin-left: 5px;"></i>
              </el-tooltip>
            </div>
            <div class="chart-wrapper">
              <bar-chart :chart-data="resourceOperationsData" height="350px" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { fetchAuditStatistics } from '@/api/audit'
import { fetchClusterList } from '@/api/clusters'
import waves from '@/directive/waves'
import PieChart from '@/components/Charts/PieChart'
import BarChart from '@/components/Charts/BarChart'
import LineChart from '@/components/Charts/LineChart'
import CountTo from 'vue-count-to'

export default {
  name: 'AuditReports',
  components: {
    PieChart,
    BarChart,
    LineChart,
    CountTo
  },
  directives: { waves },
  data() {
    return {
      loading: false,
      chartParams: {
        cluster: undefined,
        type: undefined,
        startTime: undefined,
        endTime: undefined
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      clusterOptions: [],
      operationTypeOptions: [
        { label: '创建', value: 'CREATE' },
        { label: '更新', value: 'UPDATE' },
        { label: '删除', value: 'DELETE' },
        { label: '登录', value: 'LOGIN' },
        { label: '登出', value: 'LOGOUT' },
        { label: '审批通过', value: 'APPROVE' },
        { label: '审批拒绝', value: 'REJECT' },
        { label: '执行命令', value: 'EXECUTE' }
      ],
      statistics: {
        totalOperations: 0,
        successOperations: 0,
        failedOperations: 0,
        uniqueUsers: 0,
        operationTypes: [],
        userOperations: [],
        operationTrend: [],
        clusterOperations: [],
        resourceOperations: []
      },
      operationTypeData: {
        title: '操作类型分布',
        data: []
      },
      userOperationsData: {
        title: '用户操作排行',
        xAxis: [],
        series: [{
          name: '操作次数',
          data: []
        }]
      },
      operationTrendData: {
        title: '操作趋势图',
        xAxis: [],
        series: [
          {
            name: '总操作',
            smooth: true,
            data: []
          },
          {
            name: '成功',
            smooth: true,
            data: []
          },
          {
            name: '失败',
            smooth: true,
            data: []
          }
        ]
      },
      clusterOperationsData: {
        title: '集群操作分布',
        data: []
      },
      resourceOperationsData: {
        title: '资源操作排行',
        xAxis: [],
        series: [{
          name: '操作次数',
          data: []
        }]
      }
    }
  },
  watch: {
    dateRange(val) {
      if (val && val.length === 2) {
        this.chartParams.startTime = val[0]
        this.chartParams.endTime = val[1]
      } else {
        this.chartParams.startTime = undefined
        this.chartParams.endTime = undefined
      }
    }
  },
  created() {
    this.getClusterList()
    this.getStatistics()
  },
  methods: {
    getClusterList() {
      fetchClusterList({ limit: 100 })
        .then(response => {
          this.clusterOptions = response.data.items
        })
        .catch(error => {
          console.error('获取集群列表失败:', error)
        })
    },
    getStatistics() {
      this.loading = true
      fetchAuditStatistics(this.chartParams)
        .then(response => {
          this.statistics = response.data

          // 处理图表数据
          this.processChartData()
        })
        .catch(error => {
          console.error('获取审计统计数据失败:', error)
        })
        .finally(() => {
          this.loading = false
        })
    },
    processChartData() {
      // 操作类型分布
      this.operationTypeData.data = this.statistics.operationTypes.map(item => ({
        name: this.getOperationTypeLabel(item.type),
        value: item.count
      }))

      // 用户操作排行
      const sortedUsers = [...this.statistics.userOperations]
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
      
      this.userOperationsData.xAxis = sortedUsers.map(item => item.username)
      this.userOperationsData.series[0].data = sortedUsers.map(item => item.count)

      // 操作趋势图
      this.operationTrendData.xAxis = this.statistics.operationTrend.map(item => item.date)
      this.operationTrendData.series[0].data = this.statistics.operationTrend.map(item => item.total)
      this.operationTrendData.series[1].data = this.statistics.operationTrend.map(item => item.success)
      this.operationTrendData.series[2].data = this.statistics.operationTrend.map(item => item.failure)

      // 集群操作分布
      this.clusterOperationsData.data = this.statistics.clusterOperations.map(item => ({
        name: item.cluster || '未指定集群',
        value: item.count
      }))

      // 资源操作排行
      const sortedResources = [...this.statistics.resourceOperations]
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)
      
      this.resourceOperationsData.xAxis = sortedResources.map(item => item.resourceType || '未指定资源')
      this.resourceOperationsData.series[0].data = sortedResources.map(item => item.count)
    },
    getOperationTypeLabel(type) {
      const labelMap = {
        'CREATE': '创建',
        'UPDATE': '更新',
        'DELETE': '删除',
        'LOGIN': '登录',
        'LOGOUT': '登出',
        'APPROVE': '审批通过',
        'REJECT': '审批拒绝',
        'EXECUTE': '执行命令'
      }
      return labelMap[type] || type
    },
    handleFilter() {
      this.getStatistics()
    },
    handleDownload() {
      this.$confirm('确认导出审计报表数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        
        // 创建工作簿，添加多个工作表
        import('@/vendor/Export2Excel').then(excel => {
          const operationTypes = this.statistics.operationTypes.map(item => {
            return {
              '操作类型': this.getOperationTypeLabel(item.type),
              '操作次数': item.count,
              '占比': (item.count / this.statistics.totalOperations * 100).toFixed(2) + '%'
            }
          })

          const userOperations = this.statistics.userOperations.map(item => {
            return {
              '用户名': item.username,
              '操作次数': item.count,
              '占比': (item.count / this.statistics.totalOperations * 100).toFixed(2) + '%'
            }
          })

          const operationTrend = this.statistics.operationTrend.map(item => {
            return {
              '日期': item.date,
              '总操作': item.total,
              '成功': item.success,
              '失败': item.failure
            }
          })

          const clusterOperations = this.statistics.clusterOperations.map(item => {
            return {
              '集群': item.cluster || '未指定集群',
              '操作次数': item.count,
              '占比': (item.count / this.statistics.totalOperations * 100).toFixed(2) + '%'
            }
          })

          const resourceOperations = this.statistics.resourceOperations.map(item => {
            return {
              '资源类型': item.resourceType || '未指定资源',
              '操作次数': item.count,
              '占比': (item.count / this.statistics.totalOperations * 100).toFixed(2) + '%'
            }
          })

          const summary = [{
            '总操作数': this.statistics.totalOperations,
            '成功操作数': this.statistics.successOperations,
            '失败操作数': this.statistics.failedOperations,
            '操作用户数': this.statistics.uniqueUsers
          }]

          // 使用multipleSheetType参数导出多个工作表
          excel.export_json_to_excel({
            header1: ['操作类型', '操作次数', '占比'],
            data1: operationTypes,
            header2: ['用户名', '操作次数', '占比'],
            data2: userOperations,
            header3: ['日期', '总操作', '成功', '失败'],
            data3: operationTrend,
            header4: ['集群', '操作次数', '占比'],
            data4: clusterOperations,
            header5: ['资源类型', '操作次数', '占比'],
            data5: resourceOperations,
            header6: ['总操作数', '成功操作数', '失败操作数', '操作用户数'],
            data6: summary,
            filename: '审计报表',
            sheetnames: ['操作类型分布', '用户操作排行', '操作趋势', '集群操作分布', '资源操作排行', '统计概要'],
            merges: [],
            multipleSheetType: true,
            bookType: 'xlsx'
          })
          
          this.downloadLoading = false
        })
      })
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  margin: 30px 0;
}

.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.date-range {
  width: 350px;
}

.chart-wrapper {
  background: #fff;
  padding: 15px 0;
  margin-bottom: 10px;
}

.card-panel {
  height: 120px;
  cursor: pointer;
  font-size: 12px;
  position: relative;
  overflow: hidden;
  color: #666;
  background: #fff;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
  border-color: rgba(0, 0, 0, .05);
  display: flex;
  align-items: center;
}

.card-panel-icon-wrapper {
  float: left;
  margin: 14px 0 0 14px;
  padding: 16px;
  transition: all 0.38s ease-out;
  border-radius: 6px;
}

.card-panel-icon {
  float: left;
  font-size: 48px;
}

.card-panel-description {
  float: right;
  font-weight: bold;
  margin: 26px 26px 26px 0;
  display: flex;
  flex-direction: column;
  text-align: right;
  flex: 1;
}

.card-panel-text {
  line-height: 18px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  margin-bottom: 12px;
}

.card-panel-num {
  font-size: 24px;
}
</style> 