<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.cluster" placeholder="集群" clearable style="width: 180px" class="filter-item">
        <el-option v-for="item in clusterOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.type" placeholder="操作类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in operationTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-select v-model="listQuery.result" placeholder="结果" clearable style="width: 120px" class="filter-item">
        <el-option v-for="item in resultOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="listQuery.username"
        placeholder="用户名"
        style="width: 150px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-date-picker
        v-model="dateRange"
        type="datetimerange"
        align="right"
        unlink-panels
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="yyyy-MM-dd HH:mm:ss"
        :picker-options="pickerOptions"
        class="filter-item date-range"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        @click="getList"
      >
        刷新
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-download"
        @click="handleDownload"
      >
        导出
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column label="ID" prop="id" sortable="custom" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.username }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作类型" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.type | operationTypeFilter">{{ row.type | operationTypeLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="集群" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.cluster || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源" width="200" align="center">
        <template slot-scope="{row}">
          <span v-if="row.resource">{{ row.resourceType }}/{{ row.resource }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="命名空间" width="120" align="center">
        <template slot-scope="{row}">
          <span>{{ row.namespace || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作内容" min-width="180" align="center">
        <template slot-scope="{row}">
          <el-tooltip v-if="row.content && row.content.length > 50" :content="row.content" placement="top">
            <span>{{ row.content.substring(0, 50) }}...</span>
          </el-tooltip>
          <span v-else>{{ row.content || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" width="180px" align="center" sortable="custom" prop="timestamp">
        <template slot-scope="{row}">
          <span>{{ row.timestamp | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结果" width="120" align="center">
        <template slot-scope="{row}">
          <el-tag :type="row.result | resultFilter">{{ row.result | resultLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" width="140" align="center">
        <template slot-scope="{row}">
          <span>{{ row.ipAddress }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="{row}">
          <el-button size="mini" type="primary" @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="dialogVisible" width="60%">
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-user"></i>
            用户名
          </template>
          {{ currentLog.username }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-s-operation"></i>
            操作类型
          </template>
          <el-tag :type="currentLog.type | operationTypeFilter">{{ currentLog.type | operationTypeLabel }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-s-platform"></i>
            集群
          </template>
          {{ currentLog.cluster || '-' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-document"></i>
            资源类型/名称
          </template>
          {{ currentLog.resourceType ? `${currentLog.resourceType}/${currentLog.resource}` : '-' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-collection-tag"></i>
            命名空间
          </template>
          {{ currentLog.namespace || '-' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-date"></i>
            操作时间
          </template>
          {{ currentLog.timestamp | parseTime }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-circle-check"></i>
            结果
          </template>
          <el-tag :type="currentLog.result | resultFilter">{{ currentLog.result | resultLabel }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-location-outline"></i>
            IP地址
          </template>
          {{ currentLog.ipAddress }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-s-order"></i>
            请求ID
          </template>
          {{ currentLog.requestId || '-' }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-document-copy"></i>
            操作内容
          </template>
          <div class="log-content">{{ currentLog.content || '-' }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="currentLog.diff">
          <template slot="label">
            <i class="el-icon-refresh"></i>
            变更详情
          </template>
          <div class="diff-content">
            <pre>{{ currentLog.diff }}</pre>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAuditLogs } from '@/api/audit'
import { fetchClusterList } from '@/api/clusters'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'AuditLogs',
  components: { Pagination },
  directives: { waves },
  filters: {
    operationTypeFilter(type) {
      const typeMap = {
        'CREATE': 'success',
        'UPDATE': 'warning',
        'DELETE': 'danger',
        'LOGIN': 'info',
        'LOGOUT': 'info',
        'APPROVE': 'primary',
        'REJECT': 'danger',
        'EXECUTE': 'warning'
      }
      return typeMap[type] || 'info'
    },
    operationTypeLabel(type) {
      const labelMap = {
        'CREATE': '创建',
        'UPDATE': '更新',
        'DELETE': '删除',
        'LOGIN': '登录',
        'LOGOUT': '登出',
        'APPROVE': '审批通过',
        'REJECT': '审批拒绝',
        'EXECUTE': '执行命令'
      }
      return labelMap[type] || type
    },
    resultFilter(result) {
      return result === 'SUCCESS' ? 'success' : 'danger'
    },
    resultLabel(result) {
      return result === 'SUCCESS' ? '成功' : '失败'
    }
  },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: undefined,
        type: undefined,
        result: undefined,
        username: undefined,
        startTime: undefined,
        endTime: undefined,
        sort: '-timestamp'
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '今天',
          onClick(picker) {
            const end = new Date()
            const start = new Date(new Date().toDateString())
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      clusterOptions: [],
      operationTypeOptions: [
        { label: '创建', value: 'CREATE' },
        { label: '更新', value: 'UPDATE' },
        { label: '删除', value: 'DELETE' },
        { label: '登录', value: 'LOGIN' },
        { label: '登出', value: 'LOGOUT' },
        { label: '审批通过', value: 'APPROVE' },
        { label: '审批拒绝', value: 'REJECT' },
        { label: '执行命令', value: 'EXECUTE' }
      ],
      resultOptions: [
        { label: '成功', value: 'SUCCESS' },
        { label: '失败', value: 'FAILURE' }
      ],
      dialogVisible: false,
      currentLog: {}
    }
  },
  watch: {
    dateRange(val) {
      if (val && val.length === 2) {
        this.listQuery.startTime = val[0]
        this.listQuery.endTime = val[1]
      } else {
        this.listQuery.startTime = undefined
        this.listQuery.endTime = undefined
      }
    }
  },
  created() {
    this.getClusterList()
    this.getList()
  },
  methods: {
    getClusterList() {
      fetchClusterList({ limit: 100 })
        .then(response => {
          this.clusterOptions = response.data.items
        })
        .catch(error => {
          console.error('获取集群列表失败:', error)
        })
    },
    getList() {
      this.listLoading = true
      fetchAuditLogs(this.listQuery)
        .then(response => {
          this.list = response.data.items
          this.total = response.data.total
        })
        .catch(error => {
          console.error('获取审计日志失败:', error)
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop) {
        this.listQuery.sort = (order === 'ascending' ? '+' : '-') + prop
      }
      this.handleFilter()
    },
    handleDetail(row) {
      this.currentLog = { ...row }
      this.dialogVisible = true
    },
    handleDownload() {
      this.$confirm('确认导出审计日志数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.downloadLoading = true
        
        // 使用当前查询条件获取所有数据
        const exportQuery = { ...this.listQuery, limit: 1000, page: 1 }
        
        fetchAuditLogs(exportQuery)
          .then(response => {
            const data = response.data.items
            
            // 格式化数据
            const exportData = data.map(item => {
              return {
                '日志ID': item.id,
                '用户名': item.username,
                '操作类型': this.$options.filters.operationTypeLabel(item.type),
                '集群': item.cluster || '-',
                '资源': item.resource ? `${item.resourceType}/${item.resource}` : '-',
                '命名空间': item.namespace || '-',
                '操作内容': item.content || '-',
                '操作时间': parseTime(item.timestamp),
                '结果': this.$options.filters.resultLabel(item.result),
                'IP地址': item.ipAddress,
                '请求ID': item.requestId || '-'
              }
            })
            
            // 创建工作表
            import('@/vendor/Export2Excel').then(excel => {
              const tHeader = ['日志ID', '用户名', '操作类型', '集群', '资源', '命名空间', '操作内容', '操作时间', '结果', 'IP地址', '请求ID']
              const filterVal = ['日志ID', '用户名', '操作类型', '集群', '资源', '命名空间', '操作内容', '操作时间', '结果', 'IP地址', '请求ID']
              excel.export_json_to_excel({
                header: tHeader,
                data: exportData,
                filename: '审计日志',
                autoWidth: true,
                bookType: 'xlsx'
              })
              this.downloadLoading = false
            })
          })
          .catch(error => {
            console.error('导出审计日志失败:', error)
            this.downloadLoading = false
          })
      })
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.date-range {
  width: 400px;
}

.log-content {
  white-space: pre-line;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}

.diff-content {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.diff-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 12px;
  line-height: 1.5;
}
</style> 