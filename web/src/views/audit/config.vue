<template>
  <div class="app-container">
    <el-card v-loading="loading" class="box-card">
      <div slot="header" class="clearfix">
        <span>审计配置</span>
        <el-button v-if="checkPermission(['audit:config:update'])" style="float: right; padding: 3px 0" type="text" @click="handleSave">保存配置</el-button>
      </div>

      <el-form ref="form" :model="form" label-width="150px" :rules="rules">
        <el-divider content-position="left">归档设置</el-divider>
        
        <el-form-item label="自动归档" prop="autoArchive">
          <el-switch v-model="form.autoArchive" />
          <span class="form-help">启用后系统将按照设定的频率自动归档审计日志</span>
        </el-form-item>
        
        <el-form-item label="归档频率" prop="archiveFrequency">
          <el-select v-model="form.archiveFrequency" :disabled="!form.autoArchive">
            <el-option label="每月" value="monthly" />
            <el-option label="每季度" value="quarterly" />
            <el-option label="每半年" value="half-yearly" />
            <el-option label="每年" value="yearly" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="保留天数" prop="retentionDays">
          <el-input-number v-model="form.retentionDays" :min="1" :max="3650" />
          <span class="form-help">数据库中保留审计日志的天数，超过此天数的日志将被归档或删除</span>
        </el-form-item>
        
        <el-divider content-position="left">存储设置</el-divider>
        
        <el-form-item label="归档存储类型" prop="storageType">
          <el-radio-group v-model="form.storageType">
            <el-radio label="local">本地存储</el-radio>
            <el-radio label="obs">华为云OBS</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <template v-if="form.storageType === 'obs'">
          <el-form-item label="OBS存储桶" prop="obsBucket">
            <el-input v-model="form.obsBucket" placeholder="请输入OBS存储桶名称" />
          </el-form-item>
          
          <el-form-item label="存储路径前缀" prop="obsPrefix">
            <el-input v-model="form.obsPrefix" placeholder="例如: audit-logs/" />
            <span class="form-help">在OBS存储桶中的存储路径前缀，如 audit-logs/</span>
          </el-form-item>
        </template>
        
        <template v-else>
          <el-form-item label="本地存储路径" prop="localPath">
            <el-input v-model="form.localPath" placeholder="例如: /data/audit-archives/" />
          </el-form-item>
        </template>
        
        <el-divider content-position="left">加密设置</el-divider>
        
        <el-form-item label="启用加密" prop="enableEncryption">
          <el-switch v-model="form.enableEncryption" />
          <span class="form-help">启用后归档文件将使用AES-256加密</span>
        </el-form-item>
        
        <el-form-item v-if="form.enableEncryption" label="加密密钥" prop="encryptionKey">
          <el-input v-model="form.encryptionKey" placeholder="请输入加密密钥" show-password />
          <span class="form-help">请妥善保管密钥，丢失后无法恢复已加密的归档文件</span>
        </el-form-item>
        
        <el-divider content-position="left">清理设置</el-divider>
        
        <el-form-item label="自动清理" prop="autoCleanup">
          <el-switch v-model="form.autoCleanup" />
          <span class="form-help">启用后系统将自动清理已归档的数据库记录</span>
        </el-form-item>
        
        <el-form-item label="归档后清理" prop="cleanupAfterArchive">
          <el-switch v-model="form.cleanupAfterArchive" :disabled="!form.autoCleanup" />
          <span class="form-help">启用后系统将在成功归档后立即清理已归档的数据</span>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button v-if="checkPermission(['audit:cleanup'])" type="warning" @click="handleCleanup">手动清理过期数据</el-button>
        <el-button type="primary" @click="handleSave">保存配置</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getArchiveConfig, updateArchiveConfig, cleanupExpiredData } from '@/api/audit'
import checkPermission from '@/utils/permission'

export default {
  name: 'AuditConfig',
  data() {
    return {
      loading: true,
      form: {
        autoArchive: true,
        archiveFrequency: 'quarterly',
        retentionDays: 90,
        storageType: 'obs',
        obsBucket: '',
        obsPrefix: 'audit-logs/',
        localPath: '/data/audit-archives/',
        enableEncryption: false,
        encryptionKey: '',
        autoCleanup: true,
        cleanupAfterArchive: true
      },
      rules: {
        retentionDays: [
          { required: true, message: '请输入保留天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 3650, message: '保留天数必须在1-3650之间', trigger: 'blur' }
        ],
        obsBucket: [
          { required: true, message: '请输入OBS存储桶名称', trigger: 'blur' }
        ],
        localPath: [
          { required: true, message: '请输入本地存储路径', trigger: 'blur' }
        ],
        encryptionKey: [
          { required: true, message: '请输入加密密钥', trigger: 'blur' },
          { min: 16, message: '加密密钥长度不能少于16个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.fetchConfig()
  },
  methods: {
    checkPermission,
    fetchConfig() {
      this.loading = true
      getArchiveConfig().then(response => {
        // 将后端配置映射到表单
        const config = response.data
        this.form = {
          autoArchive: config.auto_archive,
          archiveFrequency: config.archive_frequency,
          retentionDays: config.retention_days,
          storageType: config.storage_type,
          obsBucket: config.obs_bucket,
          obsPrefix: config.obs_prefix,
          localPath: config.local_path,
          enableEncryption: config.enable_encryption,
          encryptionKey: '', // 出于安全考虑，不从后端获取加密密钥
          autoCleanup: config.auto_cleanup,
          cleanupAfterArchive: config.cleanup_after_archive
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          
          // 将表单数据转换为后端API格式
          const configData = {
            auto_archive: this.form.autoArchive,
            archive_frequency: this.form.archiveFrequency,
            retention_days: this.form.retentionDays,
            storage_type: this.form.storageType,
            obs_bucket: this.form.obsBucket,
            obs_prefix: this.form.obsPrefix,
            local_path: this.form.localPath,
            enable_encryption: this.form.enableEncryption,
            auto_cleanup: this.form.autoCleanup,
            cleanup_after_archive: this.form.cleanupAfterArchive
          }
          
          // 只有在启用加密且用户输入了密钥时才发送密钥
          if (this.form.enableEncryption && this.form.encryptionKey) {
            configData.encryption_key = this.form.encryptionKey
          }
          
          updateArchiveConfig(configData).then(() => {
            this.$message({
              type: 'success',
              message: '配置保存成功!'
            })
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    handleCleanup() {
      this.$confirm('确认清理过期的审计数据吗？此操作将不可逆', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true
        cleanupExpiredData().then(response => {
          this.$message({
            type: 'success',
            message: `清理成功，共清理 ${response.data.count || 0} 条记录`
          })
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消清理操作'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-divider {
  margin: 24px 0;
}

.form-help {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}
</style>
