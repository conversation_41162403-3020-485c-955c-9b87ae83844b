<template>
  <div class="app-container">
    <el-card v-loading="loading" class="box-card">
      <div slot="header" class="clearfix">
        <span>归档详情</span>
        <el-button v-if="archiveData && archiveData.status === 'completed'" style="float: right; padding: 3px 0" type="text" @click="handleDownload">下载归档文件</el-button>
      </div>
      
      <div v-if="archiveData" class="archive-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="归档ID">{{ archiveData.id }}</el-descriptions-item>
          <el-descriptions-item label="归档名称">{{ archiveData.archive_name }}</el-descriptions-item>
          <el-descriptions-item label="季度">{{ archiveData.quarter }}</el-descriptions-item>
          <el-descriptions-item label="记录数量">{{ archiveData.record_count }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(archiveData.file_size) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="archiveData.status === 'completed' ? 'success' : (archiveData.status === 'failed' ? 'danger' : 'warning')">
              {{ formatStatus(archiveData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ archiveData.created_at | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ archiveData.completed_at | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
          <el-descriptions-item label="归档路径" :span="2">{{ archiveData.file_path }}</el-descriptions-item>
          <el-descriptions-item v-if="archiveData.error_message" label="错误信息" :span="2">
            <div class="error-message">{{ archiveData.error_message }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-if="archiveData && archiveData.metadata" class="archive-metadata">
        <h3>归档元数据</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间范围">{{ archiveData.metadata.date_range }}</el-descriptions-item>
          <el-descriptions-item label="用户操作数">{{ archiveData.metadata.user_operations }}</el-descriptions-item>
          <el-descriptions-item label="系统操作数">{{ archiveData.metadata.system_operations }}</el-descriptions-item>
          <el-descriptions-item label="资源操作数">{{ archiveData.metadata.resource_operations }}</el-descriptions-item>
          <el-descriptions-item label="加密方式">{{ archiveData.metadata.encryption || '无' }}</el-descriptions-item>
          <el-descriptions-item label="压缩方式">{{ archiveData.metadata.compression }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div v-if="!archiveData" class="empty-data">
        <el-empty description="未找到归档数据"></el-empty>
      </div>

      <div class="action-buttons">
        <el-button @click="goBack">返回</el-button>
        <el-button v-if="checkPermission(['audit:archive:delete']) && archiveData" type="danger" @click="handleDelete">删除归档</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getAuditArchive, downloadArchiveFile, deleteAuditArchive } from '@/api/audit'
import checkPermission from '@/utils/permission'

export default {
  name: 'AuditArchiveDetail',
  data() {
    return {
      loading: true,
      archiveData: null,
      archiveId: null
    }
  },
  created() {
    this.archiveId = this.$route.params.id
    this.fetchArchiveDetail()
  },
  methods: {
    checkPermission,
    fetchArchiveDetail() {
      this.loading = true
      getAuditArchive(this.archiveId).then(response => {
        this.archiveData = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    formatStatus(status) {
      const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    },
    formatFileSize(size) {
      if (!size) return '0 B'
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }
      
      return size.toFixed(2) + ' ' + units[i]
    },
    handleDownload() {
      this.$message({
        message: '正在准备下载，请稍候...',
        type: 'info'
      })
      
      downloadArchiveFile(this.archiveId).then(response => {
        const blob = new Blob([response], { type: 'application/octet-stream' })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = this.archiveData.archive_name
        link.click()
        window.URL.revokeObjectURL(link.href)
      }).catch(error => {
        console.error('下载失败', error)
        this.$message({
          message: '下载失败，请重试',
          type: 'error'
        })
      })
    },
    handleDelete() {
      this.$confirm('确认删除该归档记录吗？此操作将不可逆', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAuditArchive(this.archiveId).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.goBack()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    goBack() {
      this.$router.push('/kubeops/audit/archives')
    }
  }
}
</script>

<style lang="scss" scoped>
.archive-info {
  margin-bottom: 20px;
}

.archive-metadata {
  margin-top: 30px;
  margin-bottom: 20px;
  
  h3 {
    margin-bottom: 15px;
    font-weight: 500;
  }
}

.error-message {
  color: #F56C6C;
  white-space: pre-wrap;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.empty-data {
  padding: 40px 0;
}
</style>
