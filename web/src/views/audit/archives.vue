<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button v-if="checkPermission(['audit:archive:create'])" class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreateArchive">
        创建归档
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column label="ID" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="归档名称" min-width="150px">
        <template slot-scope="scope">
          <router-link :to="'/kubeops/audit/archives/'+scope.row.id" class="link-type">
            <span>{{ scope.row.archive_name }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="季度" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.quarter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="记录数量" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.record_count }}</span>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" width="120" align="center">
        <template slot-scope="scope">
          <span>{{ formatFileSize(scope.row.file_size) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'completed' ? 'success' : (scope.row.status === 'failed' ? 'danger' : 'warning')">
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.created_at | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status === 'completed'" size="mini" type="success" @click="handleDownload(scope.row)">
            下载
          </el-button>
          <el-button v-if="checkPermission(['audit:archive:delete'])" size="mini" type="danger" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.size" @pagination="getList" />

    <!-- 创建归档确认对话框 -->
    <el-dialog title="创建归档" :visible.sync="dialogCreateVisible">
      <div class="dialog-content">
        <p>确定要创建上一季度的审计日志归档吗？</p>
        <p class="warning">此操作将异步执行，可能需要一些时间完成。</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogCreateVisible = false">取消</el-button>
        <el-button type="primary" :loading="createLoading" @click="createArchive">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchAuditArchives, createArchive, downloadArchiveFile, deleteAuditArchive } from '@/api/audit'
import Pagination from '@/components/Pagination'
import checkPermission from '@/utils/permission'

export default {
  name: 'AuditArchives',
  components: { Pagination },
  data() {
    return {
      list: null,
      total: 0,
      listLoading: true,
      dialogCreateVisible: false,
      createLoading: false,
      listQuery: {
        page: 1,
        size: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    checkPermission,
    getList() {
      this.listLoading = true
      fetchAuditArchives(this.listQuery).then(response => {
        this.list = response.data.data
        this.total = response.data.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    formatStatus(status) {
      const statusMap = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || status
    },
    formatFileSize(size) {
      if (!size) return '0 B'
      
      const units = ['B', 'KB', 'MB', 'GB', 'TB']
      let i = 0
      while (size >= 1024 && i < units.length - 1) {
        size /= 1024
        i++
      }
      
      return size.toFixed(2) + ' ' + units[i]
    },
    handleCreateArchive() {
      this.dialogCreateVisible = true
    },
    createArchive() {
      this.createLoading = true
      createArchive().then(response => {
        this.$message({
          type: 'success',
          message: '归档任务已启动，请稍后刷新查看结果'
        })
        this.dialogCreateVisible = false
        this.createLoading = false
        this.getList()
      }).catch(() => {
        this.createLoading = false
      })
    },
    handleDownload(row) {
      this.$message({
        message: '正在准备下载，请稍候...',
        type: 'info'
      })
      
      downloadArchiveFile(row.id).then(response => {
        const blob = new Blob([response], { type: 'text/csv' })
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = row.archive_name
        link.click()
        window.URL.revokeObjectURL(link.href)
      }).catch(error => {
        console.error('下载失败', error)
        this.$message({
          message: '下载失败，请重试',
          type: 'error'
        })
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该归档记录吗？此操作将不可逆', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAuditArchive(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
}
.dialog-content {
  margin: 20px 0;
  
  .warning {
    color: #E6A23C;
    margin-top: 10px;
  }
}
</style> 