<template>
  <div class="app-container">
    <div class="editor-container">
      <div class="editor-header">
        <div class="editor-title">
          <span v-if="isEdit">编辑 {{ resourceType }}: {{ resourceName }}</span>
          <span v-else>创建 {{ resourceType }}</span>
        </div>
        <div class="editor-actions">
          <el-button type="success" size="small" @click="saveResource" :loading="saving">
            保存
          </el-button>
          <el-button size="small" @click="goBack">
            取消
          </el-button>
          <el-button v-if="isEdit" type="primary" size="small" @click="showDiff = !showDiff">
            {{ showDiff ? '隐藏差异' : '显示差异' }}
          </el-button>
          <el-button v-if="isEdit" type="warning" size="small" @click="resetResource">
            重置
          </el-button>
          <el-button size="small" type="info" @click="formatYaml">
            格式化
          </el-button>
        </div>
      </div>

      <div v-loading="loading" class="editor-main">
        <div v-if="showDiff && isEdit" class="yaml-diff">
          <div class="diff-header">原始 YAML</div>
          <pre class="diff-content">{{ originalYaml }}</pre>
          <div class="diff-header">修改后 YAML</div>
          <pre class="diff-content">{{ yamlContent }}</pre>
        </div>
        <div v-else class="yaml-editor">
          <codemirror
            ref="cmEditor"
            v-model="yamlContent"
            :options="cmOptions"
            @input="onEditorChange"
          />
        </div>
      </div>

      <div class="editor-footer">
        <div class="resource-info">
          <el-tag>集群: {{ clusterName }}</el-tag>
          <el-tag type="success">命名空间: {{ namespace }}</el-tag>
        </div>
        <div class="validation-info">
          <div v-if="validationErrors.length > 0" class="validation-error">
            <div class="error-title">验证错误:</div>
            <ul>
              <li v-for="(error, index) in validationErrors" :key="index">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>
      </div>

      <el-dialog
        title="验证错误"
        :visible.sync="showValidationDialog"
        width="60%"
        center
      >
        <div class="validation-dialog-content">
          <p>YAML 格式存在错误，请修复后再保存：</p>
          <ul>
            <li v-for="(error, index) in validationErrors" :key="index">
              {{ error }}
            </li>
          </ul>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="showValidationDialog = false">确定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getResource, createResource, updateResource } from '@/api/resources'
import { getClusterDetail } from '@/api/clusters'
import { codemirror } from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/monokai.css'
import 'codemirror/mode/yaml/yaml.js'
import 'codemirror/addon/edit/matchbrackets.js'
import 'codemirror/addon/fold/foldcode.js'
import 'codemirror/addon/fold/foldgutter.js'
import 'codemirror/addon/fold/brace-fold.js'
import 'codemirror/addon/fold/indent-fold.js'
import 'codemirror/addon/fold/comment-fold.js'
import 'codemirror/addon/fold/foldgutter.css'
import jsYaml from 'js-yaml'

export default {
  name: 'YamlEditor',
  components: {
    codemirror
  },
  data() {
    return {
      loading: false,
      saving: false,
      yamlContent: '',
      originalYaml: '',
      cmOptions: {
        tabSize: 2,
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        line: true,
        matchBrackets: true,
        lineWrapping: true,
        foldGutter: true,
        gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter'],
        extraKeys: { 'Ctrl-Space': 'autocomplete' }
      },
      cluster: '',
      clusterName: '',
      namespace: '',
      resourceType: '',
      resourceName: '',
      validationErrors: [],
      showValidationDialog: false,
      showDiff: false,
      // 资源类型模板
      resourceTemplates: {
        deployments: `apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-deployment
  namespace: default
  labels:
    app: my-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
      - name: nginx
        image: nginx:1.21
        ports:
        - containerPort: 80
`,
        statefulsets: `apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: my-statefulset
  namespace: default
spec:
  serviceName: "my-service"
  replicas: 3
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
      - name: nginx
        image: nginx:1.21
        ports:
        - containerPort: 80
        volumeMounts:
        - name: data
          mountPath: /usr/share/nginx/html
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 1Gi
`,
        daemonsets: `apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: my-daemonset
  namespace: default
spec:
  selector:
    matchLabels:
      app: my-app
  template:
    metadata:
      labels:
        app: my-app
    spec:
      containers:
      - name: agent
        image: datadog/agent:latest
`
      }
    }
  },
  computed: {
    isEdit() {
      return !!this.resourceName
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.cluster = this.$route.params.cluster
      this.namespace = this.$route.params.namespace
      this.resourceType = this.$route.params.resource
      this.resourceName = this.$route.params.name

      // 获取集群详情以显示集群名称
      this.getClusterInfo()

      if (this.isEdit) {
        // 编辑现有资源
        this.fetchResourceDetail()
      } else {
        // 创建新资源，使用模板
        if (this.resourceTemplates[this.resourceType]) {
          this.yamlContent = this.resourceTemplates[this.resourceType]
          // 替换命名空间
          this.yamlContent = this.yamlContent.replace(/namespace: default/g, `namespace: ${this.namespace}`)
        } else {
          this.yamlContent = `apiVersion: v1
kind: ${this.getKindFromType(this.resourceType)}
metadata:
  name: my-resource
  namespace: ${this.namespace}
spec:
  # 请在此处填写资源规格
`
        }
        this.originalYaml = this.yamlContent
      }
    },
    getClusterInfo() {
      getClusterDetail(this.cluster)
        .then(response => {
          this.clusterName = response.data.name
        })
        .catch(error => {
          console.error('获取集群信息失败:', error)
        })
    },
    fetchResourceDetail() {
      this.loading = true
      getResource({
        cluster: this.cluster,
        namespace: this.namespace,
        type: this.resourceType,
        name: this.resourceName
      })
        .then(response => {
          this.yamlContent = response.data.yaml
          this.originalYaml = response.data.yaml
        })
        .catch(error => {
          console.error('获取资源详情失败:', error)
          this.$notify({
            title: '错误',
            message: '获取资源详情失败',
            type: 'error',
            duration: 2000
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    onEditorChange() {
      this.validateYaml()
    },
    validateYaml() {
      this.validationErrors = []
      try {
        jsYaml.load(this.yamlContent)
      } catch (e) {
        this.validationErrors.push(`行 ${e.mark.line + 1}, 列 ${e.mark.column + 1}: ${e.message}`)
      }
    },
    formatYaml() {
      try {
        const obj = jsYaml.load(this.yamlContent)
        this.yamlContent = jsYaml.dump(obj, {
          indent: 2,
          lineWidth: 120,
          noRefs: true
        })
      } catch (e) {
        this.$message.error('YAML 格式错误，无法格式化')
      }
    },
    saveResource() {
      if (this.validationErrors.length > 0) {
        this.showValidationDialog = true
        return
      }

      this.saving = true
      const yamlObj = jsYaml.load(this.yamlContent)
      
      // 确保命名空间正确
      if (yamlObj.metadata) {
        yamlObj.metadata.namespace = this.namespace
      }
      
      // 更新或创建资源
      const savePromise = this.isEdit
        ? updateResource({
          cluster: this.cluster,
          namespace: this.namespace,
          type: this.resourceType,
          name: this.resourceName,
          yaml: this.yamlContent
        })
        : createResource({
          cluster: this.cluster,
          namespace: this.namespace,
          type: this.resourceType,
          yaml: this.yamlContent
        })

      savePromise
        .then(() => {
          this.$notify({
            title: '成功',
            message: `${this.isEdit ? '更新' : '创建'}资源成功`,
            type: 'success',
            duration: 2000
          })
          this.goBack()
        })
        .catch(error => {
          console.error(`${this.isEdit ? '更新' : '创建'}资源失败:`, error)
          this.$notify({
            title: '错误',
            message: error.response?.data?.message || `${this.isEdit ? '更新' : '创建'}资源失败`,
            type: 'error',
            duration: 5000
          })
        })
        .finally(() => {
          this.saving = false
        })
    },
    resetResource() {
      this.$confirm('确定要重置编辑器内容？这将丢失所有未保存的更改。', '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.yamlContent = this.originalYaml
        this.validateYaml()
      })
    },
    getKindFromType(type) {
      const typeToKind = {
        'deployments': 'Deployment',
        'statefulsets': 'StatefulSet',
        'daemonsets': 'DaemonSet',
        'pods': 'Pod',
        'services': 'Service',
        'ingresses': 'Ingress',
        'configmaps': 'ConfigMap',
        'secrets': 'Secret',
        'jobs': 'Job',
        'cronjobs': 'CronJob',
        'persistentvolumeclaims': 'PersistentVolumeClaim'
      }
      return typeToKind[type] || type.charAt(0).toUpperCase() + type.slice(1, -1)
    },
    goBack() {
      // 如果来自编辑器，返回到资源列表页
      this.$router.push(`/resources/${this.resourceType.toLowerCase()}`)
    }
  }
}
</script>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
}

.editor-title {
  font-size: 18px;
  font-weight: bold;
}

.editor-actions {
  display: flex;
  gap: 10px;
}

.editor-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.yaml-editor {
  height: 100%;
}

.yaml-diff {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.diff-header {
  padding: 10px;
  background-color: #f5f7fa;
  font-weight: bold;
  border-bottom: 1px solid #dcdfe6;
}

.diff-content {
  padding: 10px;
  margin: 0;
  overflow: auto;
  flex: 1;
  background-color: #f5f7fa;
  white-space: pre-wrap;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
}

.editor-footer {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.resource-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.validation-info {
  flex: 1;
}

.validation-error {
  color: #f56c6c;
}

.error-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.validation-dialog-content {
  max-height: 300px;
  overflow-y: auto;
}

.validation-dialog-content ul {
  padding-left: 20px;
}

/* 覆盖 CodeMirror 样式 */
.yaml-editor >>> .CodeMirror {
  height: 100%;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
}

.yaml-editor >>> .CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
}
</style> 