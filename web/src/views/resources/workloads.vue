<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select v-model="listQuery.cluster" placeholder="选择集群" clearable style="width: 180px" class="filter-item">
        <el-option v-for="item in clusterOptions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-model="listQuery.namespace" placeholder="命名空间" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in namespaceOptions" :key="item" :label="item" :value="item" />
      </el-select>
      <el-select v-model="listQuery.type" placeholder="资源类型" clearable style="width: 150px" class="filter-item">
        <el-option v-for="item in resourceTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-input
        v-model="listQuery.name"
        placeholder="资源名称"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreateResource"
      >
        创建
      </el-button>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane label="Deployments" name="deployments">
        <el-table
          :key="'deployments'"
          v-loading="listLoading"
          :data="resourceList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="名称" min-width="150px" align="center">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleDetail(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="命名空间" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.namespace }}</span>
            </template>
          </el-table-column>
          <el-table-column label="副本" width="100px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status === 'Ready' ? 'success' : 'warning'">
                {{ row.availableReplicas }}/{{ row.replicas }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="镜像" min-width="200px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.image }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.createdAt | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button size="mini" @click="handleScale(row)">
                扩缩容
              </el-button>
              <el-button size="mini" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="mini" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="StatefulSets" name="statefulsets">
        <el-table
          :key="'statefulsets'"
          v-loading="listLoading"
          :data="resourceList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="名称" min-width="150px" align="center">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleDetail(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="命名空间" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.namespace }}</span>
            </template>
          </el-table-column>
          <el-table-column label="副本" width="100px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status === 'Ready' ? 'success' : 'warning'">
                {{ row.availableReplicas }}/{{ row.replicas }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="镜像" min-width="200px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.image }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.createdAt | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button size="mini" @click="handleScale(row)">
                扩缩容
              </el-button>
              <el-button size="mini" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="mini" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="DaemonSets" name="daemonsets">
        <el-table
          :key="'daemonsets'"
          v-loading="listLoading"
          :data="resourceList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="名称" min-width="150px" align="center">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleDetail(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="命名空间" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.namespace }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status === 'Ready' ? 'success' : 'warning'">
                {{ row.numberReady }}/{{ row.desiredNumber }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="镜像" min-width="200px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.image }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.createdAt | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button size="mini" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button size="mini" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="Pods" name="pods">
        <el-table
          :key="'pods'"
          v-loading="listLoading"
          :data="resourceList"
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="名称" min-width="200px" align="center">
            <template slot-scope="{row}">
              <span class="link-type" @click="handleDetail(row)">{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="命名空间" width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.namespace }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="120px" align="center">
            <template slot-scope="{row}">
              <el-tag :type="row.status | statusFilter">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="节点" min-width="150px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.node }}</span>
            </template>
          </el-table-column>
          <el-table-column label="IP" min-width="120px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.ip }}</span>
            </template>
          </el-table-column>
          <el-table-column label="重启次数" width="100px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.restarts }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="180px" align="center">
            <template slot-scope="{row}">
              <span>{{ row.createdAt | parseTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="280" class-name="small-padding fixed-width">
            <template slot-scope="{row}">
              <el-button size="mini" @click="handleLogs(row)">
                日志
              </el-button>
              <el-button size="mini" @click="handleExec(row)">
                终端
              </el-button>
              <el-button size="mini" type="danger" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { fetchResourceList, deleteResource } from '@/api/resources'
import { fetchClusterList } from '@/api/clusters'
import { fetchNamespaces } from '@/api/namespaces'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'Workloads',
  components: { Pagination },
  directives: { waves },
  filters: {
    statusFilter(status) {
      const statusMap = {
        'Running': 'success',
        'Ready': 'success',
        'Pending': 'warning',
        'Failed': 'danger',
        'Unknown': 'info',
        'Error': 'danger',
        'Completed': 'info',
        'Succeeded': 'success',
        'CrashLoopBackOff': 'danger'
      }
      return statusMap[status] || 'info'
    }
  },
  data() {
    return {
      activeTab: 'deployments',
      tableKey: 0,
      resourceList: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        cluster: '',
        namespace: '',
        type: 'deployments',
        name: undefined,
        sort: '+name'
      },
      clusterOptions: [],
      namespaceOptions: [],
      resourceTypeOptions: [
        { label: 'Deployments', value: 'deployments' },
        { label: 'StatefulSets', value: 'statefulsets' },
        { label: 'DaemonSets', value: 'daemonsets' },
        { label: 'Pods', value: 'pods' },
        { label: 'Jobs', value: 'jobs' },
        { label: 'CronJobs', value: 'cronjobs' }
      ]
    }
  },
  created() {
    this.getClusterList()
  },
  methods: {
    getClusterList() {
      fetchClusterList({ limit: 100 })
        .then(response => {
          this.clusterOptions = response.data.items
          if (this.clusterOptions.length > 0) {
            this.listQuery.cluster = this.clusterOptions[0].id
            this.getNamespaces()
          }
        })
        .catch(error => {
          console.error('获取集群列表失败:', error)
        })
    },
    getNamespaces() {
      if (!this.listQuery.cluster) return

      fetchNamespaces(this.listQuery.cluster)
        .then(response => {
          this.namespaceOptions = response.data.items
          if (this.namespaceOptions.length > 0) {
            this.listQuery.namespace = 'default'
            this.getList()
          }
        })
        .catch(error => {
          console.error('获取命名空间列表失败:', error)
        })
    },
    getList() {
      if (!this.listQuery.cluster || !this.listQuery.namespace) return
      
      this.listLoading = true
      this.listQuery.type = this.activeTab
      
      fetchResourceList(this.listQuery)
        .then(response => {
          this.resourceList = response.data.items
          this.total = response.data.total
        })
        .catch(error => {
          console.error('获取资源列表失败:', error)
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleTabChange(tab) {
      this.activeTab = tab.name
      this.getList()
    },
    handleCreateResource() {
      if (!this.listQuery.cluster || !this.listQuery.namespace) {
        this.$message({
          message: '请选择集群和命名空间',
          type: 'warning'
        })
        return
      }
      
      this.$router.push({
        path: `/resources/yaml-editor/${this.listQuery.cluster}/${this.listQuery.namespace}/${this.activeTab}`
      })
    },
    handleDetail(row) {
      this.$router.push({
        path: `/resources/yaml-editor/${this.listQuery.cluster}/${row.namespace}/${this.activeTab}/${row.name}`
      })
    },
    handleScale(row) {
      // 打开扩缩容对话框
      this.$prompt(`请输入 ${row.name} 的副本数:`, '扩缩容', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.replicas,
        inputValidator: value => {
          if (!value) return '请输入副本数'
          if (!/^\d+$/.test(value)) return '请输入有效的数字'
          if (parseInt(value) < 0) return '副本数必须大于等于0'
          return true
        }
      }).then(({ value }) => {
        console.log(`扩缩容 ${row.name} 到 ${value} 副本`)
        // 实现扩缩容调用
      }).catch(() => {
        // 用户取消操作
      })
    },
    handleEdit(row) {
      this.$router.push({
        path: `/resources/yaml-editor/${this.listQuery.cluster}/${row.namespace}/${this.activeTab}/${row.name}`
      })
    },
    handleDelete(row) {
      this.$confirm(`确认删除 ${row.name}?`, '警告', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteResource({
          cluster: this.listQuery.cluster,
          namespace: row.namespace,
          type: this.activeTab,
          name: row.name
        }).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    },
    handleLogs(row) {
      // 跳转到日志查看页面
      console.log(`查看 ${row.name} 日志`)
    },
    handleExec(row) {
      // 跳转到终端页面
      console.log(`打开 ${row.name} 终端`)
    }
  },
  watch: {
    'listQuery.cluster'() {
      this.getNamespaces()
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}

.filter-item {
  margin-right: 10px;
}

.link-type {
  color: #337ab7;
  cursor: pointer;
}
</style> 