<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="on" label-position="left">

      <div class="title-container">
        <h3 class="title">系统登录</h3>
      </div>

      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="username"
          v-model="loginForm.username"
          placeholder="用户名"
          name="username"
          type="text"
          tabindex="1"
          autocomplete="on"
        />
      </el-form-item>

      <el-tooltip v-model="capsTooltip" content="大写锁定已开启" placement="right" manual>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="on"
            @keyup.native="checkCapslock"
            @blur="capsTooltip = false"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
      </el-tooltip>

      <el-button :loading="loading" type="primary" style="width:100%;margin-bottom:15px;" @click.native.prevent="handleLogin">登录</el-button>
      
      <!-- OIDC登录按钮 -->
      <el-button :loading="oidcLoading" type="success" style="width:100%;margin-bottom:30px;" @click.native.prevent="handleOIDCLogin">
        <svg-icon icon-class="link" style="margin-right: 5px;" />
        OIDC单点登录
      </el-button>

      <div style="position:relative">
        <div class="tips">
          <span>用户名: admin</span>
          <span>密码: admin123</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import { getOIDCLoginURL, handleOIDCCallback } from '@/api/user'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6个字符'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: 'admin',
        password: 'admin123'
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      oidcLoading: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
          
          // 处理OIDC回调 - 从URL参数中获取token
          if (query.token) {
            console.log('检测到URL中的token参数，处理OIDC登录')
            this.handleOIDCToken(query.token)
          }
          // 兼容旧的回调处理方式
          else if (query.code) {
            console.log('检测到OIDC回调code参数，处理OIDC回调')
            this.handleOIDCCallback(query.code)
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          console.log('表单验证通过，准备调用登录API:', this.loginForm)
          this.loading = true
          this.$store.dispatch('user/login', this.loginForm)
            .then(() => {
              console.log('登录API调用成功，准备跳转')
              this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              this.loading = false
            })
            .catch((error) => {
              console.log('登录API调用失败:', error)
              this.loading = false
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // OIDC登录处理
    handleOIDCLogin() {
      this.oidcLoading = true
      getOIDCLoginURL()
        .then(response => {
          console.log('获取OIDC认证URL响应:', response)
          
          // 检查响应格式，提取auth_url
          let authUrl = null
          
          // 处理标准响应格式 {code: 20000, message: "成功", data: {auth_url: "..."}}
          if (response.data && response.data.code === 20000 && response.data.data && response.data.data.auth_url) {
            authUrl = response.data.data.auth_url
            console.log('从标准响应格式中提取auth_url:', authUrl)
          } 
          // 处理简化响应格式 {data: {auth_url: "..."}}
          else if (response.data && response.data.data && response.data.data.auth_url) {
            authUrl = response.data.data.auth_url
            console.log('从嵌套data.data中提取auth_url:', authUrl)
          } 
          // 处理直接响应格式 {auth_url: "..."}
          else if (response.data && response.data.auth_url) {
            authUrl = response.data.auth_url
            console.log('从data中直接提取auth_url:', authUrl)
          } else {
            console.error('无法从响应中提取auth_url:', response)
            this.$message.error('获取OIDC认证URL失败')
            this.oidcLoading = false
            return
          }
          
          if (authUrl) {
            console.log('获取OIDC认证URL成功，准备跳转:', authUrl)
            window.location.href = authUrl
          } else {
            this.$message.error('获取OIDC认证URL失败')
            this.oidcLoading = false
          }
        })
        .catch(error => {
          console.error('获取OIDC认证URL失败:', error)
          this.$message.error('OIDC服务未配置或暂不可用')
          this.oidcLoading = false
        })
    },
    // 处理OIDC回调
    handleOIDCCallback(code) {
      console.log('检测到OIDC回调，code:', code)
      this.loading = true
      handleOIDCCallback(code)
        .then(response => {
          console.log('OIDC回调响应:', response)
          
          // 从响应中提取token
          let token = null
          if (response.data && response.data.data && response.data.data.token) {
            token = response.data.data.token
            console.log('从嵌套data.data中提取token:', token)
          } else if (response.data && response.data.token) {
            token = response.data.token
            console.log('从data中直接提取token:', token)
          } else {
            console.error('无法从OIDC响应中提取token:', response)
            this.$message.error('OIDC登录失败，无法获取token')
            this.loading = false
            return
          }
          
          if (token) {
            console.log('OIDC登录成功，获取到token:', token)
            // 使用专门的OIDC登录action
            this.$store.dispatch('user/handleOIDCLogin', token)
              .then(() => {
                console.log('Token已保存，准备跳转')
                this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
              })
              .catch(error => {
                console.error('保存Token失败:', error)
                this.$message.error('登录处理失败，请重试')
              })
              .finally(() => {
                this.loading = false
              })
          } else {
            this.$message.error('OIDC登录失败，未获取到有效token')
            this.loading = false
          }
        })
        .catch(error => {
          console.error('OIDC回调处理失败:', error)
          this.$message.error('OIDC登录失败，请稍后重试')
          this.loading = false
        })
    },
    // 处理URL中的OIDC token
    handleOIDCToken(token) {
      console.log('处理OIDC token:', token)
      this.loading = true
      
      // 直接使用token登录
      this.$store.dispatch('user/handleOIDCLogin', token)
        .then(() => {
          console.log('OIDC Token登录成功，准备跳转')
          this.$router.push({ path: this.redirect || '/', query: this.otherQuery })
        })
        .catch(error => {
          console.error('OIDC Token登录失败:', error)
          this.$message.error('登录处理失败，请重试')
        })
        .finally(() => {
          this.loading = false
        })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg:#283443;
$light_gray:#fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg:#2d3a4b;
$dark_gray:#889aa4;
$light_gray:#eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>