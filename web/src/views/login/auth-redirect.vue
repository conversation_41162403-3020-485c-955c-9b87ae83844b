<script>
import { handleOIDCCallback } from '@/api/user'
import { setToken } from '@/utils/auth'

export default {
  name: "AuthRedirect",
  created() {
    const token = this.$route.query.token
    const username = this.$route.query.username
    const code = this.$route.query.code
    
    if (token) {
      // 保存token到localStorage
      setToken(token)
      console.log('从URL参数获取并保存token:', token)
      
      // 保存用户名
      if (username) {
        localStorage.setItem("username", username)
      }
      
      // 跳转到首页
      this.$router.push("/")
    } else if (code) {
      // 处理OIDC回调
      console.log('AuthRedirect: 检测到OIDC回调，code:', code)
      this.handleOIDCCallback(code)
    } else {
      // 没有token也没有code，跳转到登录页
      console.warn('未找到token或code参数，跳转到登录页')
      this.$router.push("/login")
    }
  },
  methods: {
    // 处理OIDC回调
    handleOIDCCallback(code) {
      handleOIDCCallback(code)
        .then(response => {
          console.log('OIDC回调响应:', response)
          
          // 从响应中提取token
          let token = null
          
          // 处理标准响应格式 {code: 20000, message: "成功", data: {token: "..."}}
          if (response.data && response.data.code === 20000 && response.data.data && response.data.data.token) {
            token = response.data.data.token
            console.log('从标准响应格式中提取token:', token)
          }
          // 处理简化响应格式 {data: {token: "..."}}
          else if (response.data && response.data.data && response.data.data.token) {
            token = response.data.data.token
            console.log('从嵌套data.data中提取token:', token)
          } 
          // 处理直接响应格式 {token: "..."}
          else if (response.data && response.data.token) {
            token = response.data.token
            console.log('从data中直接提取token:', token)
          } else {
            console.error('无法从OIDC响应中提取token:', response)
            this.$message.error('OIDC登录失败，无法获取token')
            this.$router.push('/login')
            return
          }
          
          if (token) {
            console.log('OIDC登录成功，获取到token:', token)
            // 使用专门的OIDC登录action
            this.$store.dispatch('user/handleOIDCLogin', token)
              .then(() => {
                console.log('Token已保存，准备跳转')
                // 延迟跳转，确保token已保存
                setTimeout(() => {
                  this.$router.push('/')
                }, 300)
              })
              .catch(error => {
                console.error('保存Token失败:', error)
                this.$message.error('登录处理失败，请重试')
                this.$router.push('/login')
              })
          } else {
            console.error('OIDC登录失败，未获取到有效token')
            this.$message.error('OIDC登录失败，未获取到有效token')
            this.$router.push('/login')
          }
        })
        .catch(error => {
          console.error('OIDC回调处理失败:', error)
          this.$message.error('OIDC登录失败，请稍后重试')
          this.$router.push('/login')
        })
    }
  },
  render: function(h) {
    return h('div', {}, [
      h('p', {}, ['认证处理中，请稍候...'])
    ])
  }
}
</script>
