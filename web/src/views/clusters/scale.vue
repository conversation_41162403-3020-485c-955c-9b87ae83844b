<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>集群扩缩容</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回集群详情</el-button>
      </div>
      <div v-loading="clusterLoading">
        <div v-if="cluster.id" class="cluster-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">集群名称</div>
                <div class="info-value">{{ cluster.name }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">当前节点数</div>
                <div class="info-value">{{ cluster.nodes }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <div class="info-label">环境</div>
                <div class="info-value">{{ cluster.environment }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="form-container">
          <el-form-item label="节点数调整" prop="nodes">
            <el-input-number
              v-model="form.nodes"
              :min="cluster.minNodes || 1"
              :max="cluster.maxNodes || 50"
              :step="1"
            />
            <span class="node-tip">当前节点数: {{ cluster.nodes }}</span>
          </el-form-item>
          <el-form-item label="节点标签" prop="labels">
            <el-tag
              :key="tag"
              v-for="tag in dynamicTags"
              closable
              :disable-transitions="false"
              @close="handleClose(tag)"
            >
              {{ tag }}
            </el-tag>
            <el-input
              class="input-new-tag"
              v-if="inputVisible"
              v-model="inputValue"
              ref="saveTagInput"
              size="small"
              @keyup.enter.native="handleInputConfirm"
              @blur="handleInputConfirm"
            />
            <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 添加标签</el-button>
          </el-form-item>
          <el-form-item label="扩容原因" prop="reason">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入扩容或缩容的原因"
              v-model="form.reason"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm" :loading="submitting">提交</el-button>
            <el-button @click="goBack">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getClusterDetail, scaleCluster } from '@/api/clusters'

export default {
  name: 'ClusterScale',
  data() {
    return {
      clusterId: null,
      cluster: {},
      clusterLoading: false,
      form: {
        nodes: 3,
        reason: '',
        labels: {}
      },
      rules: {
        nodes: [
          { required: true, message: '请设置节点数', trigger: 'blur' },
          { type: 'number', message: '节点数必须为数字' }
        ],
        reason: [
          { required: true, message: '请输入扩容或缩容原因', trigger: 'blur' },
          { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      },
      submitting: false,
      dynamicTags: [],
      inputVisible: false,
      inputValue: ''
    }
  },
  created() {
    this.clusterId = this.$route.params.id
    this.getClusterData()
  },
  methods: {
    getClusterData() {
      this.clusterLoading = true
      getClusterDetail(this.clusterId)
        .then(response => {
          this.cluster = response.data
          this.form.nodes = this.cluster.nodes
          
          // 解析现有标签
          if (this.cluster.labels) {
            for (const key in this.cluster.labels) {
              this.dynamicTags.push(`${key}=${this.cluster.labels[key]}`)
            }
          }
        })
        .catch(error => {
          console.error('获取集群信息失败:', error)
          this.$notify({
            title: '错误',
            message: '获取集群信息失败',
            type: 'error',
            duration: 2000
          })
        })
        .finally(() => {
          this.clusterLoading = false
        })
    },
    handleClose(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const inputValue = this.inputValue
      if (inputValue && this.dynamicTags.indexOf(inputValue) === -1) {
        this.dynamicTags.push(inputValue)
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    processLabels() {
      const labels = {}
      this.dynamicTags.forEach(tag => {
        if (tag.includes('=')) {
          const [key, value] = tag.split('=')
          labels[key] = value
        }
      })
      return labels
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.nodes === this.cluster.nodes) {
            this.$message({
              message: '节点数未发生变化，请调整节点数',
              type: 'warning'
            })
            return
          }
          
          // 确认操作
          const action = this.form.nodes > this.cluster.nodes ? '扩容' : '缩容'
          this.$confirm(`确定要${action}集群吗？这将会修改集群节点数量。`, '警告', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.submitting = true
            
            // 处理标签
            this.form.labels = this.processLabels()
            
            // 提交请求
            scaleCluster(this.clusterId, this.form)
              .then(response => {
                this.$notify({
                  title: '成功',
                  message: `集群${action}操作已提交`,
                  type: 'success',
                  duration: 2000
                })
                this.$router.push(`/clusters/detail/${this.clusterId}`)
              })
              .catch(error => {
                console.error(`集群${action}失败:`, error)
              })
              .finally(() => {
                this.submitting = false
              })
          }).catch(() => {
            // 用户取消操作
          })
        } else {
          console.log('表单校验失败')
          return false
        }
      })
    },
    goBack() {
      this.$router.push(`/clusters/detail/${this.clusterId}`)
    }
  }
}
</script>

<style scoped>
.cluster-info {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  font-size: 14px;
  color: #909399;
}

.info-value {
  font-size: 16px;
  margin-top: 5px;
}

.form-container {
  max-width: 600px;
  margin-top: 20px;
}

.node-tip {
  margin-left: 15px;
  color: #909399;
  font-size: 13px;
}

.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style> 