<template>
  <div class="app-container">
    <div v-if="clusterLoading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else>
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>集群信息</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回列表</el-button>
        </div>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">集群名称</div>
              <div class="info-value">{{ cluster.name }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">Kubernetes版本</div>
              <div class="info-value">{{ cluster.version }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">环境</div>
              <div class="info-value">{{ cluster.environment }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">状态</div>
              <div class="info-value">
                <el-tag :type="cluster.status | statusFilter">{{ cluster.status }}</el-tag>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">节点数量</div>
              <div class="info-value">{{ cluster.nodes }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">节点规格</div>
              <div class="info-value">{{ cluster.instanceType }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">网络CIDR</div>
              <div class="info-value">{{ cluster.cidr }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <div class="info-label">创建时间</div>
              <div class="info-value">{{ cluster.createdAt | parseTime }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 20px;" v-if="cluster.description">
          <el-col :span="24">
            <div class="info-item">
              <div class="info-label">描述</div>
              <div class="info-value">{{ cluster.description }}</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <el-tabs v-model="activeTab" class="detail-tabs">
        <el-tab-pane label="节点管理" name="nodes">
          <el-table
            v-loading="nodeLoading"
            :data="nodeList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="节点名称" prop="name" align="center">
              <template slot-scope="{row}">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="IP地址" width="180px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.ip }}</span>
              </template>
            </el-table-column>
            <el-table-column label="角色" width="150px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.roles }}</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="120px" align="center">
              <template slot-scope="{row}">
                <el-tag :type="row.status | statusFilter">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="CPU" width="120px" align="center">
              <template slot-scope="{row}">
                <el-progress :percentage="row.cpuUsage" :color="getUsageColor(row.cpuUsage)"></el-progress>
              </template>
            </el-table-column>
            <el-table-column label="内存" width="120px" align="center">
              <template slot-scope="{row}">
                <el-progress :percentage="row.memoryUsage" :color="getUsageColor(row.memoryUsage)"></el-progress>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
              <template slot-scope="{row}">
                <el-button size="mini" @click="handleNodeDetail(row)">
                  详情
                </el-button>
                <el-button size="mini" type="danger" @click="handleNodeDrain(row)" :disabled="row.status !== 'Ready'">
                  维护
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="资源概览" name="resources">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>计算资源</span>
                </div>
                <div class="resource-overview">
                  <div class="resource-item">
                    <div class="resource-label">CPU</div>
                    <el-progress :percentage="resourceUsage.cpu" :format="format" :color="getUsageColor(resourceUsage.cpu)"></el-progress>
                    <div class="resource-detail">{{ resourceUsage.cpuUsed }} / {{ resourceUsage.cpuTotal }}</div>
                  </div>
                  <div class="resource-item">
                    <div class="resource-label">内存</div>
                    <el-progress :percentage="resourceUsage.memory" :format="format" :color="getUsageColor(resourceUsage.memory)"></el-progress>
                    <div class="resource-detail">{{ resourceUsage.memoryUsed }} / {{ resourceUsage.memoryTotal }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>工作负载</span>
                </div>
                <div class="workload-overview">
                  <el-row>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.pods }}</div>
                        <div class="workload-label">Pods</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.deployments }}</div>
                        <div class="workload-label">Deployments</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.statefulSets }}</div>
                        <div class="workload-label">StatefulSets</div>
                      </div>
                    </el-col>
                  </el-row>
                  <el-row style="margin-top: 20px;">
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.daemonSets }}</div>
                        <div class="workload-label">DaemonSets</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.jobs }}</div>
                        <div class="workload-label">Jobs</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ workloadSummary.cronJobs }}</div>
                        <div class="workload-label">CronJobs</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="box-card">
                <div slot="header" class="clearfix">
                  <span>网络资源</span>
                </div>
                <div class="workload-overview">
                  <el-row>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ networkSummary.services }}</div>
                        <div class="workload-label">Services</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ networkSummary.ingresses }}</div>
                        <div class="workload-label">Ingresses</div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="workload-item">
                        <div class="workload-number">{{ networkSummary.networkPolicies }}</div>
                        <div class="workload-label">Network Policies</div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="事件" name="events">
          <el-table
            v-loading="eventLoading"
            :data="eventList"
            border
            fit
            highlight-current-row
            style="width: 100%;"
          >
            <el-table-column label="时间" width="180px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.lastTimestamp | parseTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" width="100px" align="center">
              <template slot-scope="{row}">
                <el-tag :type="row.type | eventTypeFilter">
                  {{ row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="资源类型" width="150px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.involvedObject.kind }}</span>
              </template>
            </el-table-column>
            <el-table-column label="资源名称" width="200px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.involvedObject.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="命名空间" width="120px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.involvedObject.namespace }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原因" width="120px" align="center">
              <template slot-scope="{row}">
                <span>{{ row.reason }}</span>
              </template>
            </el-table-column>
            <el-table-column label="消息" align="center">
              <template slot-scope="{row}">
                <span>{{ row.message }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getClusterDetail } from '@/api/clusters'
import { getClusterNodes, drainNode } from '@/api/nodes'
import { getClusterEvents } from '@/api/events'
import { parseTime } from '@/utils'

export default {
  name: 'ClusterDetail',
  filters: {
    statusFilter(status) {
      const statusMap = {
        'Running': 'success',
        'Ready': 'success',
        'Pending': 'warning',
        'Failed': 'danger',
        'Unknown': 'info',
        'NotReady': 'danger'
      }
      return statusMap[status]
    },
    eventTypeFilter(type) {
      const typeMap = {
        'Normal': 'success',
        'Warning': 'warning'
      }
      return typeMap[type]
    }
  },
  data() {
    return {
      activeTab: 'nodes',
      clusterId: null,
      cluster: {},
      clusterLoading: true,
      nodeList: [],
      nodeLoading: false,
      eventList: [],
      eventLoading: false,
      resourceUsage: {
        cpu: 45,
        memory: 38,
        cpuUsed: '4.5 cores',
        cpuTotal: '10 cores',
        memoryUsed: '7.6 GiB',
        memoryTotal: '20 GiB'
      },
      workloadSummary: {
        pods: 28,
        deployments: 10,
        statefulSets: 2,
        daemonSets: 6,
        jobs: 5,
        cronJobs: 3
      },
      networkSummary: {
        services: 15,
        ingresses: 8,
        networkPolicies: 5
      }
    }
  },
  created() {
    this.clusterId = this.$route.params.id
    this.getClusterData()
  },
  methods: {
    format(percentage) {
      return `${percentage}%`
    },
    getUsageColor(percentage) {
      if (percentage < 60) {
        return '#67C23A'
      } else if (percentage < 85) {
        return '#E6A23C'
      } else {
        return '#F56C6C'
      }
    },
    getClusterData() {
      this.clusterLoading = true
      getClusterDetail(this.clusterId)
        .then(response => {
          this.cluster = response.data
          this.getNodes()
        })
        .catch(error => {
          console.error('获取集群信息失败:', error)
          this.$notify({
            title: '错误',
            message: '获取集群信息失败',
            type: 'error',
            duration: 2000
          })
        })
        .finally(() => {
          this.clusterLoading = false
        })
    },
    getNodes() {
      this.nodeLoading = true
      getClusterNodes(this.clusterId)
        .then(response => {
          this.nodeList = response.data.items
        })
        .catch(error => {
          console.error('获取节点列表失败:', error)
        })
        .finally(() => {
          this.nodeLoading = false
        })
    },
    getEvents() {
      this.eventLoading = true
      getClusterEvents(this.clusterId)
        .then(response => {
          this.eventList = response.data.items
        })
        .catch(error => {
          console.error('获取事件列表失败:', error)
        })
        .finally(() => {
          this.eventLoading = false
        })
    },
    handleNodeDetail(node) {
      console.log('查看节点详情:', node)
      // 可以在此处实现节点详情查看功能
    },
    handleNodeDrain(node) {
      this.$confirm(`确定要将节点 ${node.name} 设置为维护模式吗？这将驱逐节点上的所有Pod。`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        drainNode(this.clusterId, node.name)
          .then(() => {
            this.$notify({
              title: '成功',
              message: `节点 ${node.name} 已进入维护模式`,
              type: 'success',
              duration: 2000
            })
            this.getNodes()
          })
          .catch(error => {
            console.error('节点维护操作失败:', error)
          })
      })
    },
    goBack() {
      this.$router.push('/clusters/list')
    }
  },
  watch: {
    activeTab(val) {
      if (val === 'events') {
        this.getEvents()
      }
    }
  }
}
</script>

<style scoped>
.loading-container {
  padding: 40px;
}

.info-item {
  margin-bottom: 10px;
}

.info-label {
  font-size: 14px;
  color: #909399;
}

.info-value {
  font-size: 16px;
  margin-top: 5px;
}

.detail-tabs {
  margin-top: 20px;
}

.resource-overview {
  padding: 10px 0;
}

.resource-item {
  margin-bottom: 20px;
}

.resource-label {
  font-size: 14px;
  margin-bottom: 5px;
}

.resource-detail {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: right;
}

.workload-overview {
  padding: 10px 0;
}

.workload-item {
  text-align: center;
}

.workload-number {
  font-size: 24px;
  color: #409EFF;
  font-weight: bold;
}

.workload-label {
  font-size: 12px;
  color: #909399;
}
</style> 