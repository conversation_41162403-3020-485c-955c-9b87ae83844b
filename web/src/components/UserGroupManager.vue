<template>
  <div class="user-group-manager">
    <div class="header">
      <h2>用户组管理</h2>
      <el-button 
        v-permission="'ui:user-group:create'"
        type="primary" 
        @click="showCreateDialog = true"
      >
        创建用户组
      </el-button>
    </div>

    <div class="content">
      <!-- 用户组列表 -->
      <el-table 
        v-loading="loading"
        :data="userGroups" 
        style="width: 100%"
      >
        <el-table-column prop="name" label="用户组名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="domain" label="域" />
        <el-table-column prop="memberCount" label="成员数量" />
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button 
              v-permission="'ui:user-group:update'"
              size="mini" 
              @click="editGroup(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-permission="'ui:user-group:view'"
              size="mini" 
              type="info"
              @click="viewMembers(scope.row)"
            >
              成员
            </el-button>
            <el-button 
              v-permission="'ui:user-group:delete'"
              size="mini" 
              type="danger"
              @click="deleteGroup(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-show="total > 0"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="editingGroup ? '编辑用户组' : '创建用户组'"
      :visible.sync="showCreateDialog"
      width="500px"
    >
      <el-form 
        ref="groupForm"
        :model="groupForm"
        :rules="groupRules"
        label-width="100px"
      >
        <el-form-item label="用户组名称" prop="name">
          <el-input v-model="groupForm.name" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="groupForm.description" type="textarea" />
        </el-form-item>
        <el-form-item label="域" prop="domain">
          <el-select v-model="groupForm.domain" placeholder="请选择域">
            <el-option label="系统" value="system" />
            <el-option label="Kubernetes" value="kubernetes" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveGroup">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchGroupList, createGroup, updateGroup, deleteGroup } from '@/api/role'
import { UI_PERMISSIONS } from '@/config/permissions'

export default {
  name: 'UserGroupManager',
  data() {
    return {
      loading: false,
      userGroups: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      showCreateDialog: false,
      editingGroup: null,
      groupForm: {
        name: '',
        description: '',
        domain: 'system'
      },
      groupRules: {
        name: [
          { required: true, message: '请输入用户组名称', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ],
        domain: [
          { required: true, message: '请选择域', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 演示如何在计算属性中使用权限检查
    canManageGroups() {
      return this.$store.getters.permissions && 
             this.checkPermission(UI_PERMISSIONS.USER_GROUP_MANAGE)
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      try {
        const response = await fetchGroupList({
          page: this.currentPage,
          size: this.pageSize
        })
        this.userGroups = response.data.data || []
        this.total = response.data.total || 0
      } catch (error) {
        this.$message.error('获取用户组列表失败')
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    editGroup(group) {
      this.editingGroup = group
      this.groupForm = { ...group }
      this.showCreateDialog = true
    },
    
    async saveGroup() {
      try {
        await this.$refs.groupForm.validate()
        
        if (this.editingGroup) {
          await updateGroup(this.editingGroup.id, this.groupForm)
          this.$message.success('用户组更新成功')
        } else {
          await createGroup(this.groupForm)
          this.$message.success('用户组创建成功')
        }
        
        this.showCreateDialog = false
        this.resetForm()
        this.fetchData()
      } catch (error) {
        this.$message.error('操作失败')
        console.error(error)
      }
    },
    
    async deleteGroup(group) {
      try {
        await this.$confirm('确定要删除这个用户组吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await deleteGroup(group.id)
        this.$message.success('用户组删除成功')
        this.fetchData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error(error)
        }
      }
    },
    
    viewMembers(group) {
      // 跳转到成员管理页面或打开成员管理对话框
      this.$router.push(`/auth/permissions/groups/${group.id}/members`)
    },
    
    resetForm() {
      this.editingGroup = null
      this.groupForm = {
        name: '',
        description: '',
        domain: 'system'
      }
    },
    
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchData()
    },
    
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchData()
    },
    
    // 权限检查方法
    checkPermission(permission) {
      return this.$store.getters.permissions && 
             this.$store.getters.permissions.some(p => {
               if (typeof p === 'string') {
                 return p === permission
               }
               if (p && p.resource && p.action) {
                 const permissionCode = `${p.resource.type}:${p.resource.code.split(':')[1]}:${p.action}`
                 return permissionCode === permission
               }
               return false
             })
    }
  }
}
</script>

<style scoped>
.user-group-manager {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.content {
  background: white;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}
</style>
