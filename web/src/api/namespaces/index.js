import request from '@/utils/request'

// 获取命名空间列表
export function fetchNamespaces(clusterId, query) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces`,
    method: 'get',
    params: query
  })
}

// 获取命名空间详情
export function getNamespace(clusterId, name) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${name}`,
    method: 'get'
  })
}

// 创建命名空间
export function createNamespace(clusterId, data) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces`,
    method: 'post',
    data
  })
}

// 更新命名空间
export function updateNamespace(clusterId, name, data) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${name}`,
    method: 'put',
    data
  })
}

// 删除命名空间
export function deleteNamespace(clusterId, name) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${name}`,
    method: 'delete'
  })
}

// 获取命名空间资源配额
export function getNamespaceQuotas(clusterId, name) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${name}/quotas`,
    method: 'get'
  })
}

// 设置命名空间资源配额
export function setNamespaceQuotas(clusterId, name, data) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${name}/quotas`,
    method: 'post',
    data
  })
} 