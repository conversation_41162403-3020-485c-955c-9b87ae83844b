import request from '@/utils/request'

export function getClusterNodes(clusterId, query) {
  return request({
    url: `/api/v1/clusters/${clusterId}/nodes`,
    method: 'get',
    params: query
  })
}

export function fetchNodeDetail(clusterId, nodeName) {
  return request({
    url: `/api/v1/clusters/${clusterId}/nodes/${nodeName}`,
    method: 'get'
  })
}

export function cordonNode(clusterId, nodeName) {
  return request({
    url: `/api/v1/clusters/${clusterId}/nodes/${nodeName}/cordon`,
    method: 'put'
  })
}

export function uncordonNode(clusterId, nodeName) {
  return request({
    url: `/api/v1/clusters/${clusterId}/nodes/${nodeName}/uncordon`,
    method: 'put'
  })
}

export function drainNode(clusterId, nodeName, data) {
  return request({
    url: `/api/v1/clusters/${clusterId}/nodes/${nodeName}/drain`,
    method: 'put',
    data
  })
} 