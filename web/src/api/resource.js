import request from '@/utils/request'

// 获取资源列表
export function fetchResourceList(query) {
  return request({
    url: '/api/v1/rbac/resources',
    method: 'get',
    params: query
  })
}

// 获取所有资源（不分页）
export function fetchAllResources(type) {
  return request({
    url: '/api/v1/rbac/resources',
    method: 'get',
    params: { type }
  })
}

// 获取资源详情
export function getResourceDetail(id) {
  return request({
    url: `/api/v1/rbac/resources/${id}`,
    method: 'get'
  })
}

// 创建资源
export function createResource(data) {
  return request({
    url: '/api/v1/rbac/resources',
    method: 'post',
    data
  })
}

// 更新资源
export function updateResource(id, data) {
  return request({
    url: `/api/v1/rbac/resources/${id}`,
    method: 'put',
    data
  })
}

// 删除资源
export function deleteResource(id) {
  return request({
    url: `/api/v1/rbac/resources/${id}`,
    method: 'delete'
  })
}

// 获取资源操作列表
export function getResourceActions(id) {
  return request({
    url: `/api/v1/rbac/resources/${id}/actions`,
    method: 'get'
  })
}

// 创建资源操作
export function createResourceAction(resourceId, data) {
  return request({
    url: `/api/v1/rbac/resources/${resourceId}/actions`,
    method: 'post',
    data
  })
}

// 更新资源操作
export function updateResourceAction(resourceId, actionId, data) {
  return request({
    url: `/api/v1/rbac/resources/${resourceId}/actions/${actionId}`,
    method: 'put',
    data
  })
}

// 删除资源操作
export function deleteResourceAction(resourceId, actionId) {
  return request({
    url: `/api/v1/rbac/resources/${resourceId}/actions/${actionId}`,
    method: 'delete'
  })
}

// 初始化系统资源
export function initSystemResources() {
  return request({
    url: '/api/v1/rbac/resources/init',
    method: 'post'
  })
} 