import request from '@/utils/request'

// 获取资源列表
export function fetchResourceList(query) {
  return request({
    url: '/api/v1/resources',
    method: 'get',
    params: query
  })
}

// 获取资源详情
export function getResource(params) {
  const { cluster, namespace, type, name } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/${type}/${name}`,
    method: 'get'
  })
}

// 创建资源
export function createResource(params) {
  const { cluster, namespace, type, yaml } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/${type}`,
    method: 'post',
    data: {
      yaml
    }
  })
}

// 更新资源
export function updateResource(params) {
  const { cluster, namespace, type, name, yaml } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/${type}/${name}`,
    method: 'put',
    data: {
      yaml
    }
  })
}

// 删除资源
export function deleteResource(params) {
  const { cluster, namespace, type, name } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/${type}/${name}`,
    method: 'delete'
  })
}

// 扩缩容Deployment
export function scaleDeployment(params) {
  const { cluster, namespace, name, replicas } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/deployments/${name}/scale`,
    method: 'post',
    data: {
      replicas
    }
  })
}

// 扩缩容StatefulSet
export function scaleStatefulSet(params) {
  const { cluster, namespace, name, replicas } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/statefulsets/${name}/scale`,
    method: 'post',
    data: {
      replicas
    }
  })
}

// 获取Pod日志
export function getPodLogs(params) {
  const { cluster, namespace, name, container, tailLines, previous, sinceSeconds } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/pods/${name}/logs`,
    method: 'get',
    params: {
      container,
      tailLines,
      previous,
      sinceSeconds
    }
  })
}

// 在Pod中执行命令
export function execInPod(params) {
  const { cluster, namespace, name, container, command } = params
  return request({
    url: `/api/v1/clusters/${cluster}/namespaces/${namespace}/pods/${name}/exec`,
    method: 'post',
    data: {
      container,
      command
    }
  })
} 