import request from '@/utils/request'

// 获取审计日志列表
export function fetchAuditLogs(query) {
  return request({
    url: '/api/v1/audit/logs',
    method: 'get',
    params: query
  })
}

// 导出审计日志
export function exportAuditLogs(data) {
  return request({
    url: '/api/v1/audit/export',
    method: 'post',
    data,
    responseType: 'blob' // 指定响应类型为blob以便下载文件
  })
}

// 获取审计归档列表
export function fetchAuditArchives(query) {
  return request({
    url: '/api/v1/audit/archives',
    method: 'get',
    params: query
  })
}

// 获取审计归档详情
export function getAuditArchive(id) {
  return request({
    url: `/api/v1/audit/archives/${id}`,
    method: 'get'
  })
}

// 删除审计归档
export function deleteAuditArchive(id) {
  return request({
    url: `/api/v1/audit/archives/${id}`,
    method: 'delete'
  })
}

// 下载归档文件
export function downloadArchiveFile(id) {
  return request({
    url: `/api/v1/audit/archives/${id}/download`,
    method: 'get',
    responseType: 'blob' // 指定响应类型为blob以便下载文件
  })
}

// 手动创建归档
export function createArchive() {
  return request({
    url: '/api/v1/audit/archives/create',
    method: 'post'
  })
}

// 获取归档配置
export function getArchiveConfig() {
  return request({
    url: '/api/v1/audit/config',
    method: 'get'
  })
}

// 更新归档配置
export function updateArchiveConfig(data) {
  return request({
    url: '/api/v1/audit/config',
    method: 'put',
    data
  })
}

// 清理过期数据
export function cleanupExpiredData() {
  return request({
    url: '/api/v1/audit/cleanup',
    method: 'post'
  })
}

// 获取审计统计数据
export function fetchAuditStatistics(params) {
  return request({
    url: '/api/v1/audit/statistics',
    method: 'get',
    params
  })
}

// 获取用户操作统计
export function getUserOperationStats(username, query) {
  return request({
    url: `/api/v1/audit/users/${username}/stats`,
    method: 'get',
    params: query
  })
}

// 获取资源操作统计
export function getResourceOperationStats(query) {
  return request({
    url: '/api/v1/audit/resources/stats',
    method: 'get',
    params: query
  })
}

// 获取集群操作统计
export function getClusterOperationStats(clusterId, query) {
  return request({
    url: `/api/v1/audit/clusters/${clusterId}/stats`,
    method: 'get',
    params: query
  })
} 