import request from '@/utils/request'

export function getClusterEvents(clusterId, query) {
  return request({
    url: `/api/v1/clusters/${clusterId}/events`,
    method: 'get',
    params: query
  })
}

export function fetchNamespaceEvents(clusterId, namespace, query) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${namespace}/events`,
    method: 'get',
    params: query
  })
}

export function fetchResourceEvents(clusterId, namespace, resourceType, resourceName, query) {
  return request({
    url: `/api/v1/clusters/${clusterId}/namespaces/${namespace}/resources/${resourceType}/${resourceName}/events`,
    method: 'get',
    params: query
  })
} 