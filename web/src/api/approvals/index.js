import request from '@/utils/request'

// 获取待审批列表
export function fetchPendingApprovals(query) {
  return request({
    url: '/api/v1/approvals/pending',
    method: 'get',
    params: query
  })
}

// 获取审批历史
export function fetchApprovalHistory(query) {
  return request({
    url: '/api/v1/approvals/history',
    method: 'get',
    params: query
  })
}

// 获取审批详情
export function getApprovalDetail(id) {
  return request({
    url: `/api/v1/approvals/${id}`,
    method: 'get'
  })
}

// 审批通过
export function approveRequest(id, comment) {
  return request({
    url: `/api/v1/approvals/${id}/approve`,
    method: 'post',
    data: { comment }
  })
}

// 审批拒绝
export function rejectRequest(id, comment) {
  return request({
    url: `/api/v1/approvals/${id}/reject`,
    method: 'post',
    data: { comment }
  })
}

// 取消审批
export function cancelApproval(id, reason) {
  return request({
    url: `/api/v1/approvals/${id}/cancel`,
    method: 'post',
    data: { reason }
  })
}

// 创建审批
export function createApproval(data) {
  return request({
    url: '/api/v1/approvals',
    method: 'post',
    data
  })
}

// 添加审批评论
export function addApprovalComment(id, comment) {
  return request({
    url: `/api/v1/approvals/${id}/comments`,
    method: 'post',
    data: { comment }
  })
}

// 获取我的审批
export function fetchMyApprovals(query) {
  return request({
    url: '/api/v1/approvals/my',
    method: 'get',
    params: query
  })
} 