import request from '@/utils/request'

// 获取集群列表
export function fetchClusterList(query) {
  return request({
    url: '/api/v1/clusters',
    method: 'get',
    params: query
  })
}

// 获取集群详情
export function getClusterDetail(id) {
  return request({
    url: `/api/v1/clusters/${id}`,
    method: 'get'
  })
}

// 创建集群
export function createCluster(data) {
  return request({
    url: '/api/v1/clusters',
    method: 'post',
    data
  })
}

// 更新集群
export function updateCluster(id, data) {
  return request({
    url: `/api/v1/clusters/${id}`,
    method: 'put',
    data
  })
}

// 删除集群
export function deleteCluster(id) {
  return request({
    url: `/api/v1/clusters/${id}`,
    method: 'delete'
  })
}

// 扩缩容集群
export function scaleCluster(id, data) {
  return request({
    url: `/api/v1/clusters/${id}/scale`,
    method: 'post',
    data
  })
}

// 获取集群节点
export function fetchClusterNodes(id, query) {
  return request({
    url: `/api/v1/clusters/${id}/nodes`,
    method: 'get',
    params: query
  })
}

// 获取集群资源使用情况
export function getClusterResourceUsage(id) {
  return request({
    url: `/api/v1/clusters/${id}/resources/usage`,
    method: 'get'
  })
}

// 获取集群健康状态
export function getClusterHealth(id) {
  return request({
    url: `/api/v1/clusters/${id}/health`,
    method: 'get'
  })
}

// 获取集群事件
export function getClusterEvents(id, query) {
  return request({
    url: `/api/v1/clusters/${id}/events`,
    method: 'get',
    params: query
  })
} 