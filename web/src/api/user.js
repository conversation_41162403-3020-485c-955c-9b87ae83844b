import request from '@/utils/request'

// 登录
export function login(data) {
  return request({
    url: '/api/v1/auth/login',
    method: 'post',
    data
  })
}

// 获取OIDC登录URL
export function getOIDCLoginURL() {
  return request({
    url: '/api/v1/auth/oidc/login',
    method: 'get'
  })
}

// 处理OIDC回调
export function handleOIDCCallback(code) {
  return request({
    url: '/api/v1/auth/oidc/callback',
    method: 'get',
    params: { code }
  })
}

// 获取用户列表
export function fetchUserList(query) {
  return request({
    url: '/api/v1/users',
    method: 'get',
    params: query
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/api/v1/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/api/v1/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/v1/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/v1/users/${id}`,
    method: 'delete'
  })
}

// 修改用户状态
export function changeUserStatus(id, data) {
  return request({
    url: `/api/v1/users/${id}/status`,
    method: 'put',
    data
  })
}

// 修改用户密码
export function changeUserPassword(id, data) {
  return request({
    url: `/api/v1/users/${id}/password`,
    method: 'put',
    data
  })
}

// 获取用户所属用户组
export function getUserGroups(id) {
  return request({
    url: `/api/v1/users/${id}/groups`,
    method: 'get'
  })
}

// 更新用户所属用户组
export function updateUserGroups(id, data) {
  return request({
    url: `/api/v1/users/${id}/groups`,
    method: 'put',
    data
  })
}

// 获取当前用户信息
export function getInfo() {
  return request({
    url: '/api/v1/auth/me',
    method: 'get'
  })
}

// 获取用户权限列表
export function getUserPermissions(id) {
  return request({
    url: `/api/v1/rbac/users/${id}/resource-permissions`,
    method: 'get'
  })
}

// 获取用户所属用户组（新增）
export function getUserGroupsForUser(id) {
  return request({
    url: `/api/v1/rbac/users/${id}/groups`,
    method: 'get'
  })
}

// 同步飞书用户
export function syncFeishuUsers() {
  return request({
    url: '/api/v1/users/sync/feishu',
    method: 'post'
  })
}
