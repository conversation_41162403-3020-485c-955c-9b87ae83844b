import request from '@/utils/request'

// 创建用户K8s权限
export function createUserK8sPermission(data) {
  return request({
    url: '/api/v1/rbac/k8s/permissions',
    method: 'post',
    data
  })
}

// 删除用户K8s权限
export function deleteUserK8sPermission(username, clusterName) {
  return request({
    url: `/api/v1/rbac/k8s/permissions/${username}`,
    method: 'delete',
    params: { cluster_name: clusterName }
  })
} 