import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'

NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login', '/auth-redirect'] // no redirect whitelist

// 添加变量记录上次获取用户信息的时间
let lastFetchTime = 0
const FETCH_INTERVAL = 5 * 60 * 1000 // 5分钟内不重复获取用户信息

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = getToken()
  console.log('路由:', from.path, '->', to.path, '是否有Token:', !!hasToken)

  if (hasToken) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      console.log('已登录，重定向到首页')
      next({ path: '/' })
      NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      console.log('是否已获取角色:', hasRoles, store.getters.roles)

      const now = Date.now()
      // 如果已有角色信息，则不重新获取
      if (hasRoles) {
        console.log('已有角色，允许访问:', to.path)
        next()
      } else {
        try {
          console.log('获取用户信息')
          // 更新获取时间
          lastFetchTime = now

          // get user info
          // note: roles must be a object array! such as: ['admin'] or ,['developer','editor']
          const { roles } = await store.dispatch('user/getInfo')
          console.log('获取到角色:', roles)

          // load user permissions
          try {
            await store.dispatch('permission/loadPermissions')
            console.log('用户权限加载完成')
          } catch (permError) {
            console.warn('加载用户权限失败:', permError)
            // 权限加载失败不阻止路由生成，使用默认权限
          }

          // generate accessible routes map based on roles
          const accessRoutes = await store.dispatch('permission/generateRoutes', roles)
          console.log('生成可访问路由:', accessRoutes.length)

          // dynamically add accessible routes
          router.addRoutes(accessRoutes)
          console.log('动态添加路由完成')

          // hack method to ensure that addRoutes is complete
          // set the replace: true, so the navigation will not leave a history record
          next({ ...to, replace: true })
        } catch (error) {
          // remove token and go to login page to re-login
          console.error('获取用户信息失败:', error)
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      console.log('无Token，但在白名单中，允许访问:', to.path)
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      console.log('无Token，重定向到登录页')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
