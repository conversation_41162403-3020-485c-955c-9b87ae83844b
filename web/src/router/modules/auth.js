import Layout from '@/layout'

const authRouter = {
  path: '/auth',
  component: Layout,
  redirect: '/auth/users',
  alwaysShow: true,
  name: 'Authentication',
  meta: { title: '身份认证', icon: 'lock', roles: ['admin', 'user-admin'] },
  children: [
    {
      path: 'users',
      component: () => import('@/views/system/users'),
      name: 'UserManagement',
      meta: {
        title: '用户管理',
        icon: 'user',
        permission: 'ui:user:view',
        roles: ['admin', 'user-admin']
      }
    },
    {
      path: 'permissions',
      component: { render: (h) => h('router-view') },
      redirect: '/auth/permissions/resources',
      alwaysShow: true,
      name: 'PermissionManagement',
      meta: {
        title: '权限管理',
        icon: 'key',
        permission: 'ui:resource-permission:view',
        roles: ['admin']
      },
      children: [
        {
          path: 'resources',
          component: () => import('@/views/system/resource-permissions'),
          name: 'ResourcePermissions',
          props: { defaultTab: 'list', hideAssignment: true },
          meta: {
            title: '权限资源',
            icon: 'list',
            permission: 'ui:resource-permission:view',
            roles: ['admin']
          }
        },
        {
          path: 'groups',
          component: () => import('@/views/system/roles'),
          name: 'UserGroupManagement',
          meta: {
            title: '用户组管理',
            icon: 'peoples',
            permission: 'ui:user-group:view',
            roles: ['admin', 'user-admin']
          }
        },
        {
          path: 'authorizations',
          component: () => import('@/views/system/resource-permissions'),
          name: 'AuthorizationManagement',
          props: { defaultTab: 'authorized', onlyAuthorized: true },
          meta: {
            title: '授权管理',
            icon: 'connection',
            permission: 'ui:user-group:view',
            roles: ['admin']
          }
        },
        {
          path: 'check',
          component: () => import('@/views/system/permission-check'),
          name: 'PermissionCheck',
          meta: {
            title: '权限检查',
            icon: 'search',
            permission: 'ui:resource-permission:view',
            roles: ['admin']
          }
        }
      ]
    }
  ]
}

export default authRouter
