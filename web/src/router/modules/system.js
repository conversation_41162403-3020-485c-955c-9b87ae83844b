import Layout from '@/layout'

const systemRouter = {
  path: '/system',
  component: Layout,
  redirect: '/system/settings',
  name: 'SystemManagement',
  meta: {
    title: '系统管理',
    icon: 'setting',
    roles: ['admin', 'system-admin']
  },
  children: [
    {
      path: 'audit-config',
      component: () => import('@/views/audit/logs'),
      name: 'AuditConfig',
      meta: {
        title: '审计配置',
        permission: 'ui:audit-log:view',
        roles: ['admin', 'system-admin']
      }
    },
    {
      path: 'settings',
      component: () => import('@/views/system/settings'),
      name: 'SystemSettings',
      meta: {
        title: '系统配置',
        permission: 'ui:system-config:view',
        roles: ['admin', 'system-admin']
      }
    }
  ]
}

export default systemRouter
