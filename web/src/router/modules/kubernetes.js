import Layout from '@/layout'

const kubernetesRouter = {
  path: '/kubernetes',
  component: Layout,
  redirect: '/kubernetes/clusters',
  name: 'Kubernetes',
  meta: {
    title: 'Kubernetes平台管理',
    icon: 'component',
    permission: 'ui:cluster:view',
    roles: ['admin', 'k8s-admin', 'k8s-user']
  },
  children: [
    {
      path: 'clusters',
      component: () => import('@/views/clusters/list'),
      name: 'ClusterManagement',
      meta: {
        title: '集群管理',
        icon: 'cloud',
        permission: 'ui:cluster:view',
        roles: ['admin', 'k8s-admin', 'k8s-user']
      }
    },
    {
      path: 'clusters/create',
      component: () => import('@/views/clusters/create'),
      name: 'ClusterCreate',
      meta: { title: '创建集群', noCache: true },
      hidden: true
    },
    {
      path: 'clusters/:id',
      component: () => import('@/views/clusters/detail'),
      name: 'ClusterDetail',
      meta: { title: '集群详情', noCache: true },
      hidden: true
    },
    {
      path: 'clusters/:id/scale',
      component: () => import('@/views/clusters/scale'),
      name: 'ClusterScale',
      meta: { title: '集群扩缩容', noCache: true },
      hidden: true
    },
    {
      path: 'workloads',
      component: () => import('@/views/resources/workloads'),
      name: 'Workloads',
      meta: { title: '工作负载', icon: 'list' }
    },
    {
      path: 'services',
      component: () => import('@/views/resources/workloads'),
      name: 'Services',
      meta: { title: '服务与路由', icon: 'link' }
    },
    {
      path: 'storage',
      component: () => import('@/views/resources/workloads'),
      name: 'Storage',
      meta: { title: '存储', icon: 'database' }
    },
    {
      path: 'configs',
      component: () => import('@/views/resources/workloads'),
      name: 'Configs',
      meta: { title: '配置与密钥', icon: 'key' }
    },
    {
      path: 'yaml-editor/:cluster/:namespace/:resource/:name?',
      component: () => import('@/views/resources/yaml-editor'),
      name: 'YamlEditor',
      meta: { title: 'YAML编辑器', noCache: true },
      hidden: true
    }
  ]
}

export default kubernetesRouter
