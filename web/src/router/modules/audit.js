import Layout from '@/layout'

const auditRouter = {
  path: '/audit',
  component: Layout,
  redirect: '/audit/logs',
  name: 'AuditLogs',
  meta: {
    title: '审计管理',
    icon: 'documentation',
    permission: 'ui:audit-log:view',
    roles: ['admin', 'auditor']
  },
  children: [
    {
      path: 'logs',
      component: () => import('@/views/audit/logs'),
      name: 'AuditLogList',
      meta: {
        title: '操作日志',
        permission: 'ui:audit-log:view',
        roles: ['admin', 'auditor']
      }
    },
    {
      path: 'archives',
      component: () => import('@/views/audit/archives'),
      name: 'AuditArchives',
      meta: {
        title: '归档管理',
        permission: 'ui:audit-log:view',
        roles: ['admin', 'auditor']
      }
    },
    {
      path: 'archives/:id',
      component: () => import('@/views/audit/archive-detail'),
      name: 'AuditArchiveDetail',
      meta: {
        title: '归档详情',
        noCache: true,
        permission: 'ui:audit-log:view',
        roles: ['admin', 'auditor']
      },
      hidden: true
    },
    {
      path: 'reports',
      component: () => import('@/views/audit/reports'),
      name: 'AuditReports',
      meta: {
        title: '统计报表',
        permission: 'ui:audit-log:view',
        roles: ['admin', 'auditor']
      }
    },
    {
      path: 'config',
      component: () => import('@/views/audit/config'),
      name: 'AuditConfig',
      meta: {
        title: '审计配置',
        permission: 'ui:audit-log:manage',
        roles: ['admin']
      }
    }
  ]
}

export default auditRouter
