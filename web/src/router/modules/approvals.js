import Layout from '@/layout'

const approvalsRouter = {
  path: '/approvals',
  component: Layout,
  redirect: '/approvals/my-requests',
  name: 'Approvals',
  meta: { title: '审批中心', icon: 'form' },
  children: [
    {
      path: 'my-requests',
      component: () => import('@/views/approvals/history'),
      name: 'MyRequests',
      meta: { title: '我的申请' }
    },
    {
      path: 'pending',
      component: () => import('@/views/approvals/pending'),
      name: 'PendingApprovals',
      meta: { title: '我的审批' }
    },
    {
      path: 'templates',
      component: () => import('@/views/approvals/history'),
      name: 'ApprovalTemplates',
      meta: { title: '审批模板配置' }
    },
    {
      path: 'history',
      component: () => import('@/views/approvals/history'),
      name: 'ApprovalHistory',
      meta: { title: '审批历史' }
    },
    {
      path: 'detail/:id',
      component: () => import('@/views/approvals/detail'),
      name: 'ApprovalDetail',
      meta: { title: '审批详情', noCache: true },
      hidden: true
    }
  ]
}

export default approvalsRouter 