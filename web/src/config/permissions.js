/**
 * Frontend Permission Configuration
 * 
 * This file maps backend resource permissions to frontend UI elements
 * and provides a centralized configuration for permission management.
 */

// Permission resource types that match the backend
export const RESOURCE_TYPES = {
  UI: 'ui',
  API: 'api',
  K8S: 'k8s'
}

// Permission actions that match the backend
export const ACTIONS = {
  VIEW: 'view',
  CREATE: 'create',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage',
  LIST: 'list',
  GET: 'get'
}

// UI Resource permissions mapping
export const UI_PERMISSIONS = {
  // User management
  USER_VIEW: 'ui:user:view',
  USER_CREATE: 'ui:user:create',
  USER_UPDATE: 'ui:user:update',
  USER_DELETE: 'ui:user:delete',
  USER_MANAGE: 'ui:user:manage',

  // User group management
  USER_GROUP_VIEW: 'ui:user-group:view',
  USER_GROUP_CREATE: 'ui:user-group:create',
  USER_GROUP_UPDATE: 'ui:user-group:update',
  USER_GROUP_DELETE: 'ui:user-group:delete',
  USER_GROUP_MANAGE: 'ui:user-group:manage',

  // Resource permission management
  RESOURCE_PERMISSION_VIEW: 'ui:resource-permission:view',
  RESOURCE_PERMISSION_CREATE: 'ui:resource-permission:create',
  RESOURCE_PERMISSION_UPDATE: 'ui:resource-permission:update',
  RESOURCE_PERMISSION_DELETE: 'ui:resource-permission:delete',
  RESOURCE_PERMISSION_MANAGE: 'ui:resource-permission:manage',

  // Cluster management
  CLUSTER_VIEW: 'ui:cluster:view',
  CLUSTER_CREATE: 'ui:cluster:create',
  CLUSTER_UPDATE: 'ui:cluster:update',
  CLUSTER_DELETE: 'ui:cluster:delete',
  CLUSTER_MANAGE: 'ui:cluster:manage',

  // Audit log management
  AUDIT_LOG_VIEW: 'ui:audit-log:view',
  AUDIT_LOG_MANAGE: 'ui:audit-log:manage',

  // System configuration
  SYSTEM_CONFIG_VIEW: 'ui:system-config:view',
  SYSTEM_CONFIG_UPDATE: 'ui:system-config:update',
  SYSTEM_CONFIG_MANAGE: 'ui:system-config:manage'
}

// API Resource permissions mapping
export const API_PERMISSIONS = {
  // User API
  USER_READ: 'api:user:read',
  USER_WRITE: 'api:user:write',
  USER_DELETE: 'api:user:delete',

  // User group API
  USER_GROUP_READ: 'api:user-group:read',
  USER_GROUP_WRITE: 'api:user-group:write',
  USER_GROUP_DELETE: 'api:user-group:delete',

  // Cluster API
  CLUSTER_READ: 'api:cluster:read',
  CLUSTER_WRITE: 'api:cluster:write',
  CLUSTER_DELETE: 'api:cluster:delete',

  // Audit API
  AUDIT_READ: 'api:audit-log:read',
  AUDIT_WRITE: 'api:audit-log:write'
}

// K8s Resource permissions mapping
export const K8S_PERMISSIONS = {
  // Pod management
  PODS_LIST: 'k8s:pods:list',
  PODS_GET: 'k8s:pods:get',
  PODS_CREATE: 'k8s:pods:create',
  PODS_UPDATE: 'k8s:pods:update',
  PODS_DELETE: 'k8s:pods:delete',

  // Service management
  SERVICES_LIST: 'k8s:services:list',
  SERVICES_GET: 'k8s:services:get',
  SERVICES_CREATE: 'k8s:services:create',
  SERVICES_UPDATE: 'k8s:services:update',
  SERVICES_DELETE: 'k8s:services:delete',

  // Deployment management
  DEPLOYMENTS_LIST: 'k8s:deployments:list',
  DEPLOYMENTS_GET: 'k8s:deployments:get',
  DEPLOYMENTS_CREATE: 'k8s:deployments:create',
  DEPLOYMENTS_UPDATE: 'k8s:deployments:update',
  DEPLOYMENTS_DELETE: 'k8s:deployments:delete'
}

// User group names that match the backend
export const USER_GROUPS = {
  ADMIN: 'admin',
  USER_ADMIN: 'user-admin',
  SYSTEM_ADMIN: 'system-admin',
  K8S_ADMIN: 'k8s-admin',
  K8S_USER: 'k8s-user',
  AUDITOR: 'auditor',
  USER: 'user'
}

// Permission sets for different user groups
export const GROUP_PERMISSIONS = {
  [USER_GROUPS.ADMIN]: [
    // Admin has all permissions
    ...Object.values(UI_PERMISSIONS),
    ...Object.values(API_PERMISSIONS),
    ...Object.values(K8S_PERMISSIONS)
  ],
  
  [USER_GROUPS.USER_ADMIN]: [
    // User admin can manage users and user groups
    UI_PERMISSIONS.USER_VIEW,
    UI_PERMISSIONS.USER_CREATE,
    UI_PERMISSIONS.USER_UPDATE,
    UI_PERMISSIONS.USER_DELETE,
    UI_PERMISSIONS.USER_GROUP_VIEW,
    UI_PERMISSIONS.USER_GROUP_CREATE,
    UI_PERMISSIONS.USER_GROUP_UPDATE,
    UI_PERMISSIONS.USER_GROUP_DELETE,
    API_PERMISSIONS.USER_READ,
    API_PERMISSIONS.USER_WRITE,
    API_PERMISSIONS.USER_DELETE,
    API_PERMISSIONS.USER_GROUP_READ,
    API_PERMISSIONS.USER_GROUP_WRITE,
    API_PERMISSIONS.USER_GROUP_DELETE
  ],
  
  [USER_GROUPS.SYSTEM_ADMIN]: [
    // System admin can manage system configuration and audit
    UI_PERMISSIONS.SYSTEM_CONFIG_VIEW,
    UI_PERMISSIONS.SYSTEM_CONFIG_UPDATE,
    UI_PERMISSIONS.AUDIT_LOG_VIEW,
    UI_PERMISSIONS.AUDIT_LOG_MANAGE
  ],
  
  [USER_GROUPS.K8S_ADMIN]: [
    // K8s admin can manage clusters and all k8s resources
    UI_PERMISSIONS.CLUSTER_VIEW,
    UI_PERMISSIONS.CLUSTER_CREATE,
    UI_PERMISSIONS.CLUSTER_UPDATE,
    UI_PERMISSIONS.CLUSTER_DELETE,
    ...Object.values(K8S_PERMISSIONS),
    API_PERMISSIONS.CLUSTER_READ,
    API_PERMISSIONS.CLUSTER_WRITE,
    API_PERMISSIONS.CLUSTER_DELETE
  ],
  
  [USER_GROUPS.K8S_USER]: [
    // K8s user can view clusters and basic k8s resources
    UI_PERMISSIONS.CLUSTER_VIEW,
    K8S_PERMISSIONS.PODS_LIST,
    K8S_PERMISSIONS.PODS_GET,
    K8S_PERMISSIONS.SERVICES_LIST,
    K8S_PERMISSIONS.SERVICES_GET,
    K8S_PERMISSIONS.DEPLOYMENTS_LIST,
    K8S_PERMISSIONS.DEPLOYMENTS_GET,
    API_PERMISSIONS.CLUSTER_READ
  ],
  
  [USER_GROUPS.AUDITOR]: [
    // Auditor can view audit logs
    UI_PERMISSIONS.AUDIT_LOG_VIEW,
    API_PERMISSIONS.AUDIT_READ
  ],
  
  [USER_GROUPS.USER]: [
    // Basic user has minimal permissions
    UI_PERMISSIONS.CLUSTER_VIEW
  ]
}

// Helper functions
export function hasPermission(userPermissions, requiredPermission) {
  if (!userPermissions || !Array.isArray(userPermissions)) {
    return false
  }
  
  return userPermissions.some(permission => {
    if (typeof permission === 'string') {
      return permission === requiredPermission
    }
    
    if (permission && permission.resource && permission.action) {
      const permissionCode = `${permission.resource.type}:${permission.resource.code.split(':')[1]}:${permission.action}`
      return permissionCode === requiredPermission
    }
    
    return false
  })
}

export function hasAnyPermission(userPermissions, requiredPermissions) {
  return requiredPermissions.some(permission => hasPermission(userPermissions, permission))
}

export function hasAllPermissions(userPermissions, requiredPermissions) {
  return requiredPermissions.every(permission => hasPermission(userPermissions, permission))
}

export function getUserGroupPermissions(groupName) {
  return GROUP_PERMISSIONS[groupName] || []
}

export function checkUserGroupAccess(userGroups, requiredGroups) {
  if (!userGroups || !Array.isArray(userGroups)) {
    return false
  }
  
  if (!requiredGroups || !Array.isArray(requiredGroups)) {
    return true
  }
  
  const userGroupNames = userGroups.map(group => 
    typeof group === 'string' ? group : group.name
  )
  
  return requiredGroups.some(group => userGroupNames.includes(group))
}

export default {
  RESOURCE_TYPES,
  ACTIONS,
  UI_PERMISSIONS,
  API_PERMISSIONS,
  K8S_PERMISSIONS,
  USER_GROUPS,
  GROUP_PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getUserGroupPermissions,
  checkUserGroupAccess
}
