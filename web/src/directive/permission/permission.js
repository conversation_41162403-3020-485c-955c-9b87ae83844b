import { checkPermission, checkAllPermissions, checkAnyPermission } from '@/utils/permission'
import { checkUserGroupAccess } from '@/config/permissions'
import store from '@/store'

/**
 * 权限指令
 * 用法：
 * 1. 单个权限: v-permission="'ui:user:view'"
 * 2. 任意一个权限: v-permission="{ any: ['ui:user:view', 'api:user:read'] }"
 * 3. 所有权限: v-permission="{ all: ['ui:user:view', 'api:user:read'] }"
 * 4. 用户组检查: v-permission="{ groups: ['admin', 'user-admin'] }"
 * 5. 混合检查: v-permission="{ permissions: ['ui:user:view'], groups: ['admin'] }"
 * 6. 自定义处理: v-permission="{ handler: (el, checkFn) => { ... } }"
 */
export default {
  inserted(el, binding, vnode) {
    const { value } = binding

    if (!value) {
      throw new Error('需要指定权限值')
    }

    // 处理不同类型的权限检查
    let hasPermission = false

    if (typeof value === 'string') {
      // 单个权限
      hasPermission = checkPermission(value)
    } else if (typeof value === 'object') {
      if (value.any && Array.isArray(value.any)) {
        // 任意一个权限
        hasPermission = checkAnyPermission(value.any)
      } else if (value.all && Array.isArray(value.all)) {
        // 所有权限
        hasPermission = checkAllPermissions(value.all)
      } else if (value.groups && Array.isArray(value.groups)) {
        // 用户组检查
        const userGroups = store.getters.userGroups || []
        hasPermission = checkUserGroupAccess(userGroups, value.groups)
      } else if (value.permissions || value.groups) {
        // 混合检查：权限和用户组
        let permissionCheck = true
        let groupCheck = true

        if (value.permissions) {
          if (Array.isArray(value.permissions)) {
            permissionCheck = checkAnyPermission(value.permissions)
          } else {
            permissionCheck = checkPermission(value.permissions)
          }
        }

        if (value.groups) {
          const userGroups = store.getters.userGroups || []
          groupCheck = checkUserGroupAccess(userGroups, value.groups)
        }

        // 权限和用户组都需要满足（AND关系）
        hasPermission = permissionCheck && groupCheck
      } else if (typeof value.handler === 'function') {
        // 自定义处理函数
        try {
          value.handler(el, checkPermission)
          return // 自定义处理函数自己负责处理元素，直接返回
        } catch (error) {
          console.error('权限指令自定义处理函数错误:', error)
          hasPermission = false
        }
      } else if (Array.isArray(value)) {
        // 数组形式（任意一个权限）
        hasPermission = checkAnyPermission(value)
      } else {
        throw new Error('权限值格式错误')
      }
    } else {
      throw new Error('权限值格式错误')
    }

    if (!hasPermission) {
      // 无权限时移除元素
      const parentElement = el.parentNode
      if (parentElement) {
        el.setAttribute('disabled', 'disabled')
        el.classList.add('is-disabled')
        el.setAttribute('title', '无权限执行此操作')

        // 对于按钮，可以保留但禁用
        if (el.tagName === 'BUTTON') {
          el.disabled = true
          el.style.opacity = '0.5'
          el.style.cursor = 'not-allowed'
        } else {
          // 非按钮元素直接移除
          parentElement.removeChild(el)
        }
      }
    }
  }
}
