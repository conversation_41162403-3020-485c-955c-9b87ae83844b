# 前端权限系统使用说明

## 概述

本前端权限系统已经完全集成了后端优化的用户组权限管理系统，支持基于用户组的RBAC权限控制。

## 主要特性

- ✅ **用户组权限管理**：支持基于用户组的权限分配
- ✅ **细粒度权限控制**：支持UI、API、K8s三种资源类型的权限
- ✅ **权限指令**：提供v-permission指令进行页面元素权限控制
- ✅ **路由权限**：自动根据权限过滤可访问的路由
- ✅ **权限缓存**：提供权限检查缓存机制提升性能
- ✅ **混合权限检查**：支持权限和用户组的组合检查

## 权限类型

### 1. UI权限 (ui:resource:action)
控制前端界面元素的显示和操作
```javascript
'ui:user:view'           // 用户查看权限
'ui:user:create'         // 用户创建权限
'ui:user-group:manage'   // 用户组管理权限
'ui:cluster:view'        // 集群查看权限
```

### 2. API权限 (api:resource:action)
控制后端API的访问权限
```javascript
'api:user:read'          // 用户API读取权限
'api:user:write'         // 用户API写入权限
'api:cluster:manage'     // 集群API管理权限
```

### 3. K8s权限 (k8s:resource:action)
控制Kubernetes资源的操作权限
```javascript
'k8s:pods:list'          // Pod列表权限
'k8s:services:create'    // Service创建权限
'k8s:deployments:update' // Deployment更新权限
```

## 用户组类型

系统预定义了以下用户组：

- **admin**: 系统管理员，拥有所有权限
- **user-admin**: 用户管理员，可管理用户和用户组
- **system-admin**: 系统管理员，可管理系统配置和审计
- **k8s-admin**: Kubernetes管理员，可管理集群和所有K8s资源
- **k8s-user**: Kubernetes用户，可查看集群和基本K8s资源
- **auditor**: 审计员，可查看审计日志
- **user**: 普通用户，基本权限

## 使用方法

### 1. 权限指令使用

#### 单个权限检查
```vue
<template>
  <!-- 只有拥有用户查看权限的用户才能看到此按钮 -->
  <el-button v-permission="'ui:user:view'">查看用户</el-button>
</template>
```

#### 用户组检查
```vue
<template>
  <!-- 只有管理员才能看到此按钮 -->
  <el-button v-permission="{ groups: ['admin'] }">管理员功能</el-button>
  
  <!-- 管理员或用户管理员都可以看到 -->
  <el-button v-permission="{ groups: ['admin', 'user-admin'] }">用户管理</el-button>
</template>
```

#### 任意权限检查
```vue
<template>
  <!-- 拥有任意一个权限即可看到 -->
  <el-button v-permission="{ any: ['ui:user:create', 'ui:user:update'] }">
    用户操作
  </el-button>
</template>
```

#### 所有权限检查
```vue
<template>
  <!-- 必须拥有所有权限才能看到 -->
  <el-button v-permission="{ all: ['ui:user:view', 'ui:user:create'] }">
    高级用户操作
  </el-button>
</template>
```

#### 混合权限检查
```vue
<template>
  <!-- 必须拥有指定权限且属于指定用户组 -->
  <el-button v-permission="{ 
    permissions: ['ui:user:view'], 
    groups: ['admin', 'user-admin'] 
  }">
    管理员用户查看
  </el-button>
</template>
```

### 2. 编程式权限检查

```javascript
import checkPermission from '@/utils/permission'
import { UI_PERMISSIONS } from '@/config/permissions'

export default {
  methods: {
    handleUserAction() {
      // 检查单个权限
      if (checkPermission('ui:user:create')) {
        // 执行用户创建操作
      }
      
      // 使用配置常量
      if (checkPermission(UI_PERMISSIONS.USER_CREATE)) {
        // 执行用户创建操作
      }
      
      // 检查多个权限
      if (checkPermission(['ui:user:view', 'ui:user:create'])) {
        // 执行操作
      }
    }
  },
  
  computed: {
    canManageUsers() {
      return checkPermission('ui:user:manage')
    }
  }
}
```

### 3. 路由权限配置

```javascript
// router/modules/auth.js
const authRouter = {
  path: '/auth',
  component: Layout,
  meta: { 
    title: '身份认证', 
    icon: 'lock',
    roles: ['admin', 'user-admin']  // 用户组权限
  },
  children: [
    {
      path: 'users',
      component: () => import('@/views/system/users'),
      meta: { 
        title: '用户管理',
        permission: 'ui:user:view',  // 具体权限
        roles: ['admin', 'user-admin']
      }
    }
  ]
}
```

## 权限配置

权限配置文件位于 `src/config/permissions.js`，包含：

- 权限常量定义
- 用户组权限映射
- 权限检查工具函数

```javascript
import { UI_PERMISSIONS, USER_GROUPS } from '@/config/permissions'

// 使用权限常量
const hasUserViewPermission = checkPermission(UI_PERMISSIONS.USER_VIEW)

// 检查用户组
const isAdmin = this.$store.getters.userGroups.some(group => 
  group.name === USER_GROUPS.ADMIN
)
```

## 测试和调试

### 权限测试页面
访问 `/system/permission-test` 查看权限测试页面，可以：
- 查看当前用户信息和权限
- 测试各种权限指令
- 查看权限检查结果

### 调试信息
在浏览器控制台中可以查看：
```javascript
// 查看当前用户权限
console.log(this.$store.getters.permissions)

// 查看当前用户组
console.log(this.$store.getters.userGroups)

// 测试权限检查
import checkPermission from '@/utils/permission'
console.log(checkPermission('ui:user:view'))
```

## 与后端API集成

前端权限系统已完全集成后端API：

- **登录**: `POST /api/v1/auth/login`
- **用户信息**: `GET /api/v1/user/info`
- **用户权限**: `GET /api/v1/rbac/resources/user`
- **用户组**: `GET /api/v1/rbac/groups`
- **权限检查**: `POST /api/v1/rbac/check-permission`

## 最佳实践

1. **使用权限常量**：优先使用 `@/config/permissions.js` 中定义的常量
2. **权限缓存**：权限检查结果会自动缓存1分钟，提升性能
3. **错误处理**：权限检查失败时，元素会被禁用或隐藏
4. **用户体验**：对于重要操作，建议同时检查权限和用户组
5. **测试覆盖**：使用权限测试页面验证权限配置是否正确

## 故障排除

### 权限不生效
1. 检查用户是否已登录
2. 确认权限数据已加载到store中
3. 验证权限字符串格式是否正确
4. 检查后端API返回的权限数据格式

### 路由无法访问
1. 检查路由配置中的roles和permission设置
2. 确认用户拥有相应的用户组或权限
3. 查看浏览器控制台是否有权限相关错误

### 权限指令不工作
1. 确认已在main.js中注册权限指令
2. 检查指令语法是否正确
3. 验证权限数据是否已加载
