package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"go.uber.org/zap"

	"kubeops/internal/bootstrap"
	"kubeops/internal/config"
	"kubeops/internal/logger"
)

func main() {
	// 创建根上下文
	ctx := context.Background()

	// 加载配置
	cfg, err := loadConfig()
	if err != nil {
		// 在日志系统初始化前，使用标准错误输出
		fmt.Fprintf(os.Stderr, "加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	log := logger.InitLogger(cfg)
	defer logger.Sync()

	// 记录启动信息
	startInfo := map[string]interface{}{
		"version":    "1.0.0",
		"env":        cfg.Server.Mode,
		"port":       cfg.Server.Port,
		"config":     getConfigPath(),
		"log_format": cfg.Log.Format,
		"log_level":  cfg.Log.Level,
	}

	logger.Info("KubeOps 服务正在启动",
		zap.Any("app_info", startInfo))

	// 创建并初始化应用，传递日志实例
	app, err := bootstrap.NewApp(cfg, log)
	if err != nil {
		logger.Error("初始化应用失败", zap.Error(err))
		os.Exit(1)
	}

	// 启动应用
	go func() {
		if err := app.Start(); err != nil {
			logger.Fatal("启动服务器失败", zap.Error(err))
		}
	}()

	logger.Info("服务已启动，等待请求...",
		zap.Int("port", cfg.Server.Port),
		zap.String("mode", cfg.Server.Mode))

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("接收到退出信号，开始优雅关闭...")

	// 优雅退出
	ctxShutdown, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 停止应用
	if err := app.Stop(ctxShutdown); err != nil {
		logger.ErrorContext(ctxShutdown, "关闭应用失败", zap.Error(err))
	}

	logger.Info("服务已完全关闭，再见!")
}

// 加载配置
func loadConfig() (*config.Config, error) {
	// 配置文件路径
	configPath := getConfigPath()
	// 在日志系统初始化前，使用标准输出显示配置文件路径
	fmt.Printf("使用配置文件: %s\n", configPath)

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		return nil, err
	}

	return cfg, nil
}

// 获取配置文件路径
func getConfigPath() string {
	// 先检查命令行参数
	if len(os.Args) > 1 {
		return os.Args[1]
	}

	// 检查环境变量
	if path := os.Getenv("KUBEOPS_CONFIG_PATH"); path != "" {
		return path
	}

	// 默认配置路径
	return filepath.Join("configs", "config.yaml")
}
