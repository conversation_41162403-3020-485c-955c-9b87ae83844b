# 编译产物
/build/
/bin/
*.exe
*.exe~
*.dll
*.so
*.dylib
kubeops

# 测试文件
*.test
*.out
coverage.out
coverage.html

# 依赖目录
/vendor/

# IDE 文件
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# 日志文件
*.log
logs/

# 配置文件（可能包含敏感信息）
/configs/*.local.yaml
.env
.env.local

# SQLite数据库文件
*.db
*.db-journal

# 临时文件
/tmp/

# 生成的文件
swagger.json
swagger.yaml
/docs/

# 自动生成的文件
/web/node_modules/
/web/dist/
/web/build/
/web/.env.local
/web/.env.development.local
/web/.env.test.local
/web/.env.production.local
/web/npm-debug.log*
/web/yarn-debug.log*
/web/yarn-error.log* 