# 贡献指南

感谢您对KubeOps项目的关注！我们欢迎所有形式的贡献，包括但不限于：

- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 开发新功能

## 🚀 快速开始

### 开发环境设置

1. **Fork项目**
   ```bash
   # 在GitHub上Fork项目到您的账户
   # 然后克隆到本地
   git clone https://github.com/your-username/kubeops.git
   cd kubeops
   ```

2. **安装依赖**
   ```bash
   # 安装Go依赖
   go mod download
   
   # 安装开发工具
   make install-tools
   ```

3. **配置环境**
   ```bash
   # 复制配置文件
   cp config/config.example.yaml config/config.yaml
   
   # 启动依赖服务（PostgreSQL, Redis）
   docker-compose -f docker-compose.dev.yml up -d
   ```

4. **运行项目**
   ```bash
   # 运行数据库迁移
   make migrate
   
   # 启动开发服务器
   make dev
   ```

## 📋 贡献流程

### 1. 创建Issue

在开始开发之前，请先创建一个Issue来描述您要解决的问题或要添加的功能。

### 2. 创建分支

```bash
# 创建新分支
git checkout -b feature/your-feature-name
# 或
git checkout -b fix/your-bug-fix
```

### 3. 开发代码

- 遵循项目的代码规范
- 添加必要的测试
- 更新相关文档

### 4. 提交代码

```bash
# 添加文件
git add .

# 提交代码（遵循提交信息规范）
git commit -m "feat: add user group management API"

# 推送到远程分支
git push origin feature/your-feature-name
```

### 5. 创建Pull Request

在GitHub上创建Pull Request，详细描述您的更改。

## 📝 代码规范

### Go代码规范

1. **格式化代码**
   ```bash
   make fmt
   ```

2. **代码检查**
   ```bash
   make lint
   ```

3. **命名规范**
   - 使用驼峰命名法
   - 接口名以`er`结尾
   - 常量使用大写字母和下划线

4. **注释规范**
   ```go
   // UserService 用户服务接口
   type UserService interface {
       // CreateUser 创建用户
       CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
   }
   ```

### 提交信息规范

使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(rbac): add user group permission management

- Add user group CRUD operations
- Implement permission assignment for groups
- Add K8s RBAC synchronization

Closes #123
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 生成测试覆盖率报告
make test-coverage
```

### 编写测试

1. **单元测试**
   ```go
   func TestUserService_CreateUser(t *testing.T) {
       // 测试代码
   }
   ```

2. **集成测试**
   ```go
   func TestUserAPI_CreateUser(t *testing.T) {
       // 集成测试代码
   }
   ```

## 📚 文档

### 文档类型

1. **API文档**：使用Swagger注释
2. **用户文档**：Markdown格式
3. **开发文档**：代码注释和README

### 文档更新

当您的更改影响到用户界面或API时，请同时更新相关文档：

- API变更：更新Swagger注释
- 新功能：更新用户指南
- 配置变更：更新配置文档

## 🔍 代码审查

### 审查清单

- [ ] 代码符合项目规范
- [ ] 包含必要的测试
- [ ] 文档已更新
- [ ] 没有引入安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性

### 审查流程

1. 自动化检查通过
2. 至少一个维护者审查
3. 所有讨论已解决
4. 测试通过
5. 合并到主分支

## 🐛 报告Bug

### Bug报告模板

```markdown
**Bug描述**
简要描述遇到的问题

**重现步骤**
1. 执行操作A
2. 执行操作B
3. 观察到错误

**期望行为**
描述您期望发生的情况

**实际行为**
描述实际发生的情况

**环境信息**
- OS: [e.g. Ubuntu 20.04]
- KubeOps版本: [e.g. v1.0.0]
- Go版本: [e.g. 1.21]
- Kubernetes版本: [e.g. 1.28]

**附加信息**
- 错误日志
- 配置文件（去除敏感信息）
- 截图（如适用）
```

## 💡 功能建议

### 功能请求模板

```markdown
**功能描述**
简要描述建议的功能

**使用场景**
描述什么情况下需要这个功能

**详细设计**
详细描述功能的实现方案

**替代方案**
是否考虑过其他解决方案

**附加信息**
任何其他相关信息
```

## 🏷️ 发布流程

### 版本号规范

使用[语义化版本](https://semver.org/)：

- `MAJOR.MINOR.PATCH`
- `MAJOR`: 不兼容的API修改
- `MINOR`: 向后兼容的功能性新增
- `PATCH`: 向后兼容的问题修正

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 变更日志已更新
- [ ] 版本号已更新
- [ ] 创建Git标签
- [ ] 构建和发布二进制文件

## 🤝 社区

### 沟通渠道

- **GitHub Issues**: 报告Bug和功能请求
- **GitHub Discussions**: 一般讨论和问答
- **社区论坛**: https://community.kubeops.io
- **邮件列表**: <EMAIL>

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境。请遵循我们的[行为准则](CODE_OF_CONDUCT.md)。

## 📄 许可证

通过贡献代码，您同意您的贡献将在[MIT许可证](LICENSE)下授权。

## 🙏 致谢

感谢所有为KubeOps项目做出贡献的开发者！

---

如果您有任何问题，请随时通过GitHub Issues或邮件联系我们。
