# KubeOps 代码重构指南

本文档描述了按照 `docs/technical-design.md` 设计规范进行的代码重构工作。

## 重构概述

### 完成的重构内容

1. **统一配置管理模块** (`internal/config/`)
   - 实现了环境变量替换功能
   - 支持多环境配置（dev、test、prod）
   - 添加了配置验证器
   - 支持配置热更新机制

2. **Redis基础设施抽象层** (`internal/redis/`)
   - 实现了统一的Redis管理器接口
   - 支持单机、集群、哨兵三种部署模式
   - 提供了分布式锁功能
   - 实现了连接池管理和监控

3. **JWT认证模块优化** (`internal/auth/`)
   - 实现了Access Token和Refresh Token机制
   - 支持JWT黑名单功能
   - 实现了Token刷新策略和频率限制
   - 添加了会话管理和设备信息跟踪

4. **中间件重构** (`internal/middleware/`)
   - 更新了JWT认证中间件
   - 添加了角色和权限验证中间件
   - 支持可选认证和刷新Token中间件

## 配置文件结构

### 主配置文件 (`configs/config.yaml`)

```yaml
# 服务器配置
server:
  port: 8080
  mode: release
  read_timeout: 30s
  write_timeout: 30s

# 数据库配置
database:
  type: mysql
  host: "${DB_HOST:localhost}"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"

# Redis配置
redis:
  global:
    mode: "standalone"
    nodes: ["${REDIS_NODE1:localhost:6379}"]
    password: "${REDIS_PASSWORD}"
    key_prefix: "kubeops:v1:"

# JWT认证配置
auth:
  jwt_secret: "${JWT_SECRET}"
  jwt_expire: "24h"
  tokens:
    access_token:
      expires_in: "1h"
    refresh_token:
      expires_in: "168h"
```

### 环境特定配置

- `configs/config.dev.yaml` - 开发环境配置
- `configs/config.test.yaml` - 测试环境配置
- `configs/config.prod.yaml` - 生产环境配置

## 使用方法

### 1. 配置管理

```go
// 加载配置
cfg, err := config.LoadConfig("configs/config.yaml")
if err != nil {
    log.Fatal("Failed to load config", zap.Error(err))
}

// 获取数据库DSN
dsn := cfg.GetDatabaseDSN()

// 获取Redis节点
nodes := cfg.GetRedisNodes()
```

### 2. Redis管理器

```go
// 创建Redis管理器
redisManager, err := redis.NewRedisManager(cfg)
if err != nil {
    log.Fatal("Failed to create Redis manager", zap.Error(err))
}
defer redisManager.Close()

// 基础操作
ctx := context.Background()
err = redisManager.Set(ctx, "key", "value", time.Hour)
value, err := redisManager.Get(ctx, "key")

// 分布式锁
lock, err := redisManager.Lock(ctx, "resource", time.Minute)
if err == nil {
    defer lock.Release(ctx)
    // 执行需要锁保护的操作
}
```

### 3. JWT服务

```go
// 创建JWT服务
jwtService := auth.NewJWTService(cfg, redisManager)

// 生成Token对
userInfo := &auth.UserInfo{
    UserID:   1,
    Username: "user",
    Roles:    []string{"admin"},
}
deviceInfo := &auth.DeviceInfo{
    DeviceID:  "device-123",
    IPAddress: "127.0.0.1",
}

tokenPair, err := jwtService.GenerateTokenPair(ctx, userInfo, deviceInfo)

// 验证Token
claims, err := jwtService.VerifyAccessToken(ctx, tokenPair.AccessToken)

// 刷新Token
newTokenPair, err := jwtService.RefreshToken(ctx, tokenPair.RefreshToken)

// 撤销Token
err = jwtService.RevokeToken(ctx, tokenPair.AccessToken, "user_logout")
```

### 4. 中间件使用

```go
// 创建JWT中间件
jwtMiddleware := middleware.NewJWTMiddleware(jwtService, cfg)

// 在路由中使用
router := gin.New()

// 需要认证的路由
protected := router.Group("/api/v1")
protected.Use(jwtMiddleware.AuthRequired())

// 需要特定角色的路由
admin := router.Group("/api/v1/admin")
admin.Use(jwtMiddleware.AuthRequired())
admin.Use(jwtMiddleware.RequireRoles("admin"))

// 需要特定权限的路由
sensitive := router.Group("/api/v1/sensitive")
sensitive.Use(jwtMiddleware.AuthRequired())
sensitive.Use(jwtMiddleware.RequirePermissions("read:sensitive", "write:sensitive"))
```

## 环境变量

### 必需的环境变量

```bash
# JWT密钥（至少32字符）
export JWT_SECRET="your-jwt-secret-key-at-least-32-characters"

# 数据库配置（MySQL）
export DB_HOST="localhost"
export DB_USERNAME="root"
export DB_PASSWORD="password"
export DB_NAME="kubeops"

# Redis配置
export REDIS_PASSWORD="your-redis-password"
export REDIS_NODE1="redis-node1:6379"
export REDIS_NODE2="redis-node2:6379"  # 集群模式
export REDIS_NODE3="redis-node3:6379"  # 集群模式
```

### 可选的环境变量

```bash
# 环境标识
export ENVIRONMENT="dev"  # dev, test, prod

# 遥测配置
export JAEGER_ENDPOINT="http://localhost:14268/api/traces"

# Redis哨兵配置
export REDIS_SENTINEL_PASSWORD="sentinel-password"
```

## 测试

### 运行单元测试

```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/config/
go test ./internal/redis/
go test ./internal/auth/

# 跳过集成测试
go test -short ./...
```

### 集成测试

集成测试需要实际的Redis实例：

```bash
# 启动Redis（Docker）
docker run -d --name redis-test -p 6379:6379 redis:latest

# 运行集成测试
go test ./internal/redis/ -v
```

## 迁移指南

### 从旧版本迁移

1. **更新配置文件**：
   - 将旧的配置格式迁移到新的统一配置格式
   - 添加必需的环境变量

2. **更新代码**：
   - 使用新的配置加载方式
   - 替换旧的Redis客户端为新的Redis管理器
   - 更新JWT相关代码使用新的JWT服务

3. **更新中间件**：
   - 使用新的JWT中间件
   - 更新路由配置

## 注意事项

1. **向后兼容性**：
   - 保留了旧版本的兼容性函数
   - 逐步迁移，避免破坏性变更

2. **安全性**：
   - JWT密钥必须至少32字符
   - 敏感信息使用环境变量
   - 支持Token黑名单和撤销

3. **性能**：
   - Redis连接池优化
   - 支持集群和哨兵模式
   - 异步更新用户活跃状态

4. **监控**：
   - Redis监控和统计
   - JWT黑名单统计
   - 配置验证和错误处理

## 下一步计划

1. 完善单元测试覆盖率
2. 添加更多的集成测试
3. 实现配置热更新功能
4. 优化Redis性能监控
5. 添加更多的JWT安全特性
