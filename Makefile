# KubeOps Makefile

# 变量定义
BINARY_NAME=kubeops
BUILD_DIR=build
MAIN_FILE=cmd/main/main.go
CONFIG_FILE=configs/config.yaml

# 确保目录存在
$(shell mkdir -p $(BUILD_DIR))

# 默认任务
.PHONY: all
all: build

# 构建二进制文件
.PHONY: build
build:
	@echo "Building..."
	go build -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_FILE)
	@echo "Build complete: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行服务
.PHONY: run
run:
	@echo "Running service..."
	go run $(MAIN_FILE) $(CONFIG_FILE)

# 以生产模式运行
.PHONY: run-prod
run-prod:
	@echo "Running in production mode..."
	go run $(MAIN_FILE) configs/config.prod.yaml

# 清理构建文件
.PHONY: clean
clean:
	@echo "Cleaning..."
	rm -rf $(BUILD_DIR)
	@echo "Clean complete"

# 测试
.PHONY: test
test:
	@echo "Running tests..."
	go test -v ./...

# 测试覆盖率
.PHONY: test-cover
test-cover:
	@echo "Running tests with coverage..."
	go test -v -cover ./...

# 静态代码检查
.PHONY: lint
lint:
	@echo "Running golangci-lint..."
	golangci-lint run

# 依赖管理
.PHONY: deps
deps:
	@echo "Downloading dependencies..."
	go mod download
	go mod tidy

# 构建Docker镜像
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t kubeops:latest .

# 运行Docker容器
.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -p 8080:8080 --name kubeops -d kubeops:latest

# 帮助信息
.PHONY: help
help:
	@echo "KubeOps Makefile commands:"
	@echo "  make build      - Build binary"
	@echo "  make run        - Run service with default config"
	@echo "  make run-prod   - Run service with production config"
	@echo "  make clean      - Remove build artifacts"
	@echo "  make test       - Run tests"
	@echo "  make test-cover - Run tests with coverage"
	@echo "  make lint       - Run linter"
	@echo "  make deps       - Update dependencies"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run Docker container"
	@echo "  make help       - Show this help" 