-- 创建一个临时表，不带唯一约束
CREATE TABLE users_temp (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL,
    password TEXT NOT NULL,
    email TEXT,
    name TEXT,
    avatar TEXT,
    phone TEXT,
    status INTEGER DEFAULT 1,
    last_login DATETIME,
    created_at DATETIME,
    updated_at DATETIME,
    deleted_at DATETIME,
    o_id_c_subject TEXT,
    identity_provider TEXT,
    feishu_open_id TEXT,
    feishu_union_id TEXT,
    feishu_employee_id TEXT
);

-- 复制数据
INSERT INTO users_temp SELECT * FROM users;

-- 删除原表
DROP TABLE users;

-- 重命名临时表
ALTER TABLE users_temp RENAME TO users;

-- 重新创建必要的索引，但不包括飞书字段的唯一约束
CREATE UNIQUE INDEX idx_users_username ON users(username);
CREATE UNIQUE INDEX idx_users_email ON users(email);
CREATE UNIQUE INDEX idx_users_o_id_c_subject ON users(o_id_c_subject);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- 添加注释说明
-- 此脚本移除了feishu_open_id和feishu_union_id字段上的唯一约束
-- 这样可以允许OIDC用户和飞书用户共存，避免唯一约束冲突 