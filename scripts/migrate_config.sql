-- 创建临时表存储旧的配置值
CREATE TEMP TABLE temp_configs AS 
SELECT category, key, value FROM system_configs WHERE category IS NOT NULL AND key IS NOT NULL;

-- 删除旧配置
DELETE FROM system_configs;

-- 重置自增ID
DELETE FROM sqlite_sequence WHERE name='system_configs';

-- 插入新的统一配置记录
INSERT INTO system_configs (
    id,
    created_at,
    updated_at,
    feishu_app_id,
    feishu_app_secret,
    feishu_redirect_uri,
    feishu_webhook_url,
    obs_enabled,
    obs_endpoint,
    obs_access_key,
    obs_secret_key,
    obs_bucket,
    obs_region,
    obs_encryption_key,
    audit_retention_days,
    audit_archive_interval,
    system_name,
    system_logo,
    system_contact_email,
    system_version,
    system_debug_mode
) 
VALUES (
    1, 
    datetime('now'), 
    datetime('now'),
    (SELECT value FROM temp_configs WHERE category = 'feishu' AND key = 'app_id'),
    (SELECT value FROM temp_configs WHERE category = 'feishu' AND key = 'app_secret'),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'feishu' AND key = 'redirect_uri'), 'http://localhost:8080/api/v1/auth/feishu/callback'),
    (SELECT value FROM temp_configs WHERE category = 'feishu' AND key = 'webhook_url'),
    CASE WHEN (SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'enabled') = 'true' THEN 1 ELSE 0 END,
    COALESCE((SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'endpoint'), 'https://obs.cn-north-4.myhuaweicloud.com'),
    (SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'access_key'),
    (SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'secret_key'),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'bucket'), 'kubeops-audit-logs'),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'region'), 'cn-north-4'),
    (SELECT value FROM temp_configs WHERE category = 'obs' AND key = 'encryption_key'),
    CAST(COALESCE((SELECT value FROM temp_configs WHERE category = 'audit' AND key = 'retention_days'), '90') AS INTEGER),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'audit' AND key = 'archive_interval'), '0'),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'system' AND key = 'name'), 'KubeOps'),
    (SELECT value FROM temp_configs WHERE category = 'system' AND key = 'logo'),
    (SELECT value FROM temp_configs WHERE category = 'system' AND key = 'contact_email'),
    COALESCE((SELECT value FROM temp_configs WHERE category = 'system' AND key = 'version'), '1.0.0'),
    CASE WHEN (SELECT value FROM temp_configs WHERE category = 'system' AND key = 'debug_mode') = 'true' THEN 1 ELSE 0 END
);

-- 删除临时表
DROP TABLE temp_configs; 