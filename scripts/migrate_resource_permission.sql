-- 创建新的resource_permissions表
CREATE TABLE IF NOT EXISTS resource_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    resource_code VARCHAR(100) NOT NULL,
    action_code VARCHAR(100) NOT NULL,
    full_code VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建中间表，用于角色和资源权限的多对多关系
CREATE TABLE IF NOT EXISTS role_resource_permissions (
    role_id INTEGER NOT NULL,
    resource_permission_id INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY (role_id, resource_permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (resource_permission_id) REFERENCES resource_permissions(id) ON DELETE CASCADE
);

-- 从原有表迁移数据到新表
INSERT INTO resource_permissions (name, type, resource_code, action_code, full_code, description, created_at, updated_at)
SELECT 
    r.name, 
    r.type, 
    r.code AS resource_code, 
    p.action AS action_code, 
    (r.type || ':' || r.code || ':' || p.action) AS full_code, 
    p.description, 
    p.created_at, 
    p.updated_at
FROM 
    resources r
CROSS JOIN 
    permissions p 
WHERE 
    p.object = r.code;

-- 迁移角色权限关系
INSERT INTO role_resource_permissions (role_id, resource_permission_id)
SELECT 
    rp.role_id,
    rp2.id AS resource_permission_id
FROM 
    role_permissions rp
JOIN 
    permissions p ON rp.permission_id = p.id
JOIN 
    resource_permissions rp2 ON rp2.resource_code = p.object AND rp2.action_code = p.action;

-- 创建索引
-- 这些索引已经存在，不需要再次创建
-- CREATE INDEX idx_resource_permissions_full_code ON resource_permissions(full_code);
-- CREATE INDEX idx_resource_permissions_type ON resource_permissions(type);
-- CREATE INDEX idx_resource_permissions_resource_code ON resource_permissions(resource_code);
-- CREATE INDEX idx_resource_permissions_action_code ON resource_permissions(action_code); 