#!/bin/bash
# 这个脚本用于清理旧的Permission相关代码文件

# 删除旧的Repository文件
echo "删除旧的Repository文件..."
rm -f internal/repository/permission_repository.go
rm -f internal/repository/resource_repository.go

# 删除旧的Service文件
echo "删除旧的Service文件..."
rm -f internal/service/permission_service.go
rm -f internal/service/resource_service.go

# 删除旧的Handler文件
echo "删除旧的Handler文件..."
rm -f internal/api/v1/handler/permission.go
rm -f internal/api/v1/handler/resource.go

# 删除旧的Model文件
echo "删除旧的Model文件..."
rm -f internal/model/permission.go
rm -f internal/model/resource.go

# 删除可能存在的ResourcePermission单独文件(已合并到rbac_model.go)
rm -f internal/model/resource_permission.go
rm -f internal/repository/resource_permission_repository.go
rm -f internal/service/resource_permission_service.go
rm -f internal/api/v1/handler/resource_permission.go

echo "权限相关文件清理完成" 