-- 修复casbin_rule表 - 处理p_type和ptype问题
-- 步骤1：检查表结构，确认字段存在情况
PRAGMA table_info(casbin_rule);

-- 步骤2：将p_type列的数据复制到ptype列（如果p_type列存在且有数据）
UPDATE casbin_rule SET ptype = p_type WHERE p_type IS NOT NULL AND p_type != '';

-- 步骤3：修复id=1的数据（确保是admin角色的权限策略）
-- 确认ptype=p（策略规则）
UPDATE casbin_rule SET ptype = 'p' WHERE id = 1 AND ptype IS NULL;
-- 确认v0=admin（策略主体是角色名称而非用户ID）
UPDATE casbin_rule SET v0 = 'admin' WHERE id = 1 AND ptype = 'p';

-- 步骤4：修复id=2的数据（确保是用户到角色的映射）
-- 确认ptype=g（角色映射规则）
UPDATE casbin_rule SET ptype = 'g' WHERE id = 2 AND ptype IS NULL;
-- 确保按照格式: g, 用户ID, 角色名, 域名

-- 步骤5：确认修复结果
SELECT id, ptype, p_type, v0, v1, v2, v3, v4, v5 FROM casbin_rule ORDER BY id;

-- 注意：下面的语句会删除p_type列，请确认数据已经正确迁移到ptype列后再执行
-- 由于SQLite不直接支持删除列，需要创建新表并迁移数据的方式
-- CREATE TABLE casbin_rule_new (
--     id INTEGER PRIMARY KEY AUTOINCREMENT,
--     ptype TEXT,
--     v0 TEXT,
--     v1 TEXT,
--     v2 TEXT,
--     v3 TEXT,
--     v4 TEXT,
--     v5 TEXT
-- );
-- 
-- INSERT INTO casbin_rule_new (id, ptype, v0, v1, v2, v3, v4, v5)
-- SELECT id, ptype, v0, v1, v2, v3, v4, v5 FROM casbin_rule;
-- 
-- DROP TABLE casbin_rule;
-- ALTER TABLE casbin_rule_new RENAME TO casbin_rule;
--
-- PRAGMA table_info(casbin_rule); 