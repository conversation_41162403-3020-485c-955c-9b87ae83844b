-- 修复casbin_rule表的数据问题

-- 1. 将p_type列的数据复制到ptype列（如果p_type列存在）
-- 注意：此语句仅在存在p_type列时有效，具体数据库可能需要调整语法
UPDATE casbin_rule SET ptype = p_type WHERE ptype IS NULL OR ptype = '';

-- 2. 修复id=2的记录，将v0从用户ID(1)改为角色名称(admin)
-- 这是因为按照权限模型，策略主体应该是角色名称
UPDATE casbin_rule SET v0 = 'admin' WHERE id = 2 AND ptype = 'p' AND v0 = '1';

-- 3. 检查修改结果
SELECT id, ptype, v0, v1, v2, v3, v4, v5 FROM casbin_rule ORDER BY id; 