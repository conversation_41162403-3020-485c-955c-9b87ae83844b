-- 修复Casbin RBAC模型配置中的参数引用问题
-- 清理现有策略并重新初始化

-- 1. 清理现有的Casbin策略表
DELETE FROM casbin_rule WHERE 1=1;

-- 2. 清理现有的RBAC模型配置
DELETE FROM rbac_models WHERE 1=1;

-- 3. 插入修复后的RBAC模型配置
INSERT INTO rbac_models (name, description, content, is_active, created_at, updated_at) VALUES (
    'KubeOps RBAC模型',
    '支持用户直接权限和用户组权限的双策略模型',
    '[request_definition]
r = sub, resource_type, resource_path, action

[policy_definition]
p = sub, resource_type, resource_path, action
p2 = group, resource_type, resource_path, action

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = (r.sub == p.sub && (p.resource_type == "*" || r.resource_type == p.resource_type) && resourcePathMatch(r.resource_path, p.resource_path) && (p.action == "*" || actionMatch(r.action, p.action))) || (g(r.sub, p2.group) && (p2.resource_type == "*" || r.resource_type == p2.resource_type) && resourcePathMatch(r.resource_path, p2.resource_path) && (p2.action == "*" || actionMatch(r.action, p2.action)))',
    true,
    NOW(),
    NOW()
);

-- 4. 插入admin用户组权限策略 (p2)
-- admin用户组拥有所有资源的完全权限
INSERT INTO casbin_rule (ptype, v0, v1, v2, v3) VALUES ('p2', 'admin', '*', '*', '*');

-- 5. 插入用户组成员关系 (g)
-- admin用户(ID=1)属于admin用户组
INSERT INTO casbin_rule (ptype, v0, v1) VALUES ('g', 'user:1', 'admin');

-- 6. 验证插入的策略
SELECT '=== 当前RBAC模型配置 ===' as info;
SELECT name, description, is_active FROM rbac_models WHERE is_active = true;

SELECT '=== 当前Casbin策略 ===' as info;
SELECT ptype, v0, v1, v2, v3 FROM casbin_rule ORDER BY ptype, v0;

-- 7. 验证admin用户组策略
SELECT '=== admin用户组策略验证 ===' as info;
SELECT 
    'p2策略' as policy_type,
    v0 as group_name,
    v1 as resource_type,
    v2 as resource_path,
    v3 as action
FROM casbin_rule 
WHERE ptype = 'p2' AND v0 = 'admin';

-- 8. 验证用户组成员关系
SELECT '=== 用户组成员关系验证 ===' as info;
SELECT 
    'g关系' as relation_type,
    v0 as user_id,
    v1 as group_name
FROM casbin_rule 
WHERE ptype = 'g' AND v1 = 'admin';

-- 9. 预期结果说明
SELECT '=== 修复说明 ===' as info;
SELECT '1. 修复了p2策略定义中的参数引用问题' as fix_item
UNION ALL
SELECT '2. 修复了matcher中的参数名不一致问题' as fix_item
UNION ALL
SELECT '3. 确保admin用户(user:1)属于admin用户组' as fix_item
UNION ALL
SELECT '4. 确保admin用户组拥有所有资源的完全权限' as fix_item
UNION ALL
SELECT '5. 权限检查参数: subject="user:1", resource_type="api", resource_path="system:users", action="read"' as fix_item; 