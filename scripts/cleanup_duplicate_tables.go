package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 清理重复的用户组关联表
func main() {
	// 获取数据库路径
	dbPath := "data/kubeops.db"
	if len(os.Args) > 1 {
		dbPath = os.Args[1]
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("开始清理重复的用户组关联表...")

	// 检查两个表是否都存在
	var userGroupMembersExists, userGroupsUsersExists bool

	// 检查 user_group_members 表
	err = db.Exec("SELECT 1 FROM user_group_members LIMIT 1").Error
	if err == nil {
		userGroupMembersExists = true
		fmt.Println("✅ user_group_members 表存在")
	} else {
		fmt.Println("❌ user_group_members 表不存在")
	}

	// 检查 user_groups_users 表
	err = db.Exec("SELECT 1 FROM user_groups_users LIMIT 1").Error
	if err == nil {
		userGroupsUsersExists = true
		fmt.Println("✅ user_groups_users 表存在")
	} else {
		fmt.Println("❌ user_groups_users 表不存在")
	}

	// 如果两个表都存在，需要合并数据
	if userGroupMembersExists && userGroupsUsersExists {
		fmt.Println("🔄 两个表都存在，开始数据合并...")

		// 查询 user_groups_users 表中的数据
		var userGroupsUsersData []struct {
			UserID      uint `gorm:"column:user_id"`
			UserGroupID uint `gorm:"column:user_group_id"`
		}

		err = db.Table("user_groups_users").Find(&userGroupsUsersData).Error
		if err != nil {
			log.Fatalf("查询 user_groups_users 表失败: %v", err)
		}

		fmt.Printf("📊 user_groups_users 表中有 %d 条记录\n", len(userGroupsUsersData))

		// 将 user_groups_users 中的数据迁移到 user_group_members
		for _, record := range userGroupsUsersData {
			// 检查是否已存在相同的记录
			var count int64
			err = db.Table("user_group_members").
				Where("user_id = ? AND group_id = ?", record.UserID, record.UserGroupID).
				Count(&count).Error
			if err != nil {
				log.Printf("检查重复记录失败: %v", err)
				continue
			}

			if count == 0 {
				// 插入新记录
				err = db.Exec("INSERT INTO user_group_members (user_id, group_id) VALUES (?, ?)",
					record.UserID, record.UserGroupID).Error
				if err != nil {
					log.Printf("插入记录失败: %v", err)
				} else {
					fmt.Printf("✅ 迁移记录: user_id=%d, group_id=%d\n", record.UserID, record.UserGroupID)
				}
			} else {
				fmt.Printf("⚠️ 记录已存在，跳过: user_id=%d, group_id=%d\n", record.UserID, record.UserGroupID)
			}
		}

		// 删除 user_groups_users 表
		fmt.Println("🗑️ 删除重复的 user_groups_users 表...")
		err = db.Exec("DROP TABLE IF EXISTS user_groups_users").Error
		if err != nil {
			log.Fatalf("删除 user_groups_users 表失败: %v", err)
		}
		fmt.Println("✅ user_groups_users 表已删除")

	} else if userGroupsUsersExists && !userGroupMembersExists {
		// 只有 user_groups_users 表存在，重命名为 user_group_members
		fmt.Println("🔄 重命名 user_groups_users 表为 user_group_members...")
		err = db.Exec("ALTER TABLE user_groups_users RENAME TO user_group_members").Error
		if err != nil {
			log.Fatalf("重命名表失败: %v", err)
		}

		// 重命名列名
		err = db.Exec("ALTER TABLE user_group_members RENAME COLUMN user_group_id TO group_id").Error
		if err != nil {
			log.Fatalf("重命名列失败: %v", err)
		}
		fmt.Println("✅ 表重命名完成")

	} else if userGroupMembersExists && !userGroupsUsersExists {
		fmt.Println("✅ 只有 user_group_members 表存在，无需清理")
	} else {
		fmt.Println("⚠️ 两个表都不存在，可能是新的数据库")
	}

	// 验证最终结果
	var finalCount int64
	err = db.Table("user_group_members").Count(&finalCount).Error
	if err != nil {
		log.Printf("验证最终结果失败: %v", err)
	} else {
		fmt.Printf("📊 最终 user_group_members 表中有 %d 条记录\n", finalCount)
	}

	fmt.Println("🎉 清理完成！")
}
