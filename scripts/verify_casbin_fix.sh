#!/bin/bash

# Casbin RBAC修复验证脚本
# 用于验证修复后的权限系统是否正常工作

set -e

echo "=== Casbin RBAC修复验证脚本 ==="
echo "开始验证修复后的权限系统..."

# 1. 检查数据库连接
echo "1. 检查数据库连接..."
if ! mysql -u root -p -e "USE kubeops;" 2>/dev/null; then
    echo "❌ 数据库连接失败，请检查数据库配置"
    exit 1
fi
echo "✅ 数据库连接成功"

# 2. 执行修复SQL脚本
echo "2. 执行修复SQL脚本..."
if mysql -u root -p kubeops < scripts/fix_casbin_rbac.sql; then
    echo "✅ SQL脚本执行成功"
else
    echo "❌ SQL脚本执行失败"
    exit 1
fi

# 3. 验证RBAC模型配置
echo "3. 验证RBAC模型配置..."
RBAC_MODEL_COUNT=$(mysql -u root -p -s -N -e "SELECT COUNT(*) FROM kubeops.rbac_models WHERE is_active = 1;")
if [ "$RBAC_MODEL_COUNT" -eq 1 ]; then
    echo "✅ RBAC模型配置正确"
else
    echo "❌ RBAC模型配置错误，期望1个激活模型，实际$RBAC_MODEL_COUNT个"
    exit 1
fi

# 4. 验证admin用户组策略
echo "4. 验证admin用户组策略..."
ADMIN_POLICY_COUNT=$(mysql -u root -p -s -N -e "SELECT COUNT(*) FROM kubeops.casbin_rule WHERE ptype = 'p2' AND v0 = 'admin';")
if [ "$ADMIN_POLICY_COUNT" -eq 1 ]; then
    echo "✅ admin用户组策略正确"
else
    echo "❌ admin用户组策略错误，期望1条策略，实际$ADMIN_POLICY_COUNT条"
    exit 1
fi

# 5. 验证用户组成员关系
echo "5. 验证用户组成员关系..."
USER_GROUP_COUNT=$(mysql -u root -p -s -N -e "SELECT COUNT(*) FROM kubeops.casbin_rule WHERE ptype = 'g' AND v0 = 'user:1' AND v1 = 'admin';")
if [ "$USER_GROUP_COUNT" -eq 1 ]; then
    echo "✅ 用户组成员关系正确"
else
    echo "❌ 用户组成员关系错误，期望1条关系，实际$USER_GROUP_COUNT条"
    exit 1
fi

# 6. 显示当前策略详情
echo "6. 当前策略详情:"
echo "--- RBAC模型配置 ---"
mysql -u root -p -e "SELECT name, description, is_active FROM kubeops.rbac_models WHERE is_active = 1;"

echo "--- Casbin策略 ---"
mysql -u root -p -e "SELECT ptype, v0, v1, v2, v3 FROM kubeops.casbin_rule ORDER BY ptype, v0;"

# 7. 测试应用编译
echo "7. 测试应用编译..."
if go build -o bin/kubeops cmd/main/main.go; then
    echo "✅ 应用编译成功"
else
    echo "❌ 应用编译失败"
    exit 1
fi

echo ""
echo "=== 验证完成 ==="
echo "✅ 所有验证项目通过"
echo ""
echo "下一步操作："
echo "1. 重启应用以加载新的RBAC模型配置"
echo "2. 测试admin用户登录和权限访问"
echo "3. 检查应用日志确认权限检查正常工作"
echo ""
echo "测试命令示例："
echo "curl -X POST http://localhost:8080/api/v1/auth/login \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"username\":\"admin\",\"password\":\"admin123\"}'" 