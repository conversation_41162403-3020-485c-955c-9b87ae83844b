# KubeOps 平台

KubeOps 是一款面向企业的 Kubernetes 运维平台，旨在实现多集群统一管理、权限细粒度控制、自动化审批、操作可审计的运维白屏化平台。通过友好的 Web 界面和强大的 API 能力，帮助 DevOps、SRE 实现一站式的 Kubernetes 日常管理。

## 项目结构

```
.
├── cmd                     # 应用入口
│   └── main                # 主入口
├── configs                 # 配置文件
├── internal                # 内部包
│   ├── api                 # API 处理
│   ├── bootstrap           # 应用初始化
│   ├── middleware          # 中间件
│   ├── model               # 数据模型
│   ├── repository          # 数据访问层
│   └── service             # 业务逻辑层
├── pkg                     # 共享包
│   ├── auth                # 认证相关
│   ├── k8s                 # Kubernetes 客户端
│   ├── response            # 统一API响应
└── web                     # Web 前端
```

## 核心功能

- **多集群管理**：通过API Server地址和kubeconfig证书统一管理多个Kubernetes集群
- **基于RBAC的权限控制**：细粒度的访问控制（支持到工作负载级别）
  - **平台权限控制**：基于Casbin的API访问控制
  - **K8s权限同步**：自动将平台权限同步到K8s集群RBAC，实现双重权限保护
- **审批流程**：集群资源操作审批
- **用户管理**：支持Keycloak OIDC认证
- **操作审计**：记录所有关键操作
- **工作负载管理**：支持Deployment、StatefulSet、DaemonSet等

## 集群管理说明

KubeOps通过以下方式管理Kubernetes集群：

1. **集群注册**：填写集群的API Server地址和kubeconfig证书
2. **连接验证**：自动验证集群连接有效性和权限
3. **信息同步**：定期同步集群状态、节点数、Pod数等信息

### 集群配置要求

- **API Server地址**：集群的Kubernetes API Server访问地址
- **kubeconfig证书**：包含访问集群所需的认证信息
- **网络连通性**：确保KubeOps服务能够访问目标集群

## 技术栈

- 后端：Go、Gin、GORM、Casbin
- 前端：Vue.js、Element UI
- 存储：SQLite (可扩展为 MySQL、PostgreSQL)
- 认证：JWT、OIDC认证
- 可观测性：OpenTelemetry

## 架构设计

### 核心概念

1. **用户(User)**：平台用户，支持本地认证和OIDC集成
2. **用户组(UserGroup)**：用户组织结构，支持Keycloak组映射
3. **项目(Project)**：业务项目，与Kubernetes命名空间一对一映射
4. **集群(Cluster)**：Kubernetes集群
5. **应用(Application)**：具体的工作负载，如Deployment、StatefulSet等
6. **权限(Permission)**：基于RBAC的权限控制

### 项目与命名空间映射关系

**核心设计原则**：项目(Project)与Kubernetes命名空间(Namespace)是**一对一映射关系**

- **映射规则**：项目名称直接对应K8s中的命名空间名称
- **多集群支持**：一个项目可以部署到多个集群，每个集群中都有对应项目名称的命名空间
- **命名空间管理**：
  - 项目创建时，在所有关联集群中自动创建对应的命名空间
  - 项目删除时，清理所有集群中对应的命名空间
  - 项目名称必须符合Kubernetes命名空间命名规范

```
项目: web-frontend
├── 集群A: namespace/web-frontend
├── 集群B: namespace/web-frontend
└── 集群C: namespace/web-frontend
```

### 多集群部署模型

- **项目级多集群**：一个项目可以部署到多个集群
- **命名空间一致性**：每个集群中都有对应项目名称的命名空间
- **应用同步范围**：只同步已创建项目对应命名空间下的工作负载
- **系统命名空间过滤**：忽略kube-system、kube-public等系统命名空间

## 数据模型设计

### Go 结构体定义

#### 1. 用户与认证相关结构体

```go
// User 用户模型
type User struct {
    ID        uint           `gorm:"primaryKey" json:"id"`
    Username  string         `gorm:"size:50;uniqueIndex;not null" json:"username"`
    Password  string         `gorm:"size:100" json:"-"` // 移除not null约束，允许OIDC用户没有密码
    Email     string         `gorm:"size:100;uniqueIndex" json:"email"`
    Name      string         `gorm:"size:50" json:"name"`
    Avatar    string         `gorm:"size:255" json:"avatar"`
    Phone     string         `gorm:"size:20" json:"phone"`
    Status    int            `gorm:"default:1" json:"status"`       // 1-正常, 0-禁用
    IsAdmin   bool           `gorm:"default:false" json:"is_admin"` // 是否为超级管理员
    LastLogin *time.Time     `json:"last_login"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

    // OIDC相关信息
    OIDCSubject      string `gorm:"column:o_id_c_subject;size:255;uniqueIndex" json:"oidc_subject"` // OIDC唯一标识
    IdentityProvider string `gorm:"size:50" json:"identity_provider"`                               // 身份提供者(keycloak)

    // 飞书相关信息 - 从Keycloak返回的数据
    FeishuOpenID  string `gorm:"size:100" json:"feishu_open_id"`  // 移除uniqueIndex以支持多渠道登录
    FeishuUnionID string `gorm:"size:100" json:"feishu_union_id"` // 移除uniqueIndex以支持多渠道登录
    FeishuUserID  string `gorm:"size:100" json:"feishu_user_id"`  // 新增飞书用户ID

    // UserGroups 用户所在的组，通过Keycloak返回的groups映射
    UserGroups []UserGroup `gorm:"many2many:user_group_members;" json:"user_groups"`
}

// UserGroup 用户组模型
type UserGroup struct {
    ID          uint      `gorm:"primaryKey" json:"id"`
    Name        string    `gorm:"uniqueIndex:idx_group_name_domain;not null" json:"name"`
    Domain      string    `gorm:"uniqueIndex:idx_group_name_domain;default:''" json:"domain"` // 添加域字段
    Description string    `json:"description"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`

    // 关联关系
    Users []User `gorm:"many2many:user_group_members;" json:"users,omitempty"`

    // 资源权限关联
    ResourcePermissions []*ResourcePermission `gorm:"many2many:group_resource_permissions;" json:"resource_permissions,omitempty"`
}

// UserGroupMember 用户组成员关系，记录用户是手动添加还是自动映射
type UserGroupMember struct {
    GroupID    uint      `gorm:"primaryKey;autoIncrement:false" json:"group_id"` // 用户组ID
    UserID     uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`  // 用户ID
    IsAutoJoin bool      `gorm:"default:false" json:"is_auto_join"`              // 是否自动加入（通过映射）
    CreatedAt  time.Time `json:"created_at"`                                     // 创建时间
}

// KeycloakGroupMapping Keycloak组织映射表
type KeycloakGroupMapping struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    KeycloakGroup string    `gorm:"size:255;uniqueIndex;not null" json:"keycloak_group"` // Keycloak组名称
    UserGroupID   uint      `gorm:"not null;index" json:"user_group_id"`                 // 对应的本地用户组ID
    Description   string    `gorm:"size:255" json:"description"`                         // 映射描述
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
```

#### 2. 集群与项目相关结构体

```go
// Cluster 集群配置模型
type Cluster struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    Name        string         `gorm:"size:50;uniqueIndex;not null" json:"name"`
    DisplayName string         `gorm:"size:100" json:"display_name"`
    ApiServer   string         `gorm:"size:200;not null" json:"api_server"`
    KubeConfig  string         `gorm:"type:text" json:"-"`
    Status      ClusterStatus  `gorm:"default:1" json:"status"` // 1-正常, 0-异常, 2-维护中
    Version     string         `gorm:"size:20" json:"version"`
    Description string         `gorm:"size:200" json:"description"`
    CreatedBy   uint           `json:"created_by"`

    // 集群信息
    NodeCount       int       `json:"node_count"`
    PodCount        int       `json:"pod_count"`
    NamespaceCount  int       `json:"namespace_count"`
    LastHealthCheck time.Time `json:"last_health_check"`

    // 时间戳
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ClusterStatus 集群状态枚举
type ClusterStatus int

const (
    ClusterStatusOffline     ClusterStatus = 0 // 离线/异常
    ClusterStatusOnline      ClusterStatus = 1 // 在线/正常
    ClusterStatusMaintenance ClusterStatus = 2 // 维护中
)

// Project 项目模型 - 可以部署到多个集群
type Project struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    Name        string         `gorm:"size:50;uniqueIndex;not null" json:"name"`        // 项目名称
    DisplayName string         `gorm:"size:100" json:"display_name"`                    // 显示名称
    Description string         `gorm:"size:500" json:"description"`                     // 项目描述
    Status      ProjectStatus  `gorm:"default:1" json:"status"`                         // 项目状态
    CreatedBy   uint           `json:"created_by"`                                      // 创建者ID
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

    // 关联关系
    Members         []User            `gorm:"many2many:project_members;" json:"members,omitempty"`
    ProjectClusters []ProjectCluster  `json:"project_clusters,omitempty"` // 项目在各集群的部署信息
    Applications    []Application     `json:"applications,omitempty"`     // 项目下的应用
}

// ProjectCluster 项目集群关联表 - 项目在特定集群的部署配置
type ProjectCluster struct {
    ID          uint           `gorm:"primaryKey" json:"id"`
    ProjectID   uint           `gorm:"not null;index" json:"project_id"`                // 项目ID
    ClusterID   uint           `gorm:"not null;index" json:"cluster_id"`                // 集群ID
    Namespace   string         `gorm:"size:50;not null" json:"namespace"`               // 在该集群中的命名空间名称
    Status      int8           `gorm:"default:1" json:"status"`                         // 状态：0=未激活，1=激活，2=已归档
    Config      datatypes.JSON `json:"config"`                                          // 集群特定配置
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`

    // 关联关系
    Project *Project `gorm:"foreignKey:ProjectID" json:"project,omitempty"`
    Cluster *Cluster `gorm:"foreignKey:ClusterID" json:"cluster,omitempty"`
}

// ProjectStatus 项目状态枚举
type ProjectStatus int

const (
    ProjectStatusInactive ProjectStatus = 0 // 未激活
    ProjectStatusActive   ProjectStatus = 1 // 激活
    ProjectStatusArchived ProjectStatus = 2 // 已归档
)



// ProjectMember 项目成员关联表（仅用于界面展示，不承载权限控制）
type ProjectMember struct {
    ProjectID uint      `gorm:"primaryKey;autoIncrement:false" json:"project_id"`
    UserID    uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
    Role      string    `gorm:"size:50;not null" json:"role"` // 项目角色：owner, admin, developer, viewer（仅用于界面展示）
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
// 注意：此模型仅用于界面展示项目成员关系，实际权限控制完全基于用户组权限

// ClusterResource 集群资源模型（用于资源级权限控制）
type ClusterResource struct {
    ID        uint           `gorm:"primaryKey" json:"id"`
    GroupID   uint           `gorm:"index" json:"group_id"`            // 用户组ID
    Cluster   string         `gorm:"size:50;not null" json:"cluster"`  // 集群名称
    Namespace string         `gorm:"size:50" json:"namespace"`         // 命名空间，为空表示所有命名空间
    Resource  string         `gorm:"size:50;not null" json:"resource"` // 资源类型: deployment, pod, service等
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}
```

#### 3. 应用相关结构体

```go
// ApplicationStatus 应用状态枚举
type ApplicationStatus int8

const (
    ApplicationStatusInactive ApplicationStatus = 0 // 未激活
    ApplicationStatusActive   ApplicationStatus = 1 // 激活
    ApplicationStatusArchived ApplicationStatus = 2 // 已归档
)

// ApplicationSource 应用来源枚举
type ApplicationSource string

const (
    ApplicationSourceManual     ApplicationSource = "manual"     // 手动创建
    ApplicationSourceDiscovered ApplicationSource = "discovered" // 从K8s发现
)

// ApplicationSyncStatus 应用同步状态枚举
type ApplicationSyncStatus string

const (
    ApplicationSyncStatusSynced  ApplicationSyncStatus = "synced"  // 已同步
    ApplicationSyncStatusPending ApplicationSyncStatus = "pending" // 待同步
    ApplicationSyncStatusFailed  ApplicationSyncStatus = "failed"  // 同步失败
)

// Application 应用模型 - 混合模式：支持手动创建和自动发现
type Application struct {
    ID           uint                  `gorm:"primaryKey" json:"id"`
    Name         string                `gorm:"size:100;not null" json:"name"`                              // 应用名称
    DisplayName  string                `gorm:"size:100" json:"display_name"`                               // 显示名称
    Description  string                `gorm:"size:500" json:"description"`                                // 描述
    ProjectID    uint                  `gorm:"not null;index" json:"project_id"`                           // 所属项目ID
    WorkloadType string                `gorm:"size:50;not null" json:"workload_type"`                      // 工作负载类型: deployment, statefulset, daemonset, job, cronjob

    // 业务信息（手动创建时填写，自动发现时为空）
    GitRepoURL   string                `gorm:"size:500" json:"git_repo_url"`                               // Git仓库地址
    GitBranch    string                `gorm:"size:100;default:main" json:"git_branch"`                    // 默认Git分支
    Language     string                `gorm:"size:50" json:"language"`                                    // 编程语言
    Framework    string                `gorm:"size:50" json:"framework"`                                   // 框架
    OwnerID      *uint                 `gorm:"index" json:"owner_id"`                                      // 负责人ID

    // 应用管理信息
    Source       ApplicationSource     `gorm:"size:20;default:manual" json:"source"`                       // 应用来源：manual/discovered
    SyncStatus   ApplicationSyncStatus `gorm:"size:20;default:synced" json:"sync_status"`                  // 同步状态
    IsComplete   bool                  `gorm:"default:false" json:"is_complete"`                           // 是否完善了业务信息
    Status       ApplicationStatus     `gorm:"default:1" json:"status"`                                    // 状态：0=未激活，1=激活，2=已归档

    CreatedAt    time.Time             `json:"created_at"`
    UpdatedAt    time.Time             `json:"updated_at"`
    DeletedAt    gorm.DeletedAt        `gorm:"index" json:"-"`

    // 关联关系
    Project             *Project             `gorm:"foreignKey:ProjectID" json:"project,omitempty"`
    Owner               *User                `gorm:"foreignKey:OwnerID" json:"owner,omitempty"`
    Members             []User               `gorm:"many2many:application_members;" json:"members,omitempty"`
    ApplicationClusters []ApplicationCluster `json:"application_clusters,omitempty"` // 应用在各集群的部署信息
}

// ApplicationCluster 应用集群关联表 - 应用在特定集群的部署配置
type ApplicationCluster struct {
    ID                uint              `gorm:"primaryKey" json:"id"`
    ApplicationID     uint              `gorm:"not null;index" json:"application_id"`                   // 应用ID
    ProjectClusterID  uint              `gorm:"not null;index" json:"project_cluster_id"`               // 项目集群关联ID
    Replicas          int               `gorm:"default:1" json:"replicas"`                              // 副本数
    Image             string            `gorm:"size:500" json:"image"`                                  // 镜像地址
    Ports             datatypes.JSON    `json:"ports"`                                                  // 端口配置
    EnvVars           datatypes.JSON    `json:"env_vars"`                                               // 环境变量
    Resources         datatypes.JSON    `json:"resources"`                                              // 资源配置
    Labels            datatypes.JSON    `json:"labels"`                                                 // 标签
    Annotations       datatypes.JSON    `json:"annotations"`                                            // 注解
    Status            ApplicationStatus `gorm:"default:1" json:"status"`                               // 状态：0=未激活，1=激活，2=已归档
    K8sStatus         string            `gorm:"size:50" json:"k8s_status"`                             // K8s状态：Running/Pending/Failed等
    LastDeployedAt    *time.Time        `json:"last_deployed_at"`                                       // 最后部署时间
    CreatedAt         time.Time         `json:"created_at"`
    UpdatedAt         time.Time         `json:"updated_at"`
    DeletedAt         gorm.DeletedAt    `gorm:"index" json:"-"`

    // 关联关系
    Application    *Application    `gorm:"foreignKey:ApplicationID" json:"application,omitempty"`
    ProjectCluster *ProjectCluster `gorm:"foreignKey:ProjectClusterID" json:"project_cluster,omitempty"`
}

// ApplicationMember 应用成员关联表（仅用于界面展示，不承载权限控制）
type ApplicationMember struct {
    ApplicationID uint      `gorm:"primaryKey;autoIncrement:false" json:"application_id"`
    UserID        uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
    Role          string    `gorm:"size:50;not null" json:"role"` // 角色：owner, maintainer, developer, viewer（仅用于界面展示）
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
}
// 注意：此模型仅用于界面展示应用成员关系，实际权限控制完全基于用户组权限

// ClusterResource 集群资源模型（用于资源级权限控制）
type ClusterResource struct {
    ID        uint           `gorm:"primaryKey" json:"id"`
    GroupID   uint           `gorm:"index" json:"group_id"`            // 用户组ID
    Cluster   string         `gorm:"size:50;not null" json:"cluster"`  // 集群名称
    Namespace string         `gorm:"size:50" json:"namespace"`         // 命名空间，为空表示所有命名空间
    Resource  string         `gorm:"size:50;not null" json:"resource"` // 资源类型: deployment, pod, service等
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// Alert 告警模型
type Alert struct {
    ID           uint           `gorm:"primaryKey" json:"id"`
    Title        string         `gorm:"size:200;not null" json:"title"`
    ClusterName  string         `gorm:"size:50;index" json:"cluster_name"`
    Namespace    string         `gorm:"size:50;index" json:"namespace"`
    Level        string         `gorm:"size:20;not null" json:"level"` // critical, warning, info
    Resource     string         `gorm:"size:100" json:"resource"`      // 资源名称
    ResourceKind string         `gorm:"size:50" json:"resource_kind"`  // 资源类型
    Message      string         `gorm:"type:text;not null" json:"message"`
    Source       string         `gorm:"size:50;not null" json:"source"` // 告警来源
    Status       int            `gorm:"default:0" json:"status"`        // 0-未处理, 1-处理中, 2-已解决, 3-已忽略
    CreatedAt    time.Time      `json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}
```

#### 4. 权限相关结构体

```go
// ResourcePermission 资源权限模型 - 层次化权限设计
type ResourcePermission struct {
    ID           uint      `gorm:"primaryKey" json:"id"`
    Name         string    `gorm:"size:100;not null" json:"name"`                       // 权限名称
    ResourceType string    `gorm:"size:50;not null" json:"resource_type"`               // 资源类型: api, k8s, ui
    ResourcePath string    `gorm:"size:200;not null" json:"resource_path"`              // 资源路径: system:api:users, cluster:*:project:*:k8s:pods
    Actions      string    `gorm:"size:200;not null" json:"actions"`                    // 允许的操作: get,list,create,update,delete 或 *
    ScopeLevel   string    `gorm:"size:50;not null" json:"scope_level"`                 // 权限级别: system, cluster, project, application
    Description  string    `gorm:"size:500" json:"description"`                         // 描述
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
}

// GroupResourcePermission 用户组权限关联表
type GroupResourcePermission struct {
    GroupID              uint      `gorm:"primaryKey;autoIncrement:false" json:"group_id"`
    ResourcePermissionID uint      `gorm:"primaryKey;autoIncrement:false" json:"resource_permission_id"`
    CreatedAt            time.Time `json:"created_at"`
}

// UserResourcePermission 用户直接权限关联表（用于特殊授权场景）
type UserResourcePermission struct {
    UserID               uint      `gorm:"primaryKey;autoIncrement:false" json:"user_id"`
    ResourcePermissionID uint      `gorm:"primaryKey;autoIncrement:false" json:"resource_permission_id"`
    GrantedBy            uint      `gorm:"not null" json:"granted_by"`        // 授权人ID
    GrantReason          string    `gorm:"size:500" json:"grant_reason"`      // 授权原因
    ExpiresAt            *time.Time `json:"expires_at"`                       // 过期时间，NULL表示永不过期
    CreatedAt            time.Time `json:"created_at"`

    // 关联关系
    User               *User               `gorm:"foreignKey:UserID" json:"user,omitempty"`
    ResourcePermission *ResourcePermission `gorm:"foreignKey:ResourcePermissionID" json:"resource_permission,omitempty"`
    GrantedByUser      *User               `gorm:"foreignKey:GrantedBy" json:"granted_by_user,omitempty"`
}

// RBACModel 表示RBAC模型配置，存储在数据库中而不是静态文件
type RBACModel struct {
    ID          uint      `gorm:"primaryKey" json:"id"`     // 主键ID，数据库自增
    Name        string    `json:"name"`                     // 模型名称，用于标识不同的RBAC策略模型
    Description string    `json:"description"`              // 模型描述，详细解释模型的用途和特点
    Content     string    `gorm:"type:text" json:"content"` // 模型配置内容，使用Casbin语法定义的RBAC模型
    IsActive    bool      `json:"is_active"`                // 是否为当前激活的模型，true表示当前使用的模型
    CreatedAt   time.Time `json:"created_at"`               // 创建时间，记录模型创建的时间戳
    UpdatedAt   time.Time `json:"updated_at"`               // 更新时间，记录模型最后更新的时间戳
}
```

#### 5. 审批流程相关结构体

```go
// ApprovalFlow 审批流程模型
type ApprovalFlow struct {
    ID            uint           `gorm:"primaryKey" json:"id"`
    Name          string         `gorm:"size:100;not null" json:"name"`
    Description   string         `gorm:"size:255" json:"description"`
    Type          string         `gorm:"size:50;not null" json:"type"` // resource_create, resource_update, resource_delete
    ResourceType  string         `gorm:"size:50" json:"resource_type"` // pod, deployment, service等
    Namespace     string         `gorm:"size:100" json:"namespace"`    // 命名空间匹配，空表示所有
    ClusterID     uint           `gorm:"default:0" json:"cluster_id"`  // 0表示所有集群
    Status        int            `gorm:"default:1" json:"status"`      // 1启用，0禁用
    CreatedAt     time.Time      `json:"created_at"`
    UpdatedAt     time.Time      `json:"updated_at"`
    DeletedAt     gorm.DeletedAt `gorm:"index" json:"-"`
}

// ApprovalRequest 审批请求
type ApprovalRequest struct {
    ID           uint           `gorm:"primaryKey" json:"id"`
    Title        string         `gorm:"size:200;not null" json:"title"`
    Description  string         `gorm:"type:text" json:"description"`
    Type         string         `gorm:"size:50;not null" json:"type"`
    ResourceType string         `gorm:"size:50" json:"resource_type"`
    ResourceName string         `gorm:"size:100" json:"resource_name"`
    Namespace    string         `gorm:"size:100" json:"namespace"`
    ClusterID    uint           `json:"cluster_id"`
    RequestData  string         `gorm:"type:text" json:"request_data"`
    Status       string         `gorm:"size:20;default:pending" json:"status"`
    ApproverID   *uint          `json:"approver_id"`
    ApprovedAt   *time.Time     `json:"approved_at"`
    Reason       string         `gorm:"type:text" json:"reason"`
    CreatedBy    uint           `json:"created_by"`
    CreatedAt    time.Time      `json:"created_at"`
    UpdatedAt    time.Time      `json:"updated_at"`
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`

    // 关联关系
    Creator  *User    `gorm:"foreignKey:CreatedBy" json:"creator,omitempty"`
    Approver *User    `gorm:"foreignKey:ApproverID" json:"approver,omitempty"`
    Cluster  *Cluster `gorm:"foreignKey:ClusterID" json:"cluster,omitempty"`
}

// ApprovalRequestStep 审批步骤
type ApprovalRequestStep struct {
    ID                uint       `gorm:"primaryKey" json:"id"`
    ApprovalRequestID uint       `gorm:"not null;index" json:"approval_request_id"`
    StepOrder         int        `gorm:"not null" json:"step_order"`
    ApproverID        uint       `gorm:"not null" json:"approver_id"`
    Status            int8       `gorm:"default:0" json:"status"`
    ApprovedAt        *time.Time `json:"approved_at"`
    Reason            string     `gorm:"size:500" json:"reason"`
    CreatedAt         time.Time  `json:"created_at"`
    UpdatedAt         time.Time  `json:"updated_at"`

    // 关联关系
    ApprovalRequest *ApprovalRequest `gorm:"foreignKey:ApprovalRequestID" json:"approval_request,omitempty"`
    Approver        *User            `gorm:"foreignKey:ApproverID" json:"approver,omitempty"`
}
```

#### 6. 审计日志相关结构体

```go
// AuditLog 审计日志模型 - 优化版，支持"5W1H"审计要素
type AuditLog struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    UserID        uint      `gorm:"index" json:"user_id"`                  // 操作用户ID
    Username      string    `gorm:"size:50" json:"username"`               // 用户名
    UserType      string    `gorm:"size:20" json:"user_type"`              // 用户类型: local, feishu
    Department    string    `gorm:"size:100" json:"department"`            // 部门/组织
    Role          string    `gorm:"size:50" json:"role"`                   // 角色
    Action        string    `gorm:"size:50;not null" json:"action"`        // 操作类型: create, update, delete, read, approve等
    ResourceType  string    `gorm:"size:50;not null" json:"resource_type"` // 资源类型: user, role, cluster, pod等
    ResourceID    string    `gorm:"size:100" json:"resource_id"`           // 资源ID或名称
    RequestPath   string    `gorm:"size:200" json:"request_path"`          // API路径
    RequestParams string    `gorm:"type:text" json:"request_params"`       // 请求参数，JSON格式
    Result        string    `gorm:"size:20" json:"result"`                 // 操作结果: success, failed
    ErrorMessage  string    `gorm:"type:text" json:"error_message"`        // 失败原因
    IP            string    `gorm:"size:50" json:"ip"`                     // 来源IP
    Client        string    `gorm:"size:255" json:"client"`                // 客户端信息
    TraceID       string    `gorm:"size:100" json:"trace_id"`              // 链路追踪ID
    CreatedAt     time.Time `gorm:"index" json:"created_at"`               // 操作时间
}

// AuditArchive 审计归档记录模型
type AuditArchive struct {
    ID           uint           `gorm:"primaryKey" json:"id"`
    ArchiveName  string         `gorm:"size:200;not null" json:"archive_name"` // 归档文件名
    Quarter      string         `gorm:"size:10;not null" json:"quarter"`       // 归档季度，格式：2024-Q1
    StartTime    time.Time      `gorm:"not null" json:"start_time"`            // 归档数据开始时间
    EndTime      time.Time      `gorm:"not null" json:"end_time"`              // 归档数据结束时间
    RecordCount  int64          `gorm:"not null" json:"record_count"`          // 归档记录数
    FileSize     int64          `gorm:"not null" json:"file_size"`             // 文件大小（字节）
    OBSKey       string         `gorm:"size:500;not null" json:"obs_key"`      // OBS对象键
    Status       string         `gorm:"size:20;not null" json:"status"`        // 状态: pending, processing, completed, failed
    ErrorMessage string         `gorm:"type:text" json:"error_message"`        // 错误信息
    CreatedBy    uint           `gorm:"not null" json:"created_by"`            // 创建人ID
    CreatedAt    time.Time      `json:"created_at"`                            // 创建时间
    UpdatedAt    time.Time      `json:"updated_at"`                            // 更新时间
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}


```

#### 7. 系统配置相关结构体

```go
// SystemConfig 统一系统配置模型，包含所有配置项作为直接属性
type SystemConfig struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`

    // OIDC配置
    OIDCEnabled      bool   `gorm:"column:oidc_enabled" json:"oidc_enabled"`
    OIDCIssuerURL    string `gorm:"column:oidc_issuer_url" json:"oidc_issuer_url"`
    OIDCClientID     string `gorm:"column:oidc_client_id" json:"oidc_client_id"`
    OIDCClientSecret string `gorm:"column:oidc_client_secret" json:"oidc_client_secret"`
    OIDCRedirectURI  string `gorm:"column:oidc_redirect_uri" json:"oidc_redirect_uri"`
    OIDCScopes       string `gorm:"column:oidc_scopes" json:"oidc_scopes"`
    OIDCGroupsClaim  string `gorm:"column:oidc_groups_claim" json:"oidc_groups_claim"`
    OIDCRolesClaim   string `gorm:"column:oidc_roles_claim" json:"oidc_roles_claim"`

    // 飞书配置
    FeishuAppID     string `gorm:"column:feishu_app_id" json:"feishu_app_id"`
    FeishuAppSecret string `gorm:"column:feishu_app_secret" json:"feishu_app_secret"`

    // OBS配置
    OBSEnabled       bool   `gorm:"column:obs_enabled" json:"obs_enabled"`
    OBSEndpoint      string `gorm:"column:obs_endpoint" json:"obs_endpoint"`
    OBSAccessKey     string `gorm:"column:obs_access_key" json:"obs_access_key"`
    OBSSecretKey     string `gorm:"column:obs_secret_key" json:"obs_secret_key"`
    OBSBucket        string `gorm:"column:obs_bucket" json:"obs_bucket"`
    OBSRegion        string `gorm:"column:obs_region" json:"obs_region"`
    OBSEncryptionKey string `gorm:"column:obs_encryption_key" json:"obs_encryption_key"`

    // 审计配置
    AuditRetentionDays      int       `gorm:"column:audit_retention_days" json:"audit_retention_days"`           // 审计日志保留天数
    AuditArchiveInterval    string    `gorm:"column:audit_archive_interval" json:"audit_archive_interval"`       // 审计日志归档间隔
    AuditArchiveEnabled     bool      `gorm:"column:audit_archive_enabled" json:"audit_archive_enabled"`         // 是否启用归档
    AuditEncryptionEnabled  bool      `gorm:"column:audit_encryption_enabled" json:"audit_encryption_enabled"`   // 是否启用加密
    AuditEncryptionKey      string    `gorm:"column:audit_encryption_key" json:"audit_encryption_key"`           // 加密密钥（加密存储）
    AuditLastArchiveTime    time.Time `gorm:"column:audit_last_archive_time" json:"audit_last_archive_time"`     // 上次归档时间

    // 系统配置
    SystemName         string `gorm:"column:system_name" json:"system_name"`
    SystemLogo         string `gorm:"column:system_logo;type:text" json:"system_logo"`
    SystemContactEmail string `gorm:"column:system_contact_email" json:"system_contact_email"`
    SystemVersion      string `gorm:"column:system_version" json:"system_version"`
    SystemDebugMode    bool   `gorm:"column:system_debug_mode" json:"system_debug_mode"`
}


```

### 数据库表结构设计

#### 1. 用户与认证相关表

#### 1.1 users - 用户表
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码（OIDC用户可为空）',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    name VARCHAR(50) COMMENT '姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号',
    status INTEGER DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
    is_admin BOOLEAN DEFAULT FALSE COMMENT '是否为超级管理员',
    last_login TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    -- OIDC相关字段
    o_id_c_subject VARCHAR(255) UNIQUE COMMENT 'OIDC唯一标识',
    identity_provider VARCHAR(50) COMMENT '身份提供者',
    -- 飞书相关字段
    feishu_open_id VARCHAR(100) COMMENT '飞书OpenID',
    feishu_union_id VARCHAR(100) COMMENT '飞书UnionID',
    feishu_user_id VARCHAR(100) COMMENT '飞书用户ID'
);
```

#### 1.2 user_groups - 用户组表
```sql
CREATE TABLE user_groups (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '用户组名称',
    domain VARCHAR(100) DEFAULT '' COMMENT '域',
    description VARCHAR(500) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_name_domain (name, domain)
);
```

#### 1.3 user_group_members - 用户组成员关联表
```sql
CREATE TABLE user_group_members (
    group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    is_auto_join BOOLEAN DEFAULT FALSE COMMENT '是否自动加入',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (group_id, user_id),
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### 1.4 keycloak_group_mappings - Keycloak组映射表
```sql
CREATE TABLE keycloak_group_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    keycloak_group VARCHAR(255) UNIQUE NOT NULL COMMENT 'Keycloak组名称',
    user_group_id INTEGER NOT NULL COMMENT '对应的本地用户组ID',
    description VARCHAR(255) COMMENT '映射描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_group_id) REFERENCES user_groups(id) ON DELETE CASCADE
);
```

#### 2. 集群与项目相关表

#### 2.1 clusters - 集群表
```sql
CREATE TABLE clusters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '集群名称',
    display_name VARCHAR(100) COMMENT '显示名称',
    api_server VARCHAR(200) NOT NULL COMMENT 'API Server地址',
    kube_config TEXT COMMENT 'kubeconfig配置',
    status INTEGER DEFAULT 1 COMMENT '状态：0=离线，1=在线，2=维护中',
    version VARCHAR(20) COMMENT 'K8s版本',
    description VARCHAR(200) COMMENT '描述',
    created_by INTEGER COMMENT '创建者',
    node_count INTEGER DEFAULT 0 COMMENT '节点数',
    pod_count INTEGER DEFAULT 0 COMMENT 'Pod数',
    namespace_count INTEGER DEFAULT 0 COMMENT '命名空间数',
    last_health_check TIMESTAMP COMMENT '最后健康检查时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### 2.2 projects - 项目表
```sql
CREATE TABLE projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '项目名称',
    display_name VARCHAR(100) COMMENT '显示名称',
    description VARCHAR(500) COMMENT '描述',
    status INTEGER DEFAULT 1 COMMENT '状态：0=未激活，1=激活，2=已归档',
    created_by INTEGER COMMENT '创建者',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

#### 2.3 project_clusters - 项目集群关联表
```sql
CREATE TABLE project_clusters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL COMMENT '项目ID',
    cluster_id INTEGER NOT NULL COMMENT '集群ID',
    namespace VARCHAR(50) NOT NULL COMMENT '在该集群中的命名空间名称',
    status INTEGER DEFAULT 1 COMMENT '状态：0=未激活，1=激活，2=已归档',
    config TEXT COMMENT '集群特定配置JSON',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE (project_id, cluster_id, namespace),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (cluster_id) REFERENCES clusters(id) ON DELETE CASCADE
);
```

#### 2.4 project_members - 项目成员关联表（仅用于界面展示）
```sql
CREATE TABLE project_members (
    project_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role VARCHAR(50) NOT NULL COMMENT '角色：owner/admin/developer/viewer（仅用于界面展示，不承载权限）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (project_id, user_id),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
-- 注意：此表仅用于界面展示项目成员关系，实际权限控制通过用户组权限实现
```

#### 3. 应用相关表

#### 3.1 applications - 应用表（混合模式）
```sql
CREATE TABLE applications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '应用名称',
    display_name VARCHAR(100) COMMENT '显示名称',
    description VARCHAR(500) COMMENT '描述',
    project_id INTEGER NOT NULL COMMENT '所属项目ID',
    workload_type VARCHAR(50) NOT NULL COMMENT '工作负载类型: deployment, statefulset, daemonset, job, cronjob',

    -- 业务信息（手动创建时填写，自动发现时为空）
    git_repo_url VARCHAR(500) COMMENT 'Git仓库地址',
    git_branch VARCHAR(100) DEFAULT 'main' COMMENT '默认Git分支',
    language VARCHAR(50) COMMENT '编程语言',
    framework VARCHAR(50) COMMENT '框架',
    owner_id INTEGER COMMENT '负责人ID',

    -- 应用管理信息
    source VARCHAR(20) DEFAULT 'manual' COMMENT '应用来源: manual=手动创建, discovered=从K8s发现',
    sync_status VARCHAR(20) DEFAULT 'synced' COMMENT '同步状态: synced=已同步, pending=待同步, failed=同步失败',
    is_complete BOOLEAN DEFAULT FALSE COMMENT '是否完善了业务信息',
    status INTEGER DEFAULT 1 COMMENT '状态：0=未激活，1=激活，2=已归档',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE (project_id, name),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id)
);
```

#### 3.2 application_clusters - 应用集群关联表
```sql
CREATE TABLE application_clusters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    application_id INTEGER NOT NULL COMMENT '应用ID',
    project_cluster_id INTEGER NOT NULL COMMENT '项目集群关联ID',
    replicas INTEGER DEFAULT 1 COMMENT '副本数',
    image VARCHAR(500) COMMENT '镜像地址',
    ports TEXT COMMENT '端口配置JSON',
    env_vars TEXT COMMENT '环境变量JSON',
    resources TEXT COMMENT '资源配置JSON',
    labels TEXT COMMENT '标签JSON',
    annotations TEXT COMMENT '注解JSON',
    status INTEGER DEFAULT 1 COMMENT '状态：0=未激活，1=激活，2=已归档',
    k8s_status VARCHAR(50) COMMENT 'K8s状态：Running/Pending/Failed等',
    last_deployed_at TIMESTAMP COMMENT '最后部署时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    UNIQUE (application_id, project_cluster_id),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (project_cluster_id) REFERENCES project_clusters(id) ON DELETE CASCADE
);
```

#### 3.3 application_members - 应用成员关联表（仅用于界面展示）
```sql
CREATE TABLE application_members (
    application_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    role VARCHAR(50) NOT NULL COMMENT '角色：owner/maintainer/developer/viewer（仅用于界面展示，不承载权限）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (application_id, user_id),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
-- 注意：此表仅用于界面展示应用成员关系，实际权限控制通过用户组权限实现
```

#### 3.4 cluster_resources - 集群资源表
```sql
CREATE TABLE cluster_resources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL COMMENT '用户组ID',
    cluster VARCHAR(50) NOT NULL COMMENT '集群名称',
    namespace VARCHAR(50) COMMENT '命名空间，为空表示所有命名空间',
    resource VARCHAR(50) NOT NULL COMMENT '资源类型: deployment, pod, service等',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE
);
```

#### 4. 权限相关表

#### 4.1 resource_permissions - 资源权限表（层次化设计）
```sql
CREATE TABLE resource_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '权限名称',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型: api, k8s, ui',
    resource_path VARCHAR(200) NOT NULL COMMENT '资源路径: system:api:users, cluster:*:project:*:k8s:pods',
    actions VARCHAR(200) NOT NULL COMMENT '允许的操作: get,list,create,update,delete 或 *',
    scope_level VARCHAR(50) NOT NULL COMMENT '权限级别: system, cluster, project, application',
    description VARCHAR(500) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (resource_type, resource_path, actions)
);
```

#### 4.2 group_resource_permissions - 用户组权限关联表
```sql
CREATE TABLE group_resource_permissions (
    group_id INTEGER NOT NULL,
    resource_permission_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (group_id, resource_permission_id),
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (resource_permission_id) REFERENCES resource_permissions(id) ON DELETE CASCADE
);
```

#### 4.3 user_resource_permissions - 用户直接权限关联表
```sql
CREATE TABLE user_resource_permissions (
    user_id INTEGER NOT NULL,
    resource_permission_id INTEGER NOT NULL,
    granted_by INTEGER NOT NULL COMMENT '授权人ID',
    grant_reason VARCHAR(500) COMMENT '授权原因',
    expires_at TIMESTAMP COMMENT '过期时间，NULL表示永不过期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, resource_permission_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (resource_permission_id) REFERENCES resource_permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id)
);
```

#### 4.4 rbac_models - RBAC模型表
```sql
CREATE TABLE rbac_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '模型名称',
    description TEXT COMMENT '模型描述',
    content TEXT NOT NULL COMMENT '模型配置内容',
    is_active BOOLEAN DEFAULT FALSE COMMENT '是否为当前激活的模型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 5. 审批流程相关表

#### 5.1 approval_flows - 审批流程配置表
```sql
CREATE TABLE approval_flows (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '流程名称',
    description VARCHAR(255) COMMENT '描述',
    type VARCHAR(50) NOT NULL COMMENT '流程类型: resource_create, resource_update, resource_delete',
    resource_type VARCHAR(50) COMMENT '资源类型: pod, deployment, service等',
    namespace VARCHAR(100) COMMENT '命名空间匹配，空表示所有',
    cluster_id INTEGER DEFAULT 0 COMMENT '集群ID，0表示所有集群',
    status INTEGER DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### 5.2 approval_requests - 审批请求表
```sql
CREATE TABLE approval_requests (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '申请标题',
    description TEXT COMMENT '申请描述',
    type VARCHAR(50) NOT NULL COMMENT '申请类型',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_name VARCHAR(100) COMMENT '资源名称',
    namespace VARCHAR(100) COMMENT '命名空间',
    cluster_id INTEGER COMMENT '集群ID',
    request_data TEXT COMMENT '请求数据JSON',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态: pending, approved, rejected, cancelled',
    approver_id INTEGER COMMENT '审批人ID',
    approved_at TIMESTAMP COMMENT '审批时间',
    reason TEXT COMMENT '审批理由',
    created_by INTEGER NOT NULL COMMENT '申请人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approver_id) REFERENCES users(id),
    FOREIGN KEY (cluster_id) REFERENCES clusters(id)
);
```

#### 5.3 approval_request_steps - 审批步骤表
```sql
CREATE TABLE approval_request_steps (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    approval_request_id INTEGER NOT NULL COMMENT '审批请求ID',
    step_order INTEGER NOT NULL COMMENT '步骤顺序',
    approver_id INTEGER NOT NULL COMMENT '审批人ID',
    status INTEGER DEFAULT 0 COMMENT '状态：0=待审批，1=已通过，2=已拒绝',
    approved_at TIMESTAMP COMMENT '审批时间',
    reason VARCHAR(500) COMMENT '审批理由',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (approval_request_id) REFERENCES approval_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id)
);
```

#### 6. 审计日志相关表

#### 6.1 audit_logs - 审计日志表
```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '用户名',
    user_type VARCHAR(20) COMMENT '用户类型: local, feishu',
    department VARCHAR(100) COMMENT '部门/组织',
    role VARCHAR(50) COMMENT '角色',
    action VARCHAR(50) NOT NULL COMMENT '操作类型: create, update, delete, read, approve等',
    resource_type VARCHAR(50) NOT NULL COMMENT '资源类型: user, role, cluster, pod等',
    resource_id VARCHAR(100) COMMENT '资源ID或名称',
    request_path VARCHAR(200) COMMENT 'API路径',
    request_params TEXT COMMENT '请求参数，JSON格式',
    result VARCHAR(20) COMMENT '操作结果: success, failed',
    error_message TEXT COMMENT '失败原因',
    ip VARCHAR(50) COMMENT '来源IP',
    client VARCHAR(255) COMMENT '客户端信息',
    trace_id VARCHAR(100) COMMENT '链路追踪ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### 6.2 audit_archives - 审计归档表
```sql
CREATE TABLE audit_archives (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    archive_name VARCHAR(200) NOT NULL COMMENT '归档文件名',
    quarter VARCHAR(10) NOT NULL COMMENT '归档季度，格式：2024-Q1',
    start_time TIMESTAMP NOT NULL COMMENT '归档数据开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '归档数据结束时间',
    record_count INTEGER NOT NULL COMMENT '归档记录数',
    file_size INTEGER NOT NULL COMMENT '文件大小（字节）',
    obs_key VARCHAR(500) NOT NULL COMMENT 'OBS对象键',
    status VARCHAR(20) NOT NULL COMMENT '状态: pending, processing, completed, failed',
    error_message TEXT COMMENT '错误信息',
    created_by INTEGER NOT NULL COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```



#### 7. 系统配置相关表

#### 7.1 system_configs - 系统配置表
```sql
CREATE TABLE system_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    -- OIDC配置
    oidc_enabled BOOLEAN DEFAULT FALSE,
    oidc_issuer_url VARCHAR(255),
    oidc_client_id VARCHAR(255),
    oidc_client_secret VARCHAR(255),
    oidc_redirect_uri VARCHAR(255),
    oidc_scopes VARCHAR(255),
    oidc_groups_claim VARCHAR(100),
    oidc_roles_claim VARCHAR(100),
    -- 飞书配置
    feishu_app_id VARCHAR(255),
    feishu_app_secret VARCHAR(255),
    -- OBS配置
    obs_enabled BOOLEAN DEFAULT FALSE,
    obs_endpoint VARCHAR(255),
    obs_access_key VARCHAR(255),
    obs_secret_key VARCHAR(255),
    obs_bucket VARCHAR(100),
    obs_region VARCHAR(50),
    obs_encryption_key VARCHAR(255),
    -- 审计配置
    audit_retention_days INTEGER DEFAULT 90,
    audit_archive_interval VARCHAR(20),
    audit_archive_enabled BOOLEAN DEFAULT FALSE,
    audit_encryption_enabled BOOLEAN DEFAULT FALSE,
    audit_encryption_key VARCHAR(255),
    audit_last_archive_time TIMESTAMP,
    -- 系统配置
    system_name VARCHAR(100) DEFAULT 'KubeOps',
    system_logo TEXT,
    system_contact_email VARCHAR(100),
    system_version VARCHAR(50),
    system_debug_mode BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```



#### 7.3 alerts - 告警表
```sql
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '告警标题',
    cluster_name VARCHAR(50) COMMENT '集群名称',
    namespace VARCHAR(50) COMMENT '命名空间',
    level VARCHAR(20) NOT NULL COMMENT '告警级别: critical, warning, info',
    resource VARCHAR(100) COMMENT '资源名称',
    resource_kind VARCHAR(50) COMMENT '资源类型',
    message TEXT NOT NULL COMMENT '告警消息',
    source VARCHAR(50) NOT NULL COMMENT '告警来源',
    status INTEGER DEFAULT 0 COMMENT '状态：0=未处理, 1=处理中, 2=已解决, 3=已忽略',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

### 数据模型关系说明

#### 1. 用户与权限关系
- **主要权限路径**：用户(User) → 用户组成员(UserGroupMember) → 用户组(UserGroup) → 用户组权限(GroupResourcePermission) → 资源权限(ResourcePermission)
- **直接权限路径**：用户(User) → 用户直接权限(UserResourcePermission) → 资源权限(ResourcePermission)
- Casbin策略(CasbinRule)存储最终的权限策略
- **权限设计原则**：
  - 主要通过用户组进行权限管理，便于批量管理和维护
  - 支持用户直接授权，用于特殊场景（临时权限、紧急访问等）
  - 项目成员和应用成员关系仅用于界面展示和管理便利

#### 2. 组织架构关系（支持多集群部署）
- 项目(Project) → 项目集群关联(ProjectCluster) → 集群(Cluster)
- 应用(Application) → 应用集群关联(ApplicationCluster) → 项目集群关联(ProjectCluster)
- 项目成员(ProjectMember) → 用户(User)
- 应用成员(ApplicationMember) → 用户(User)
  - **注意**：项目成员和应用成员关系仅用于界面展示，不承载实际权限控制
  - **权限控制**：通过用户组(UserGroup)授权项目/应用资源权限，用户加入成员仅为展示目的
- 集群资源(ClusterResource)关联用户组和集群资源权限

#### 多集群部署架构
- **项目多集群**：一个项目可以部署到多个集群，每个集群有独立的namespace和配置
- **应用多集群**：一个应用可以部署到项目关联的多个集群，每个集群有独立的配置（副本数、镜像、环境变量等）
- **配置隔离**：不同集群的配置完全独立，支持开发、测试、生产环境的差异化配置

#### 3. 审批流程关系
- 审批流程(ApprovalFlow) → 审批请求(ApprovalRequest) → 审批步骤(ApprovalRequestStep)

#### 4. 审计关系
- 审计日志(AuditLog)关联用户、集群、项目等资源
- 审计归档(AuditArchive)管理历史数据归档
- 审计配置统一存储在系统配置(SystemConfig)中

#### 5. GORM标签说明
- `gorm:"primaryKey"`: 主键
- `gorm:"uniqueIndex"`: 唯一索引
- `gorm:"size:100"`: 字段长度限制
- `gorm:"not null"`: 非空约束
- `gorm:"default:1"`: 默认值
- `gorm:"foreignKey:UserID"`: 外键关联
- `gorm:"index"`: 普通索引
- `json:"-"`: JSON序列化时忽略该字段
- `datatypes.JSON`: GORM的JSON数据类型

## 业务流程设计

### 1. 用户认证与授权流程

#### 1.1 本地用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 认证服务
    participant D as 数据库
    
    U->>F: 输入用户名密码
    F->>A: POST /api/auth/login
    A->>D: 验证用户凭据
    D-->>A: 返回用户信息
    A->>A: 生成JWT Token
    A->>D: 记录登录日志
    A-->>F: 返回Token和用户信息
    F-->>U: 登录成功，跳转首页
```

#### 1.2 OIDC用户登录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as 认证服务
    participant K as Keycloak
    participant D as 数据库
    
    U->>F: 点击OIDC登录
    F->>A: GET /api/auth/oidc/login
    A-->>F: 重定向到Keycloak
    F-->>K: 用户认证
    K-->>A: 回调携带授权码
    A->>K: 交换访问令牌
    K-->>A: 返回用户信息
    A->>D: 查询/创建本地用户
    A->>D: 处理用户组映射
    A->>A: 生成JWT Token
    A-->>F: 返回Token和用户信息
```

#### 1.3 权限检查流程
```mermaid
sequenceDiagram
    participant F as 前端
    participant A as API服务
    participant R as RBAC服务
    participant C as Casbin
    participant D as 数据库
    
    F->>A: API请求（携带Token）
    A->>A: 验证Token
    A->>R: 检查权限(user, domain, resource, action)
    R->>C: Casbin.Enforce()
    C->>D: 查询策略规则
    D-->>C: 返回策略数据
    C-->>R: 返回权限结果
    alt 有权限
        R-->>A: 允许访问
        A->>A: 执行业务逻辑
        A-->>F: 返回结果
    else 无权限
        R-->>A: 拒绝访问
        A-->>F: 返回403错误
    end
```

### 2. 集群与项目管理流程

#### 2.1 集群注册流程
```mermaid
sequenceDiagram
    participant U as 管理员
    participant A as API服务
    participant K as K8s客户端
    participant D as 数据库
    participant R as RBAC服务
    
    U->>A: 提交集群配置
    A->>A: 验证kubeconfig
    A->>K: 测试集群连接
    K-->>A: 返回集群信息
    A->>D: 保存集群信息
    A->>R: 创建集群级用户组
    R->>D: 保存用户组和权限
    A-->>U: 集群注册成功
```

#### 2.2 项目创建流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as API服务
    participant K as K8s客户端
    participant D as 数据库
    participant R as RBAC服务
    
    U->>A: 创建项目请求
    A->>A: 验证项目权限
    A->>D: 保存项目信息
    A->>R: 创建项目级用户组
    R->>R: 设置项目权限策略
    A->>R: 将创建者加入项目管理员组
    loop 为每个选择的集群
        A->>K: 创建namespace
        K-->>A: namespace创建成功
        A->>D: 保存项目集群关联
    end
    A-->>U: 项目创建成功
```

### 3. 工作负载管理流程

#### 3.1 工作负载创建流程
```mermaid
sequenceDiagram
    participant U as 开发者
    participant A as API服务
    participant K as K8s客户端
    participant D as 数据库
    participant R as RBAC服务
    
    U->>A: 创建工作负载请求
    A->>A: 验证项目权限
    A->>D: 保存工作负载信息
    A->>R: 创建工作负载级用户组
    A->>R: 将创建者设为owner
    A->>K: 创建K8s资源
    K-->>A: 资源创建成功
    A->>D: 更新工作负载状态
    A-->>U: 工作负载创建成功
```

#### 3.2 权限分配流程（支持K8s权限同步）

##### 3.2.1 平台权限分配流程
```mermaid
sequenceDiagram
    participant A as 管理员
    participant API as API服务
    participant R as RBAC服务
    participant D as 数据库
    participant C as Casbin

    A->>API: 分配用户组权限
    API->>API: 验证管理员权限
    API->>R: 处理权限分配
    R->>D: 保存权限关系
    R->>C: 更新Casbin策略
    C->>D: 持久化策略规则
    API-->>A: 权限分配成功
```

##### 3.2.2 K8s权限同步流程（支持用户和用户组授权）
```mermaid
sequenceDiagram
    participant A as 管理员
    participant API as API服务
    participant R as RBAC服务
    participant K8S as K8s客户端
    participant KC as K8s集群
    participant D as 数据库

    A->>API: 分配K8s资源权限
    API->>API: 验证权限类型为k8s
    API->>R: 处理K8s权限分配
    R->>D: 保存平台权限关系

    Note over R,K8S: 同步K8s RBAC权限（支持用户和用户组）
    R->>K8S: 解析权限路径和授权对象
    K8S->>K8S: 判断授权类型（用户/用户组）

    loop 为每个相关集群
        alt 用户组授权
            K8S->>KC: 创建/更新Role/ClusterRole
            K8S->>KC: 创建/更新RoleBinding/ClusterRoleBinding (subject: Group)
        else 用户授权
            K8S->>KC: 创建/更新Role/ClusterRole
            K8S->>KC: 创建/更新RoleBinding/ClusterRoleBinding (subject: User)
        end
        KC-->>K8S: 同步成功
    end

    K8S-->>R: K8s权限同步完成
    R-->>API: 权限分配完成
    API-->>A: 分配成功（平台+K8s双重权限）
```

##### 3.2.3 权限分配详细流程
```mermaid
flowchart TD
    A[权限分配请求] --> B{检查权限类型}

    B -->|API权限| C[更新平台权限]
    B -->|UI权限| C
    B -->|K8s权限| D[更新平台权限 + K8s同步]

    C --> E[保存到数据库]
    E --> F[更新Casbin策略]
    F --> G[权限分配完成]

    D --> H[保存到数据库]
    H --> I[更新Casbin策略]
    I --> J{解析权限路径}

    J --> K[提取集群信息]
    K --> L[提取命名空间信息]
    L --> M[提取资源类型]
    M --> N[提取操作权限]

    N --> O{权限范围判断}
    O -->|集群级权限| P[创建ClusterRole + ClusterRoleBinding]
    O -->|命名空间级权限| Q[创建Role + RoleBinding]

    P --> R[同步到目标集群]
    Q --> R
    R --> S{同步结果}
    S -->|成功| T[记录同步状态]
    S -->|失败| U[记录错误信息]
    T --> V[权限分配完成]
    U --> W[部分失败，需要重试]
```

#### 3.2.4 K8s权限同步实现细节

##### 权限路径解析规则
```
权限路径格式：cluster:{cluster_name}:project:{project_name}:application:{app_name}:k8s:{resource_type}

解析示例：
cluster:prod-cluster:project:web-project:application:web-frontend:k8s:pods
├── 集群：prod-cluster
├── 命名空间：web-project (项目名称对应命名空间)
├── 应用：web-frontend (可选，用于标签选择器)
└── 资源：pods

cluster:*:project:web-project:k8s:deployments
├── 集群：所有集群
├── 命名空间：web-project
└── 资源：deployments
```

##### K8s RBAC资源生成规则（支持用户和用户组）
```yaml
# 1. Role/ClusterRole（根据权限范围）
apiVersion: rbac.authorization.k8s.io/v1
kind: Role  # 或 ClusterRole
metadata:
  name: kubeops-{subject_type}-{subject_name}-{namespace}  # 或 kubeops-{subject_type}-{subject_name}-cluster
  namespace: {namespace}  # ClusterRole时省略
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "{subject_type}"  # user 或 group
    kubeops.io/subject-name: "{subject_name}"
rules:
- apiGroups: ["", "apps", "extensions"]
  resources: ["{resource_type}"]
  verbs: ["{actions}"]  # get, list, create, update, delete等
  resourceNames: []  # 可选：特定资源名称限制

---
# 2. RoleBinding/ClusterRoleBinding（用户组授权）
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding  # 或 ClusterRoleBinding
metadata:
  name: kubeops-group-{group_name}-{namespace}
  namespace: {namespace}  # ClusterRoleBinding时省略
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "group"
    kubeops.io/subject-name: "{group_name}"
subjects:
- kind: Group
  name: "kubeops:group:{group_name}"  # 用于impersonate的组名
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role  # 或 ClusterRole
  name: kubeops-group-{group_name}-{namespace}
  apiGroup: rbac.authorization.k8s.io

---
# 3. RoleBinding/ClusterRoleBinding（用户授权）
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding  # 或 ClusterRoleBinding
metadata:
  name: kubeops-user-{username}-{namespace}
  namespace: {namespace}  # ClusterRoleBinding时省略
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "user"
    kubeops.io/subject-name: "{username}"
subjects:
- kind: User
  name: "kubeops:user:{username}"  # 用于impersonate的用户名
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role  # 或 ClusterRole
  name: kubeops-user-{username}-{namespace}
  apiGroup: rbac.authorization.k8s.io
```

##### K8s Impersonate特性应用
```
1. 集群配置：
   - 平台使用一个高权限的kubeconfig（具有impersonate权限）
   - 通过impersonate模拟不同用户/用户组身份访问K8s资源
   - 无需为每个用户/用户组创建独立的kubeconfig

2. Impersonate身份映射：
   用户授权：kubeops:user:{username}
   用户组授权：kubeops:group:{group_name}

3. WebShell身份选择：
   - 用户可选择以个人身份或用户组身份访问
   - 支持多重身份切换
   - 实时权限验证

4. 权限验证流程：
   平台权限检查 → Impersonate身份设置 → K8s RBAC验证 → 资源访问
```

##### 权限同步策略
```
1. 权限聚合策略：
   - 同一用户/用户组在同一命名空间的多个权限会聚合到一个Role中
   - 避免创建过多的RBAC资源
   - 支持用户和用户组权限的独立管理

2. 权限更新策略：
   - 增量更新：只更新变化的权限
   - 全量同步：定期全量同步确保一致性
   - 支持用户和用户组权限的分别同步

3. 权限清理策略：
   - 用户/用户组删除时，清理对应的K8s RBAC资源
   - 权限撤销时，更新对应的Role规则
   - 自动清理孤立的RBAC资源

4. 错误处理策略：
   - 同步失败时记录错误日志
   - 支持手动重试同步
   - 提供权限同步状态查询接口
   - 区分用户和用户组的同步状态
```

### 4. 审批流程

#### 4.1 审批流程配置
```mermaid
sequenceDiagram
    participant A as 管理员
    participant S as API服务
    participant D as 数据库
    
    A->>S: 配置审批流程
    S->>S: 验证管理员权限
    S->>D: 保存流程配置
    S-->>A: 配置保存成功
```

#### 4.2 审批请求处理流程
```mermaid
sequenceDiagram
    participant U as 申请人
    participant A as API服务
    participant D as 数据库
    participant P as 审批人
    participant N as 通知服务
    
    U->>A: 提交操作请求
    A->>A: 检查是否需要审批
    A->>D: 创建审批请求
    A->>D: 创建审批步骤
    A->>N: 发送审批通知
    N-->>P: 通知待审批
    P->>A: 审批操作
    A->>D: 更新审批状态
    alt 审批通过
        A->>A: 执行原始操作
        A->>N: 通知申请人
    else 审批拒绝
        A->>N: 通知申请人
    end
```

### 5. 审计日志流程

#### 5.1 审计日志记录流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant M as 中间件
    participant A as API服务
    participant L as 审计服务
    participant D as 数据库
    
    U->>M: API请求
    M->>M: 记录请求信息
    M->>A: 转发请求
    A->>A: 处理业务逻辑
    A-->>M: 返回响应
    M->>L: 异步记录审计日志
    L->>D: 保存审计记录
    M-->>U: 返回响应
```

#### 5.2 审计日志归档流程
```mermaid
sequenceDiagram
    participant S as 定时任务
    participant A as 归档服务
    participant D as 数据库
    participant O as 对象存储
    
    S->>A: 触发归档任务
    A->>D: 查询过期日志
    A->>A: 生成归档文件
    A->>O: 上传到对象存储
    O-->>A: 上传成功
    A->>D: 记录归档信息
    A->>D: 删除过期日志
    A-->>S: 归档完成
```

### 6. 权限继承示例

#### 6.1 多级权限检查（层次化权限模型）
```
用户请求：重启应用web-frontend在生产集群的deployment
权限检查路径（按优先级从高到低）：

1. 应用级权限：cluster:prod-cluster:project:web-project:application:web-frontend:k8s:deployments (action: restart)
2. 项目级权限：cluster:prod-cluster:project:web-project:k8s:deployments (action: restart)
3. 集群级权限：cluster:prod-cluster:k8s:deployments (action: restart)
4. 系统级权限：system:k8s:deployments (action: restart)

匹配到任一权限即允许访问，体现了权限的层次化继承
```

#### 6.2 用户组权限示例（层次化权限模型）
```
系统管理员组：system-admin
- 权限：(*, *, *)
- 成员：alice

集群管理员组：prod-cluster-admin
- 权限：(*, cluster:prod-cluster:*, *)
- 成员：bob

项目管理员组：web-project-admin
- 权限：(api, cluster:*:project:web-project:*, *)
- 权限：(k8s, cluster:*:project:web-project:*, *)
- 成员：charlie

项目生产环境管理员组：web-project-prod-admin
- 权限：(k8s, cluster:prod-cluster:project:web-project:*, *)
- 成员：david

应用负责人组：web-frontend-owner
- 权限：(k8s, cluster:*:project:web-project:application:web-frontend:*, *)
- 成员：eve

应用生产环境负责人组：web-frontend-prod-owner
- 权限：(k8s, cluster:prod-cluster:project:web-project:application:web-frontend:*, create,update,delete,restart)
- 成员：frank

应用开发者组：web-frontend-developer
- 权限：(k8s, cluster:dev-cluster:project:web-project:application:web-frontend:*, get,list,watch)
- 权限：(k8s, cluster:dev-cluster:project:web-project:application:web-frontend:pods, logs,exec)
- 成员：grace

应用只读组：web-frontend-viewer
- 权限：(k8s, cluster:*:project:web-project:application:web-frontend:*, get,list,watch)
- 成员：henry
```

#### 6.3 层次化权限管理示例
```
场景1：给用户组 "web-project-admin" 授权项目 "web-project" 的完全管理权限（所有集群）

权限配置：
- ResourcePermission: (api, cluster:*:project:web-project:*, *)
- ResourcePermission: (k8s, cluster:*:project:web-project:*, *)

结果：
- 权限控制：charlie 可以管理 web-project 在所有集群的 API 和 K8s 资源
- 界面展示：charlie 在项目 "web-project" 的成员列表中显示为 "admin" 角色

场景2：给用户组 "web-frontend-prod-owner" 授权应用 "web-frontend" 在生产集群的管理权限

权限配置：
- ResourcePermission: (k8s, cluster:prod-cluster:project:web-project:application:web-frontend:*, create,update,delete,restart)

结果：
- 权限控制：frank 只能管理 web-frontend 在生产集群的 K8s 资源
- 界面展示：frank 在应用 "web-frontend" 的成员列表中显示为 "owner" 角色

场景3：给用户组 "web-frontend-developer" 授权应用 "web-frontend" 在开发集群的开发权限

权限配置：
- ResourcePermission: (k8s, cluster:dev-cluster:project:web-project:application:web-frontend:*, get,list,watch)
- ResourcePermission: (k8s, cluster:dev-cluster:project:web-project:application:web-frontend:pods, logs,exec)

结果：
- 权限控制：grace 可以查看开发集群的应用资源，并可以查看日志和执行命令
- 界面展示：grace 在应用 "web-frontend" 的成员列表中显示为 "developer" 角色

场景4：权限继承示例

用户 henry 属于 "web-frontend-viewer" 组，请求查看生产集群的 web-frontend 应用的 pods：

权限检查顺序：
1. 检查应用级权限：cluster:prod-cluster:project:web-project:application:web-frontend:pods (action: get)
   - 匹配到：(k8s, cluster:*:project:web-project:application:web-frontend:*, get,list,watch) ✓
2. 权限检查通过，允许访问

层次化权限特点：
- 精细化控制：可以精确到具体应用的具体资源和操作
- 权限继承：应用级 > 项目级 > 集群级 > 系统级
- 双重授权：支持用户组权限和用户直接权限两种方式

#### 6.4 用户直接授权示例
```
场景：紧急情况下，需要给开发者 david 临时授权生产集群的 pod 执行权限

直接授权配置：
- UserResourcePermission:
  - UserID: david_id
  - ResourcePermission: (k8s, cluster:prod-cluster:project:web-project:application:web-frontend:pods, exec)
  - GrantedBy: admin_id
  - GrantReason: "生产环境紧急调试，临时授权24小时"
  - ExpiresAt: "2024-01-15 18:00:00"

Casbin 策略：
p, david, k8s, cluster:prod-cluster:project:web-project:application:web-frontend:pods, exec

权限检查：
当 david 请求执行 pod 命令时：
1. 检查用户直接权限：匹配到临时授权 ✓
2. 检查权限是否过期：未过期 ✓
3. 允许访问

用户直接授权的适用场景：
- 紧急访问：生产环境故障需要临时权限
- 临时权限：短期项目需要特殊权限
- 个人权限：某些用户需要特殊的个性化权限
- 权限测试：测试特定权限配置
- 审计要求：需要明确记录特殊权限的授权原因和时间
```
- 配置隔离：不同集群的配置完全独立，支持差异化部署
- 成员展示：成员角色仅用于界面展示，实际权限基于用户组权限

## 权限模型最佳实践

### 1. 层次化权限设计

#### 权限层次结构
```
系统级 (System Level)
├── API权限：用户管理、集群管理、系统配置等
└── 集群级 (Cluster Level)
    └── 项目级 (Project/Namespace Level)
        ├── 项目资源权限：namespace管理、资源配额等
        └── 应用级 (Application/Workload Level)
            └── 应用资源权限：deployment、pod、service等
```

#### 资源路径设计规范
```
# 系统级资源
system:api:users                    # 用户管理API
system:api:clusters                 # 集群管理API
system:api:configs                  # 系统配置API
system:api:audit                    # 审计日志API

# 集群级资源
cluster:{cluster_name}:api:*         # 集群管理API
cluster:{cluster_name}:k8s:*         # 集群K8s资源

# 项目级资源
cluster:{cluster_name}:project:{project_name}:api:*                    # 项目管理API
cluster:{cluster_name}:project:{project_name}:k8s:*                    # 项目K8s资源
cluster:{cluster_name}:project:{project_name}:k8s:pods                 # 项目Pod资源
cluster:{cluster_name}:project:{project_name}:k8s:services             # 项目Service资源

# 应用级资源
cluster:{cluster_name}:project:{project_name}:application:{app_name}:k8s:deployments  # 应用Deployment
cluster:{cluster_name}:project:{project_name}:application:{app_name}:k8s:pods         # 应用Pod
cluster:{cluster_name}:project:{project_name}:application:{app_name}:k8s:services     # 应用Service
```

### 2. K8s权限同步最佳实践

#### 2.1 权限同步策略
```
1. 双重权限控制：
   - 平台层权限：通过Casbin进行API访问控制
   - K8s层权限：通过K8s RBAC进行资源访问控制
   - 确保两层权限的一致性

2. 权限粒度控制：
   - 集群级权限：使用ClusterRole和ClusterRoleBinding
   - 命名空间级权限：使用Role和RoleBinding
   - 资源级权限：通过resourceNames限制特定资源

3. 权限聚合优化：
   - 同一用户组在同一命名空间的权限聚合到一个Role
   - 避免创建过多的RBAC资源
   - 定期清理无用的权限资源
```

#### 2.2 命名规范
```
ServiceAccount命名：kubeops-group-{group_name}
Role命名：kubeops-{group_name}-{namespace}
ClusterRole命名：kubeops-{group_name}-cluster
RoleBinding命名：kubeops-{group_name}-{namespace}
ClusterRoleBinding命名：kubeops-{group_name}-cluster

标签规范：
- kubeops.io/managed: "true"           # 标识由KubeOps管理
- kubeops.io/group: "{group_name}"     # 关联的用户组
- kubeops.io/sync-time: "{timestamp}"  # 同步时间
```

#### 2.3 权限同步监控
```
1. 同步状态监控：
   - 记录每次同步的结果和耗时
   - 监控同步失败率和重试次数
   - 提供同步状态查询接口

2. 权限一致性检查：
   - 定期检查平台权限与K8s权限的一致性
   - 发现不一致时自动修复或告警
   - 提供手动同步和强制重建功能

3. 错误处理机制：
   - 同步失败时记录详细错误信息
   - 支持自动重试和手动重试
   - 提供权限清理和回滚功能
```

#### 2.4 安全考虑
```
1. 最小权限原则：
   - 只授予必要的权限，避免过度授权
   - 定期审查和清理不必要的权限
   - 使用resourceNames限制特定资源访问

2. 权限隔离：
   - 不同项目使用不同的命名空间
   - 应用级权限通过标签选择器隔离
   - 避免跨项目的权限泄露

3. 审计和监控：
   - 记录所有权限变更操作
   - 监控异常的权限使用行为
   - 定期生成权限使用报告
```

### 3. Casbin 模型（支持用户直接授权）

```ini
[request_definition]
r = sub, resource_type, resource_path, action

[policy_definition]
p = sub, resource_type, resource_path, action
p2 = group, resource_type, resource_path, action

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = (r.sub == p.sub && (p.resource_type == "*" || r.resource_type == p.resource_type) && resourcePathMatch(r.resource_path, p.resource_path) && (p.action == "*" || actionMatch(r.action, p.action))) || (g(r.sub, p2.group) && (p2.resource_type == "*" || r.resource_type == p2.resource_type) && resourcePathMatch(r.resource_path, p2.resource_path) && (p2.action == "*" || actionMatch(r.action, p2.action)))
```

### 3. 权限策略示例

```
# 用户组权限策略 (p2)
p2, system-admin, *, *, *
p2, cluster-admin-prod, *, cluster:prod-cluster:*, *
p2, project-admin-web, *, cluster:*:project:web-project:*, *
p2, app-owner-frontend, *, cluster:*:project:web-project:application:web-frontend:*, *
p2, developer-frontend, k8s, cluster:*:project:web-project:application:web-frontend:*, get,list,watch
p2, ops-web-project, k8s, cluster:prod-cluster:project:web-project:*, get,list,watch,update

# 用户直接权限策略 (p) - 用于特殊授权场景
p, alice, *, *, *                                                                    # 临时系统管理员权限
p, bob, k8s, cluster:prod-cluster:project:emergency:*, *                            # 紧急项目访问权限
p, charlie, api, system:api:users, get,list                                         # 临时用户查看权限
p, david, k8s, cluster:*:project:web-project:application:web-frontend:pods, exec   # 临时调试权限

# 用户组成员关系 (g)
g, alice, system-admin
g, bob, cluster-admin-prod
g, charlie, project-admin-web
g, david, developer-frontend
```

### 4. 权限检查流程

```
用户请求：查看 prod-cluster 集群 web-project 项目下 web-frontend 应用的 pods

Casbin 请求格式：
Request: (user, k8s, cluster:prod-cluster:project:web-project:application:web-frontend:pods, get)

权限检查顺序（通过 resourcePathMatch 函数实现）：
1. cluster:prod-cluster:project:web-project:application:web-frontend:pods (应用级 - 最具体)
2. cluster:prod-cluster:project:web-project:application:web-frontend:* (应用级 - 通配符)
3. cluster:prod-cluster:project:web-project:* (项目级)
4. cluster:prod-cluster:* (集群级)
5. * (系统级)

匹配到任一权限即允许访问，体现了权限的层次化继承
```
```

#### 6.4 多集群部署数据关系示例
```
项目：web-project
├── 开发集群部署 (project_clusters)
│   ├── cluster_id: 1 (dev-cluster)
│   ├── namespace: web-project-dev
│   └── config: {"resource_quota": "small"}
│
├── 生产集群部署 (project_clusters)
│   ├── cluster_id: 2 (prod-cluster)
│   ├── namespace: web-project-prod
│   └── config: {"resource_quota": "large", "backup_enabled": true}
│
└── 应用：web-frontend
    ├── 开发集群配置 (application_clusters)
    │   ├── project_cluster_id: 1
    │   ├── replicas: 1
    │   ├── image: "web-frontend:dev"
    │   └── env_vars: {"ENV": "development"}
    │
    └── 生产集群配置 (application_clusters)
        ├── project_cluster_id: 2
        ├── replicas: 3
        ├── image: "web-frontend:v1.2.0"
        └── env_vars: {"ENV": "production"}
```

## API 响应规范

为方便前后端协作、日志追踪与错误排查，所有 API 接口均采用统一的响应格式：

```json
{
  "code": 20000,           // 业务状态码
  "message": "成功",        // 状态说明
  "data": {},              // 业务数据
  "trace_id": "abc123",    // 追踪ID
  "timestamp": 1622541234  // 响应时间戳
}
```

### 状态码说明

| 状态码范围  | 含义       | 示例                                  |
|------------|------------|--------------------------------------|
| 40000-40099| 参数错误   | 40000: 参数错误                      |
| 40100-40199| 未授权     | 40100: 未授权或登录失效               |
| 40300-40399| 禁止访问   | 40300: 禁止访问                      |
| 40400-40499| 资源不存在 | 40400: 资源不存在                    |
| 50000-50099| 内部错误   | 50000: 系统内部错误                  |
| 50300-50399| 服务不可用 | 50300: 服务不可用                    |

### HTTP 状态码

绝大部分响应使用 HTTP 200 状态码，但在以下情况会使用特定的 HTTP 状态码：

- 401 Unauthorized: 未授权（如无效 token）
- 403 Forbidden: 禁止访问（无权限）
- 404 Not Found: 资源不存在
- 400 Bad Request: 请求参数错误
- 500 Internal Server Error: 服务端错误

### 使用示例

```go
// 成功响应
response.Success(c, data)

// 自定义消息的成功响应
response.Success(c, data, response.WithMessage("自定义成功消息"))

// 错误响应
response.BadRequest(c, "参数无效")
response.Unauthorized(c, "未登录或登录失效")
response.Forbidden(c, "无权限访问")
response.NotFound(c, "资源不存在")
response.ServerError(c, err)
```

## K8s权限同步API接口

### 权限分配接口（支持用户和用户组K8s同步）

#### 分配用户组权限
```http
POST /api/v1/usergroups/{id}/permissions
Content-Type: application/json

{
  "permission_ids": [1, 2, 3],
  "sync_to_k8s": true  // 是否同步到K8s集群
}
```

#### 分配用户权限
```http
POST /api/v1/users/{id}/permissions
Content-Type: application/json

{
  "permission_ids": [1, 2, 3],
  "sync_to_k8s": true  // 是否同步到K8s集群
}
```

**响应示例（用户组K8s权限同步）：**
```json
{
  "code": 20000,
  "message": "用户组权限分配成功，K8s权限同步完成",
  "data": {
    "subject_type": "group",
    "subject_name": "web-frontend-team",
    "platform_permissions": [
      {
        "id": 1,
        "resource_type": "k8s",
        "resource_path": "cluster:prod-cluster:project:web-project:k8s:pods",
        "actions": ["get", "list", "watch"]
      }
    ],
    "k8s_sync_results": [
      {
        "cluster_name": "prod-cluster",
        "namespace": "web-project",
        "status": "success",
        "resources_created": [
          "Role/kubeops-group-web-frontend-team-web-project",
          "RoleBinding/kubeops-group-web-frontend-team-web-project"
        ],
        "impersonate_identity": "kubeops:group:web-frontend-team"
      }
    ]
  }
}
```

**响应示例（用户K8s权限同步）：**
```json
{
  "code": 20000,
  "message": "用户权限分配成功，K8s权限同步完成",
  "data": {
    "subject_type": "user",
    "subject_name": "zhangsan",
    "platform_permissions": [
      {
        "id": 2,
        "resource_type": "k8s",
        "resource_path": "cluster:dev-cluster:project:test-project:k8s:deployments",
        "actions": ["get", "list", "create", "update"]
      }
    ],
    "k8s_sync_results": [
      {
        "cluster_name": "dev-cluster",
        "namespace": "test-project",
        "status": "success",
        "resources_created": [
          "Role/kubeops-user-zhangsan-test-project",
          "RoleBinding/kubeops-user-zhangsan-test-project"
        ],
        "impersonate_identity": "kubeops:user:zhangsan"
      }
    ]
  }
}
```

### WebShell身份选择接口

#### 获取用户可用身份列表
```http
GET /api/v1/webshell/identities?cluster_name=prod-cluster
```

**响应示例：**
```json
{
  "code": 20000,
  "message": "查询成功",
  "data": {
    "user_identity": {
      "type": "user",
      "name": "zhangsan",
      "display_name": "张三",
      "impersonate_identity": "kubeops:user:zhangsan",
      "permissions": [
        {
          "cluster_name": "dev-cluster",
          "namespace": "test-project",
          "resources": ["deployments"],
          "actions": ["get", "list", "create", "update"]
        }
      ]
    },
    "group_identities": [
      {
        "type": "group",
        "name": "web-frontend-team",
        "display_name": "前端开发团队",
        "impersonate_identity": "kubeops:group:web-frontend-team",
        "permissions": [
          {
            "cluster_name": "prod-cluster",
            "namespace": "web-project",
            "resources": ["pods"],
            "actions": ["get", "list", "watch"]
          }
        ]
      },
      {
        "type": "group",
        "name": "ops-team",
        "display_name": "运维团队",
        "impersonate_identity": "kubeops:group:ops-team",
        "permissions": [
          {
            "cluster_name": "prod-cluster",
            "namespace": "*",
            "resources": ["*"],
            "actions": ["*"]
          }
        ]
      }
    ]
  }
}
```

#### 连接WebShell
```http
POST /api/v1/webshell/connect
Content-Type: application/json

{
  "cluster_name": "prod-cluster",
  "namespace": "web-project",
  "identity_type": "group",  // "user" 或 "group"
  "identity_name": "web-frontend-team",
  "session_timeout": 3600  // 会话超时时间（秒）
}
```

**响应示例：**
```json
{
  "code": 20000,
  "message": "WebShell连接成功",
  "data": {
    "session_id": "ws-session-123456",
    "websocket_url": "wss://kubeops.example.com/api/v1/webshell/ws/ws-session-123456",
    "identity": {
      "type": "group",
      "name": "web-frontend-team",
      "impersonate_identity": "kubeops:group:web-frontend-team"
    },
    "cluster_info": {
      "name": "prod-cluster",
      "namespace": "web-project"
    },
    "session_timeout": 3600,
    "available_commands": [
      "kubectl get pods",
      "kubectl describe pod",
      "kubectl logs"
    ]
  }
}
```

### K8s权限同步管理接口

#### 查询权限同步状态
```http
# 查询用户组权限同步状态
GET /api/v1/rbac/k8s-sync/status?subject_type=group&subject_id=1&cluster_name=prod-cluster

# 查询用户权限同步状态
GET /api/v1/rbac/k8s-sync/status?subject_type=user&subject_id=123&cluster_name=dev-cluster
```

**响应示例（用户组）：**
```json
{
  "code": 20000,
  "message": "查询成功",
  "data": {
    "subject_type": "group",
    "subject_id": 1,
    "subject_name": "web-frontend-team",
    "impersonate_identity": "kubeops:group:web-frontend-team",
    "sync_status": [
      {
        "cluster_name": "prod-cluster",
        "namespace": "web-project",
        "last_sync_time": "2024-01-15T10:30:00Z",
        "status": "success",
        "error_message": null,
        "k8s_resources": [
          {
            "type": "Role",
            "name": "kubeops-group-web-frontend-team-web-project",
            "namespace": "web-project",
            "status": "active"
          },
          {
            "type": "RoleBinding",
            "name": "kubeops-group-web-frontend-team-web-project",
            "namespace": "web-project",
            "status": "active"
          }
        ]
      }
    ]
  }
}
```

**响应示例（用户）：**
```json
{
  "code": 20000,
  "message": "查询成功",
  "data": {
    "subject_type": "user",
    "subject_id": 123,
    "subject_name": "zhangsan",
    "impersonate_identity": "kubeops:user:zhangsan",
    "sync_status": [
      {
        "cluster_name": "dev-cluster",
        "namespace": "test-project",
        "last_sync_time": "2024-01-15T11:00:00Z",
        "status": "success",
        "error_message": null,
        "k8s_resources": [
          {
            "type": "Role",
            "name": "kubeops-user-zhangsan-test-project",
            "namespace": "test-project",
            "status": "active"
          },
          {
            "type": "RoleBinding",
            "name": "kubeops-user-zhangsan-test-project",
            "namespace": "test-project",
            "status": "active"
          }
        ]
      }
    ]
  }
}
```

#### 手动重试权限同步
```http
POST /api/v1/rbac/k8s-sync/retry
Content-Type: application/json

{
  "subject_type": "group",  // "user" 或 "group"
  "subject_id": 1,
  "cluster_names": ["prod-cluster", "dev-cluster"],
  "force_recreate": false  // 是否强制重新创建K8s资源
}
```

#### 查看同步错误日志
```http
GET /api/v1/rbac/k8s-sync/errors?subject_type=group&subject_id=1&start_time=2024-01-01&end_time=2024-01-31
```

**响应示例：**
```json
{
  "code": 20000,
  "message": "查询成功",
  "data": {
    "errors": [
      {
        "id": 1,
        "subject_type": "group",
        "subject_id": 1,
        "subject_name": "web-frontend-team",
        "cluster_name": "prod-cluster",
        "error_type": "k8s_api_error",
        "error_message": "failed to create Role: forbidden",
        "occurred_at": "2024-01-15T09:15:00Z",
        "resolved": false,
        "retry_count": 3,
        "impersonate_identity": "kubeops:group:web-frontend-team"
      }
    ],
    "total": 1
  }
}
```

#### 清理K8s权限资源
```http
DELETE /api/v1/rbac/k8s-sync/cleanup
Content-Type: application/json

{
  "subject_type": "group",  // "user" 或 "group"
  "subject_id": 1,
  "cluster_names": ["prod-cluster"],
  "dry_run": true  // 是否为预演模式
}
```

## 快速开始

### 前置条件

- Go 1.18+
- 配置 Kubernetes 集群的 kubeconfig

### 安装和运行

1. 克隆仓库

```bash
git clone https://github.com/yourusername/kubeops.git
cd kubeops
```

2. 安装依赖

```bash
go mod download
```

3. 编译

```bash
make build
```

## 代码设计

项目遵循清晰架构（Clean Architecture）的设计原则：

- **领域层**：定义核心业务实体（model 包）
- **用例层**：实现业务逻辑（service 包）
- **接口适配层**：处理外部请求（api 包）和数据存储（repository 包）
- **基础设施层**：提供技术支持（pkg 目录）

依赖关系从外向内，内层不依赖外层。

## UI菜单栏

```ini
kubernetes平台管理
├── 集群管理
├── 项目管理
├── 应用管理           # 混合模式：手动创建 + K8s发现
│   ├── 应用列表       # 显示所有应用，支持状态筛选，可以直接修改应用元数据
│   ├── 创建应用       # 手动创建应用
├── 工作负载管理       # 直接展示K8s原生工作负载
├── 服务与路由
├── 存储
└── 配置与密钥

审批中心
├── 我的申请        # 记录用户发起的请求
├── 我的审批（可选） 
├── 审批模板配置    # 哪些系统操作 → 对应审批流程
└── 审批历史        # 查看审批结果

审计管理
├── 操作日志
├── 归档管理
└── 统计报表

系统管理
└── 系统配置

身份认证
├── 用户管理
├── 用户组管理
└── 权限管理
     ├── 权限资源（列出所有权限）
     ├── 授权管理（列出权限分配给了哪些角色，也可以直接分配资源给用户组或者用户）
     │   ├── 平台权限分配（API、UI权限）
     │   └── K8s权限分配（自动同步到K8s集群RBAC）
     ├── 权限检查
     │   ├── 平台权限检查（基于Casbin）
     │   └── K8s权限验证（集群内RBAC验证）
     └── K8s权限同步
         ├── 权限同步状态查询
         ├── 手动重试同步
         └── 同步错误日志查看
```

## 应用管理功能

### 1. 混合模式应用管理

系统采用混合模式的应用管理策略，既支持手动创建应用，也支持从 K8s 集群自动发现应用。这种设计兼顾了完整的应用生命周期管理和现有集群的兼容性。

#### 1.1 设计理念
- **Application 作为业务抽象层**：存储应用的业务属性（Owner、Git仓库、CICD配置等）
- **ApplicationCluster 作为运行时层**：存储应用在各集群的运行时配置和状态
- **支持两种创建方式**：手动创建（完整业务信息）+ 自动发现（运行时信息）
- **渐进式完善**：从K8s发现的应用可以逐步补充业务信息

#### 1.2 应用状态分类
- **完整应用**：有完整业务信息的应用（`is_complete=true`，界面显示为绿色/正常状态）
- **待完善应用**：从K8s发现但缺少业务信息的应用（`source=discovered, is_complete=false`，界面显示为橙色/警告状态）
- **孤儿应用**：有Application记录但K8s中不存在的应用（界面显示为红色/错误状态）

### 2. 应用创建流程

#### 2.1 手动创建应用
```
1. 用户填写应用基本信息（名称、Owner、Git仓库等）
2. 创建 Application 记录（source='manual', is_complete=true）
3. 用户选择部署到哪些集群
4. 配置各集群的部署参数
5. 执行部署操作
6. 创建 ApplicationCluster 记录
```

#### 2.2 从K8s发现应用
```
1. 扫描 K8s 集群中的工作负载
2. 检查是否已存在对应的 Application 记录
3. 如果不存在，创建 Application 记录（source='discovered', is_complete=false）
4. 创建或更新 ApplicationCluster 记录
5. 在界面上标记为"待完善"状态
6. 用户后续补充业务信息，完善后设置 is_complete=true
```

### 3. K8s 应用发现接口

为了兼容现有 K8s 集群，系统提供了应用发现接口，可以自动发现和同步 K8s 工作负载到数据库中。

#### 3.1 发现接口
```http
POST /api/v1/applications/discover
Content-Type: application/json

{
  "cluster_id": 1,                    // 目标集群ID
  "project_id": 2,                    // 目标项目ID
  "namespace": "web-project-prod",    // 要发现的命名空间
  "workload_types": [                 // 要发现的工作负载类型
    "deployment",
    "statefulset",
    "daemonset"
  ],
  "auto_create_project_cluster": true // 是否自动创建项目集群关联
}
```

#### 3.2 发现响应格式
```json
{
  "code": 20000,
  "message": "发现成功",
  "data": {
    "discover_summary": {
      "total_discovered": 15,         // 发现的工作负载总数
      "created": 12,                  // 新创建的应用数
      "updated": 2,                   // 更新的应用数
      "skipped": 1,                   // 跳过的应用数（已存在）
      "incomplete": 12                // 待完善的应用数
    },
    "discover_details": [
      {
        "k8s_name": "web-frontend",
        "k8s_type": "deployment",
        "action": "created",
        "application_id": 123,
        "is_complete": false,
        "message": "成功创建应用，待完善业务信息"
      }
    ]
  }
}
```

### 4. 应用完善接口

#### 4.1 完善应用信息
```http
PUT /api/v1/applications/{id}/complete
Content-Type: application/json

{
  "display_name": "前端应用",
  "description": "公司官网前端应用",
  "git_repo_url": "https://github.com/company/web-frontend",
  "git_branch": "main",
  "language": "JavaScript",
  "framework": "Vue.js",
  "owner_id": 123
}
```

#### 4.2 批量完善应用
```http
POST /api/v1/applications/batch-complete
Content-Type: application/json

{
  "applications": [
    {
      "id": 123,
      "owner_id": 456,
      "git_repo_url": "https://github.com/company/web-frontend"
    },
    {
      "id": 124,
      "owner_id": 789,
      "git_repo_url": "https://github.com/company/api-backend"
    }
  ]
}
```

### 5. UI界面设计

#### 5.1 应用列表界面
```
应用管理页面：
┌─────────────────────────────────────────────────────────────────┐
│ 应用管理                    [+ 创建应用] [发现应用] [批量完善]    │
├─────────────────────────────────────────────────────────────────┤
│ 筛选：[全部] [完整应用] [待完善] [孤儿应用]                      │
├─────────────────────────────────────────────────────────────────┤
│ 应用名称     | 类型 | 状态 | Owner | Git仓库 | 集群部署 | 操作   │
│ ✅web-frontend | dep  | 完整 | alice | github  | ●●○     | 详情   │
│ ⚠️api-backend  | dep  | 待完善| -     | 未配置   | ●○○     | 完善   │
│ ❌redis        | dep  | 孤儿 | -     | -       | ○○○     | 删除   │
└─────────────────────────────────────────────────────────────────┘

图例：
✅ 完整应用（绿色）
⚠️ 待完善应用（橙色）
❌ 孤儿应用（红色）
●○○ 集群部署状态（实心表示已部署，空心表示未部署）
```

#### 5.2 应用详情界面
```
应用详情页面：
┌─────────────────────────────────────────────────────────────────┐
│ web-frontend                    [编辑] [部署] [CICD配置]         │
├─────────────────────────────────────────────────────────────────┤
│ Tab: [基本信息] [集群部署] [CICD配置] [监控告警] [操作日志]      │
├─────────────────────────────────────────────────────────────────┤
│ 基本信息：                                                      │
│   应用来源：✅ 手动创建                                         │
│   应用状态：✅ 完整应用                                         │
│   负责人：alice                                                 │
│   Git仓库：https://github.com/company/web-frontend             │
│   默认分支：main                                                │
│                                                                 │
│ 集群部署：                                                      │
│   ┌─ 生产集群 (Running) ─────────────────────────────────────┐  │
│   │ 副本数：3/3  镜像：web-frontend:v1.2.0                   │  │
│   │ 分支：main   最后部署：2024-01-15 10:30:00               │  │
│   │ [查看详情] [重新部署] [扩缩容]                            │  │
│   └───────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 6. 应用同步策略

#### 6.1 实时同步机制
- **基于项目命名空间**：只同步已创建项目对应命名空间下的工作负载
- **Informer 监听**：为每个集群启动Informer，实时监听工作负载变化
- **命名空间过滤**：启动时获取所有项目名称，只监听对应的命名空间
- **系统命名空间排除**：忽略kube-system、kube-public等系统命名空间
- **状态更新**：自动更新 ApplicationCluster 的运行时状态
- **事件通知**：通过 WebSocket 实时通知前端状态变化

#### 6.2 同步范围控制
```
同步范围 = 已创建项目的命名空间集合
例如：
- 项目: web-frontend → 监听命名空间: web-frontend
- 项目: api-backend → 监听命名空间: api-backend
- 项目: data-service → 监听命名空间: data-service

忽略的命名空间：
- kube-system, kube-public, kube-node-lease
- istio-system, monitoring, logging
- 其他非项目命名空间
```

#### 6.3 多集群同步
- **集群独立同步**：每个集群独立启动应用同步服务
- **项目命名空间一致性**：确保所有集群中项目命名空间的一致性
- **分布式锁**：使用Redis防止多副本重复处理同一事件

#### 6.4 数据一致性保障
- **定期校验**：定期检查 Application 与 K8s 工作负载的一致性
- **孤儿应用检测**：发现数据库中存在但 K8s 中不存在的应用
- **缺失应用发现**：发现 K8s 中存在但数据库中缺失的工作负载
- **自动协调**：提供数据协调接口，修复不一致状态

#### 6.5 冲突处理
- **版本控制**：使用 K8s ResourceVersion 处理并发更新
- **优先级策略**：K8s 实际状态优先于数据库记录
- **手动干预**：提供界面让用户处理冲突情况

## 集群元数据缓存机制

### 1. 混合架构设计（Informer + Redis）

考虑到系统多副本部署的需求，采用 **Informer + Redis** 的混合架构来实现集群元数据缓存。这种架构结合了 Informer 的实时性和 Redis 的分布式特性，确保多副本环境下的数据一致性和高可用性。

#### 1.1 架构优势
- **实时监听**：每个副本通过 Informer 实时监听 K8s 资源变化
- **统一缓存**：所有副本共享 Redis 中的资源状态数据
- **事件去重**：通过 Redis 分布式锁防止多副本重复处理事件
- **高可用性**：任意副本故障不影响整体服务可用性
- **数据一致性**：所有副本从 Redis 读取统一的资源状态

#### 1.2 整体架构图
```
                    K8s API Server
                           ↓ Watch API
    ┌─────────────────────────────────────────────────────────┐
    │                   KubeOps 服务集群                        │
    │                                                         │
    │  副本1              副本2              副本3              │
    │ ┌─────────┐       ┌─────────┐       ┌─────────┐         │
    │ │Informer │       │Informer │       │Informer │         │
    │ │ Cache   │       │ Cache   │       │ Cache   │         │
    │ └─────────┘       └─────────┘       └─────────┘         │
    │      ↓                 ↓                 ↓              │
    └──────┼─────────────────┼─────────────────┼──────────────┘
           │                 │                 │
           └─────────────────┼─────────────────┘
                             ↓
                    ┌─────────────────┐
                    │      Redis      │
                    │                 │
                    │ • 资源状态 KV    │
                    │ • 事件流 Stream  │
                    │ • 分布式锁       │
                    └─────────────────┘
                             ↑
                    ┌─────────┼─────────┐
                    │         │         │
              前端应用    API服务    其他消费者
```

### 2. 数据流转机制

#### 2.1 资源状态缓存（Redis KV）
- **键格式**：`kubeops:cluster:{cluster_id}:resource:{resource_type}:{namespace}:{name}`
- **值内容**：完整的 K8s 资源对象 JSON
- **过期策略**：根据资源类型设置不同的 TTL
- **更新机制**：Informer 事件触发时更新

#### 2.2 事件流处理（Redis Stream）
- **流名称**：`kubeops:events:cluster:{cluster_id}`
- **事件格式**：
  ```json
  {
    "event_id": "uuid",
    "event_type": "ADD|UPDATE|DELETE",
    "resource_type": "pod|service|deployment",
    "namespace": "web-project",
    "name": "web-frontend",
    "cluster_id": 1,
    "timestamp": "2024-01-15T10:30:00Z",
    "resource_version": "12345",
    "data": {...}  // 完整资源对象
  }
  ```

#### 2.3 分布式锁机制
- **锁键格式**：`kubeops:lock:event:{cluster_id}:{resource_type}:{namespace}:{name}`
- **锁超时**：30秒（防止死锁）
- **获取策略**：SETNX + EXPIRE
- **释放策略**：Lua 脚本确保原子性

### 3. 工作流程

#### 3.1 资源状态查询流程
```
1. 前端/API 请求资源列表
2. 查询 Redis KV 缓存
3. 如果缓存命中，直接返回
4. 如果缓存未命中，从 Informer 本地缓存获取
5. 更新 Redis 缓存
6. 返回结果
```

#### 3.2 资源变更事件处理流程
```
1. K8s 资源发生变化
2. 所有副本的 Informer 同时收到事件
3. 每个副本尝试获取分布式锁
4. 获得锁的副本处理事件：
   a. 更新 Redis KV 缓存
   b. 写入 Redis Stream 事件流
   c. 执行业务逻辑（如更新应用状态）
   d. 释放分布式锁
5. 其他副本获取锁失败，跳过处理
```

#### 3.3 事件消费流程
```
1. 异步消费者监听 Redis Stream
2. 处理各种事件类型：
   a. 更新数据库中的应用状态
   b. 发送 WebSocket 通知到前端
   c. 触发告警规则检查
   d. 记录审计日志
3. 确认消息处理完成
```

### 4. 缓存策略

#### 4.1 缓存键设计
```
# 资源状态缓存
kubeops:cluster:1:pods:default:web-frontend-xxx
kubeops:cluster:1:services:web-project:web-frontend
kubeops:cluster:1:deployments:web-project:web-frontend

# 列表缓存
kubeops:cluster:1:pods:list:default
kubeops:cluster:1:services:list:web-project

# 集群状态缓存
kubeops:cluster:1:info
kubeops:cluster:1:health
```

#### 4.2 TTL 策略
```yaml
cache_ttl:
  pods: 30s          # Pod 状态变化频繁
  services: 5m       # Service 相对稳定
  deployments: 2m    # Deployment 需要及时更新
  configmaps: 10m    # ConfigMap 变化较少
  nodes: 1m          # 节点状态重要
  events: 1m         # 事件需要及时展示
  cluster_info: 1h   # 集群信息基本不变
```

### 5. 高可用性保障

#### 5.1 故障处理
- **Informer 重连**：网络断开时自动重连和重新同步
- **Redis 故障**：降级到 Informer 本地缓存
- **分布式锁超时**：防止单点故障导致的死锁
- **事件重试**：失败事件自动重试处理

#### 5.2 数据一致性
- **最终一致性**：通过事件流确保最终数据一致
- **冲突解决**：使用资源版本号解决并发更新冲突
- **数据校验**：定期校验 Redis 缓存与 K8s 实际状态

#### 5.3 性能优化
- **批量操作**：批量更新 Redis 缓存减少网络开销
- **压缩存储**：大对象使用压缩算法存储
- **连接池**：Redis 连接池复用连接
- **异步处理**：事件处理异步化，不阻塞主流程

### 6. 监控和运维

#### 6.1 关键指标
- **缓存命中率**：监控 Redis 缓存命中率
- **事件处理延迟**：监控从 K8s 事件到处理完成的延迟
- **锁竞争情况**：监控分布式锁的竞争和等待时间
- **Informer 同步状态**：监控各副本 Informer 的同步状态

#### 6.2 告警规则
```yaml
alerts:
  - name: "cache_hit_rate_low"
    condition: "cache_hit_rate < 0.8"
    message: "Redis 缓存命中率过低"

  - name: "event_processing_lag"
    condition: "event_lag > 10s"
    message: "事件处理延迟过高"

  - name: "informer_sync_failed"
    condition: "informer_synced == false"
    message: "Informer 同步失败"

  - name: "redis_connection_failed"
    condition: "redis_connected == false"
    message: "Redis 连接失败"
```

#### 6.3 运维接口
- **缓存刷新**：`POST /api/v1/cache/refresh`
- **事件重放**：`POST /api/v1/events/replay`
- **健康检查**：`GET /api/v1/health/cache`
- **统计信息**：`GET /api/v1/stats/cache`

### 7. 配置示例

```yaml
cache:
  redis:
    host: "redis-cluster"
    port: 6379
    password: ""
    db: 0
    pool_size: 20

  informer:
    resync_period: "10m"
    worker_count: 5

  distributed_lock:
    timeout: "30s"
    retry_interval: "100ms"
    max_retries: 3

  event_stream:
    max_len: 10000
    consumer_group: "kubeops-processors"

clusters:
  - id: 1
    name: "prod-cluster"
    cache_enabled: true
    event_processing: true

  - id: 2
    name: "dev-cluster"
    cache_enabled: true
    event_processing: false  # 开发环境可关闭事件处理
```

## K8s权限同步完整示例（支持用户和用户组）

### 场景描述
1. 为用户组 `web-frontend-team` 分配生产集群中 `web-project` 项目下的Pod查看权限
2. 为用户 `zhangsan` 分配开发集群中 `test-project` 项目下的Deployment管理权限

### 1. 创建权限资源
```bash
# 用户组权限路径：cluster:prod-cluster:project:web-project:k8s:pods
# 用户权限路径：cluster:dev-cluster:project:test-project:k8s:deployments
```

### 2. 分配权限

#### 2.1 分配权限给用户组
```http
POST /api/v1/usergroups/1/permissions
{
  "permission_ids": [123],
  "sync_to_k8s": true
}
```

#### 2.2 分配权限给用户
```http
POST /api/v1/users/456/permissions
{
  "permission_ids": [124],
  "sync_to_k8s": true
}
```

### 3. 平台自动执行K8s权限同步

#### 3.1 解析权限路径
```
用户组权限路径：cluster:prod-cluster:project:web-project:k8s:pods
解析结果：
├── 授权对象：用户组 (web-frontend-team)
├── 目标集群：prod-cluster
├── 命名空间：web-project
├── 资源类型：pods
├── 操作权限：get, list, watch
└── Impersonate身份：kubeops:group:web-frontend-team

用户权限路径：cluster:dev-cluster:project:test-project:k8s:deployments
解析结果：
├── 授权对象：用户 (zhangsan)
├── 目标集群：dev-cluster
├── 命名空间：test-project
├── 资源类型：deployments
├── 操作权限：get, list, create, update
└── Impersonate身份：kubeops:user:zhangsan
```

#### 3.2 生成K8s RBAC资源

##### 用户组权限资源（prod-cluster）
```yaml
# Role for Group
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kubeops-group-web-frontend-team-web-project
  namespace: web-project
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "group"
    kubeops.io/subject-name: "web-frontend-team"
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
# RoleBinding for Group
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kubeops-group-web-frontend-team-web-project
  namespace: web-project
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "group"
    kubeops.io/subject-name: "web-frontend-team"
subjects:
- kind: Group
  name: "kubeops:group:web-frontend-team"
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: kubeops-group-web-frontend-team-web-project
  apiGroup: rbac.authorization.k8s.io
```

##### 用户权限资源（dev-cluster）
```yaml
# Role for User
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kubeops-user-zhangsan-test-project
  namespace: test-project
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "user"
    kubeops.io/subject-name: "zhangsan"
rules:
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "create", "update"]

---
# RoleBinding for User
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kubeops-user-zhangsan-test-project
  namespace: test-project
  labels:
    kubeops.io/managed: "true"
    kubeops.io/subject-type: "user"
    kubeops.io/subject-name: "zhangsan"
subjects:
- kind: User
  name: "kubeops:user:zhangsan"
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: kubeops-user-zhangsan-test-project
  apiGroup: rbac.authorization.k8s.io
```

#### 3.3 同步结果

##### 用户组权限同步结果
```json
{
  "code": 20000,
  "message": "用户组权限分配成功，K8s权限同步完成",
  "data": {
    "subject_type": "group",
    "subject_name": "web-frontend-team",
    "platform_permissions": [
      {
        "id": 123,
        "resource_type": "k8s",
        "resource_path": "cluster:prod-cluster:project:web-project:k8s:pods",
        "actions": ["get", "list", "watch"]
      }
    ],
    "k8s_sync_results": [
      {
        "cluster_name": "prod-cluster",
        "namespace": "web-project",
        "status": "success",
        "resources_created": [
          "Role/kubeops-group-web-frontend-team-web-project",
          "RoleBinding/kubeops-group-web-frontend-team-web-project"
        ],
        "impersonate_identity": "kubeops:group:web-frontend-team",
        "sync_time": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

##### 用户权限同步结果
```json
{
  "code": 20000,
  "message": "用户权限分配成功，K8s权限同步完成",
  "data": {
    "subject_type": "user",
    "subject_name": "zhangsan",
    "platform_permissions": [
      {
        "id": 124,
        "resource_type": "k8s",
        "resource_path": "cluster:dev-cluster:project:test-project:k8s:deployments",
        "actions": ["get", "list", "create", "update"]
      }
    ],
    "k8s_sync_results": [
      {
        "cluster_name": "dev-cluster",
        "namespace": "test-project",
        "status": "success",
        "resources_created": [
          "Role/kubeops-user-zhangsan-test-project",
          "RoleBinding/kubeops-user-zhangsan-test-project"
        ],
        "impersonate_identity": "kubeops:user:zhangsan",
        "sync_time": "2024-01-15T11:00:00Z"
      }
    ]
  }
}
```

### 4. WebShell身份选择和权限验证

#### 4.1 WebShell身份选择
```http
# 用户张三访问WebShell，可选择身份
GET /api/v1/webshell/identities?cluster_name=prod-cluster

# 响应：用户可选择以下身份
{
  "user_identity": {
    "type": "user",
    "name": "zhangsan",
    "impersonate_identity": "kubeops:user:zhangsan",
    "permissions": ["dev-cluster:test-project:deployments"]
  },
  "group_identities": [
    {
      "type": "group",
      "name": "web-frontend-team",
      "impersonate_identity": "kubeops:group:web-frontend-team",
      "permissions": ["prod-cluster:web-project:pods"]
    }
  ]
}
```

#### 4.2 WebShell连接
```http
# 用户选择以用户组身份连接
POST /api/v1/webshell/connect
{
  "cluster_name": "prod-cluster",
  "namespace": "web-project",
  "identity_type": "group",
  "identity_name": "web-frontend-team"
}
```

#### 4.3 平台层权限验证
```bash
# 用户访问平台API时，Casbin验证权限
# 检查用户是否有对应的权限路径访问权限
```

#### 4.4 K8s层权限验证（使用Impersonate）
```bash
# WebShell使用Impersonate特性访问K8s资源

# 用户组身份访问
kubectl get pods -n web-project --as=kubeops:group:web-frontend-team

# 用户身份访问
kubectl get deployments -n test-project --as=kubeops:user:zhangsan

# 平台后端使用高权限kubeconfig + impersonate实现
# 无需为每个用户/用户组创建独立的kubeconfig
```

### 5. 权限管理

#### 5.1 查看同步状态
```http
# 查看用户组同步状态
GET /api/v1/rbac/k8s-sync/status?subject_type=group&subject_id=1&cluster_name=prod-cluster

# 查看用户同步状态
GET /api/v1/rbac/k8s-sync/status?subject_type=user&subject_id=456&cluster_name=dev-cluster
```

#### 5.2 权限撤销
```http
# 撤销用户组权限
DELETE /api/v1/usergroups/1/permissions
{
  "permission_ids": [123],
  "cleanup_k8s": true  // 同时清理K8s权限资源
}

# 撤销用户权限
DELETE /api/v1/users/456/permissions
{
  "permission_ids": [124],
  "cleanup_k8s": true
}
```

#### 5.3 手动重新同步
```http
# 重新同步用户组权限
POST /api/v1/rbac/k8s-sync/retry
{
  "subject_type": "group",
  "subject_id": 1,
  "cluster_names": ["prod-cluster"],
  "force_recreate": false
}

# 重新同步用户权限
POST /api/v1/rbac/k8s-sync/retry
{
  "subject_type": "user",
  "subject_id": 456,
  "cluster_names": ["dev-cluster"],
  "force_recreate": false
}
```

## 总结

通过K8s Impersonate特性和双重权限控制机制，KubeOps实现了：

1. **统一身份管理**：使用一个高权限kubeconfig + impersonate，无需管理多个证书
2. **灵活权限分配**：支持用户和用户组两种授权方式
3. **WebShell身份选择**：用户可以选择以个人身份或用户组身份访问K8s
4. **双重权限保护**：平台层Casbin权限 + K8s层RBAC权限
5. **权限一致性**：自动同步平台权限到K8s集群，确保权限一致性

这种设计既保证了安全性，又提供了极大的灵活性和易用性。