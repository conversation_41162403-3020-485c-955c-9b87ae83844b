# KubeOps - Kubernetes多集群管理平台

<div align="center">

![KubeOps Logo](https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=KubeOps)

**企业级Kubernetes多集群管理和运维平台**

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/your-org/kubeops)

[快速开始](#快速开始) • [功能特性](#核心功能) • [文档](#文档) • [演示](#演示)

</div>

## 📋 目录

- [项目概述](#项目概述)
- [核心功能](#核心功能)
- [快速开始](#快速开始)
- [架构设计](#架构设计)
- [权限模型](#权限模型)
- [配置说明](#配置说明)
- [文档](#文档)
- [贡献指南](#贡献指南)

## 🚀 项目概述

KubeOps是一个现代化的Kubernetes多集群管理平台，专为企业级环境设计。它提供了统一的界面来管理多个Kubernetes集群，支持细粒度的权限控制、审批流程和操作审计。

### 🎯 设计目标

- **统一管理**：一个平台管理多个Kubernetes集群
- **安全可控**：基于RBAC的细粒度权限控制，支持K8s权限同步
- **流程规范**：内置审批流程，确保操作合规性
- **用户友好**：直观的Web界面和kubectl WebShell
- **企业级**：支持OIDC认证、操作审计、高可用部署

## ✨ 核心功能

### 🔧 集群管理
- **多集群接入**：支持通过kubeconfig或ServiceAccount接入多个集群
- **集群监控**：实时监控集群状态、资源使用情况
- **版本管理**：支持不同版本的Kubernetes集群

### 🛡️ 权限控制
- **双重权限保护**：平台层权限控制 + K8s RBAC权限同步
- **灵活授权**：支持用户和用户组两种授权方式
- **K8s Impersonate**：使用impersonate特性实现安全的身份切换
- **WebShell身份选择**：用户可选择不同身份访问K8s资源

### 👥 用户管理
- **OIDC集成**：支持Keycloak等OIDC提供商
- **用户组管理**：灵活的用户组织架构
- **权限继承**：用户可继承用户组权限

### 📋 审批流程
- **操作审批**：敏感操作需要审批
- **流程配置**：可配置的审批流程
- **审批记录**：完整的审批历史

### 🔍 审计监控
- **操作审计**：记录所有用户操作
- **资源变更**：跟踪K8s资源变更
- **安全事件**：监控异常操作

### 🖥️ WebShell
- **kubectl访问**：浏览器中的kubectl终端
- **身份切换**：支持用户身份和用户组身份
- **权限隔离**：基于用户权限的资源访问控制

## 🚀 快速开始

### 前置要求

- Go 1.21+
- PostgreSQL 12+
- Redis 6+
- Kubernetes 1.20+

### 安装部署

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/kubeops.git
cd kubeops
```

#### 2. 配置环境
```bash
# 复制配置文件
cp config/config.example.yaml config/config.yaml

# 编辑配置文件
vim config/config.yaml
```

#### 3. 初始化数据库
```bash
# 创建数据库
createdb kubeops

# 运行迁移
go run cmd/migrate/main.go
```

#### 4. 启动服务
```bash
# 开发环境
go run cmd/main/main.go

# 生产环境
make build
./bin/kubeops
```

#### 5. 访问平台
- Web界面：http://localhost:8080
- API文档：http://localhost:8080/swagger/index.html

### Docker部署

```bash
# 使用docker-compose
docker-compose up -d

# 或使用Kubernetes
kubectl apply -f deploy/kubernetes/
```

## 🏗️ 架构设计

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Mobile App    │    │   kubectl CLI   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │     KubeOps Platform      │
                    │  ┌─────────────────────┐  │
                    │  │   Authentication    │  │
                    │  │   Authorization     │  │
                    │  │   Audit & Logging   │  │
                    │  │   Workflow Engine   │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
          ┌───────────────────────┼───────────────────────┐
          │                       │                       │
┌─────────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
│  K8s Cluster A  │    │  K8s Cluster B  │    │  K8s Cluster C  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈

- **后端**：Go + Gin + GORM + Casbin
- **数据库**：PostgreSQL + Redis
- **认证**：OIDC (Keycloak)
- **权限**：Casbin + K8s RBAC
- **前端**：Vue.js + Element UI
- **部署**：Docker + Kubernetes

## 🔐 权限模型

### 权限架构

KubeOps采用双重权限控制机制：

1. **平台层权限**：基于Casbin的API访问控制
2. **K8s层权限**：自动同步到K8s集群的RBAC权限

### 权限路径格式

```
cluster:{cluster_name}:project:{project_name}:application:{app_name}:{resource_type}:{resource_name}
```

### 授权方式

- **用户授权**：直接为用户分配权限
- **用户组授权**：通过用户组继承权限
- **K8s权限同步**：自动同步平台权限到K8s集群

### WebShell身份选择

用户可以选择不同身份访问K8s资源：
- **个人身份**：`kubeops:user:{username}`
- **用户组身份**：`kubeops:group:{group_name}`

## ⚙️ 配置说明

### 基础配置

```yaml
# config/config.yaml
server:
  port: 8080
  mode: release

database:
  host: localhost
  port: 5432
  name: kubeops
  username: kubeops
  password: password

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

auth:
  oidc:
    issuer_url: "https://keycloak.example.com/realms/kubeops"
    client_id: "kubeops"
    client_secret: "your-client-secret"
```

### 集群配置

```yaml
clusters:
  - name: "prod-cluster"
    api_server: "https://k8s-api.example.com"
    kubeconfig_path: "/etc/kubeops/kubeconfig/prod"
    
  - name: "dev-cluster"
    api_server: "https://k8s-dev.example.com"
    kubeconfig_path: "/etc/kubeops/kubeconfig/dev"
```

## 📚 文档

- [📖 详细文档](docs/README.md)
- [🔧 API文档](docs/api/README.md)
- [🛡️ 权限管理](docs/rbac/README.md)
- [🚀 部署指南](docs/deployment/README.md)
- [🔍 故障排除](docs/troubleshooting/README.md)

## 🤝 贡献指南

我们欢迎社区贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发环境设置

```bash
# 安装依赖
go mod download

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make lint
```

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢所有为KubeOps项目做出贡献的开发者和用户。

---

<div align="center">
Made with ❤️ by the KubeOps Team
</div>
