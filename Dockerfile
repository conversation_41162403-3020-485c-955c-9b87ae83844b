# 第一阶段：构建应用
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装依赖
RUN apk add --no-cache git make gcc libc-dev

# 复制go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o kubeops cmd/main/main.go

# 第二阶段：创建最小运行镜像
FROM alpine:3.19

# 安装基础工具
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN adduser -D -g '' kubeops

# 设置工作目录
WORKDIR /app

# 复制配置文件
COPY --from=builder /app/configs /app/configs

# 从构建阶段复制可执行文件
COPY --from=builder /app/kubeops /app/kubeops

# 更改所有权
RUN chown -R kubeops:kubeops /app

# 切换到非root用户
USER kubeops

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV KUBEOPS_CONFIG_PATH=/app/configs/config.yaml

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD wget -q -O- http://localhost:8080/health || exit 1

# 运行应用
ENTRYPOINT ["/app/kubeops"] 